menu.title_platform: 'Campus formativo'
complete: Completado
Show: Ver
Edit: Modificar
Remove: Quitar
Delete: Eliminar
total: Total
'Yes': Sí
'No': 'No'
Actions: Acciones
Clear: Limpiar
'No results found': 'No se han encontrado resultados'
Configuration: Configuración
Limit: Límite
Close: Cerrar
Save: Guardar
'Save and create other': 'Crear y añadir otro'
'Save changes': 'Guardar cambios'
'Save and keep editing': 'Guardar y seguir editando'
state: Estado
create: Crear
cancelar: Cancelar
back: Regresar
add: Añadir
no_content: 'No hay contenido'
no_result: 'No se encontrarón resultados'
configure_simulator: 'Configurar simulador'
edit_configure_simulator: 'Editar configuración'
configure_success: 'La configuración se realizó correctamente'
save_success: 'Registro guardado con éxito'
error_success: 'Ocurrió un error al guardar el registro'
configure_completed: 'Configuración completada'
'Created At': <PERSON>reado
'Created By': 'Creado por'
'Created by': 'Creado por'
'Updated At': Actualizado
'Updated By': 'Actualizado por'
'Deleted By': 'Eliminado por'
'Deleted At': Eliminado
menu.courses_managment.title: 'Gestión de cursos'
menu.courses_managment.Segments: Segmentos
menu.courses_managment.categories: Categorías
menu.courses_managment.level: Niveles
menu.courses_managment.courses: Cursos
menu.courses_managment.announcements: Convocatoria
menu.courses_managment.nps_question: 'Preguntas NPS'
menu.courses_managment.opinion_course: 'Opiniones curso'
menu.help_managment.title: 'Gestión de ayuda'
menu.help_managment.content_help: 'Contenido ayuda'
menu.help_managment.categories_help: 'Categoría ayuda'
menu.users_managment.title: 'Gestión de usuarios'
menu.users_managment.users: Usuarios
menu.users_managment.managers: Managers
menu.users_managment.filter: Filtros
menu.news.title: Noticias
menu.stats.title: Estadísticas
menu.stats.export: 'Herramienta excel'
menu.users.edit_profile: 'Editar perfil'
form.label.delete: Eliminar
action.save: 'Guardar ahora'
common_areas.created_at: Creado
common_areas.updated_at: Actualizado
common_areas.deleted_at: Actualizado
common_areas.created_by: 'Creado por'
common_areas.updated_by: 'Actualizado por'
common_areas.actions: Acciones
common_areas.basic_information: 'Información básica'
common_areas.edit: Editar
common_areas.delete: Eliminar
common_areas.name: Nombre
common_areas.image: Imagen
common_areas.state: Estado
common_areas.create: Crear
common_areas.save: Guardar
common_areas.back_list: 'Volver al listado'
course_category.label_in_singular: 'Categoría curso'
course_category.label_in_plural: 'Categorías curso'
course_category.configureFields.category_name: 'Nombre categoría'
course_category.configureFields.category_order: 'Orden categoría'
course_category.configureFields.translations: Traducciones
course.label_in_singular: Curso
course.label_in_plural: Cursos
course.back_to_course: 'Volver al curso'
course.configureFields.basic_information: 'Información básica'
course.configureFields.code: Código
course.configureFields.name: Nombre
course.configureFields.description: Descripción
course.configureFields.basic: Básico
course.configureFields.access_level: 'Nivel accesso'
course.configureFields.clone: Clonar
course.configureFields.open: Abierto
course.configureFields.open_visible: 'Visible en campus abierto'
course.configureFields.active: Publicado
course.configureFields.categories: Categorias
course.configureFields.profesional_categories: 'Categorias profesionales'
course.configureFields.image: Imagen
course.configureFields.chapter: Capítulo
course.configureFields.translation: Traducción
course.configureFields.general_information: 'Información general'
course.configureFields.segment: Segmento
course.configureFields.category: Categoría
course.configureFields.thumbnail_url: 'Url miniatura'
course.configureFields.locale: Idioma
course.configureFields.all_seasons: 'Todas las temporadas'
course.configureFields.chapters: Capítulos
course.configureFields.seasons: Temporadas
course.configureFields.courses_translate: Traducciones
course.configureFields.add_chapter: 'Añadir capítulo'
course.configureFields.no_seasons: 'Sin temporada'
course.configureFields.add_seasons: 'Añadir temporada'
course.configureFields.add_annuncement: 'Añadir convocatoria'
course.configureFields.question_modal_translate: '¿Realmente quieres traducir este curso?'
course.configureFields.content_modal_translate: 'Esta acción creará una copia del curso para utilizarla como guía para la traducción a otro idioma.'
course.configureFields.translate_already: 'Este curso ya tiene una traducción en este idioma'
course.configureFields.tag_description: 'Separa las etiquetas pulsando enter'
course.configureFields.new: Nuevo
course.configureFields.add_material: 'Añadir material'
course.configureFields.add_task: 'Añadir tarea'
course.configureFields.task: Tareas
course.season_add: 'La temporada fue añadida correctamente'
course.season_update: 'La temporada fue actualizada correctamente'
course.season_add_error: 'Ocurrió un error al añadir la temporada'
course.panel.class: 'Datos del curso'
chapter.label_in_plural: Capítulos
chapter.configureFields.title: Título
chapter.configureFields.course: Curso
chapter.configureFields.type: Tipo
chapter.configureFields.season: Temporada
chapter.configureFields.description: Descripción
chapter.configureFields.image: Imagen
chapter.configureFields.image_file: 'Archivo imagen'
chapter_type.description.1: '<p>El capítulo Scorm es muy interesante:</p><p>Nos permite cargar contenidos muy diversos generados con otras herramientas, por ejemplo documentos, contenidos interactivos e incluso juegos.</p>'
chapter_type.description.2: '<p>Se trata de uno de los capítulos más versátiles.</p><p>A la izquierda se muestran los títulos introducidos, que sirven como índice para encontrar contenidos rápidamente y facilitar la lectura.</p>'
chapter_type.description.3: '<p>Es un juego de preguntas que añade un componente de azar, ya que hay que completar los segmentos de una ruleta para superarlo.</p><p>Se basa en la creación de una batería de preguntas para reforzarlos conocimientos aprendidos. Se pueden introducir tantas preguntas como quieras y además acompañarlas con imágenes.</p><p>Para el funcionamiento idóneo del juego es recomendable incluir un mínimo de 10 preguntas.</p> '
chapter_type.description.4: '<p>Este juego presenta una serie de preguntas que incluyen un factor de riesgo adicional. Después de cada pregunta, los participantes tienen la opción de plantarse y quedarse con la puntuación actual, o arriesgarse a responder una pregunta adicional para obtener más puntos. Sin embargo, si se responde incorrectamente, se pierden todos los puntos acumulados hasta ese momento.</p>'
chapter_type.description.5: '<p>Es el capítulo de juego más clásico.</p><p>La idea consiste en crear una batería de preguntas para reforzar los conocimientos aprendidos. Se pueden introducir preguntas ilimitadas acompañadas de imagen y con una única respuesta correcta.</p>'
chapter_type.description.6: '<p>Ordena y gira las piezas hasta colocarlas en su posición y orientación correspondiente, ya que en caso contrario no se unirán entre sí.</p><p>En la parte superior existen cuatro segmentos que se corresponden con el tiempo disponible para completar el puzle. Cuando se agota un segmento de tiempo se formula una de las preguntas que hemos introducido. Acertando las preguntas se obtiene más tiempo para resolver el puzzle. La puntuación final dependerá de una combinación entre el tiempo empleado en completar el puzle en sí, número de preguntas correctas respondidas y el número de preguntas falladas.</p>'
chapter_type.description.7: '<p>Este juego plantea un acertijo basado en una imagen. Para resolverlo hay que seleccionar las letras adecuadas de forma cuidadosa antes de que se agote el tiempo.</p>'
chapter_type.description.8: '<p>Dado que el formato PDF está muy extendido en contenidos de diversa índole, como protocolos o manuales, los capítulos de tipo PDF son muy interesantes, ya que nos permiten reutilizar materiales ya editados.</p>'
chapter_type.description.9: '<p>Los recursos audiovisuales tienen un gran potencial pedagógico atraen, captan la atención y despierta la curiosidad.</p><p>La plataforma nos permite elegir como introducir el vídeo via "url", o seleccionado un archivo que este en nuestro ordenador. En este ultimo caso podemos adjuntar un archivo de subtitulos.</p>'
chapter_type.description.10: '<p>Capítulo de tipo slider con imágenes.</p>'
chapter_type.description.11: '<p>El juego consiste en acertar las palabras, cada una de las cuales se corresponde con una letra de la rueda. A veces la solución será una palabra que empieza por la letra y otras simplemente contendrá la letra.</p>'
chapter_type.description.12: '<p>En este juego se irán sucediendo una serie de preguntas en formato texto, imagen o una combinación de ambas planteadas como afirmaciones. Existen dos posibles respuestas “Verdadero” o Falso” y únicamente una es la correcta. El tiempo para resolver el juego es limitado.</p>'
chapter_type.description.13: '<p>En este juego, se tendrá que resolver una adivinanza antes de que el tiempo se acabe. La pista estará oculta detrás de una imagen borrosa que se irá enfocando gradualmente a medida que se avanza en el juego. Además de la imagen, también existe una ayuda adicional en forma de texto.</p>'
chapter_type.description.14: '<p>En este juego clásico, se tendrán que ordenar los elementos arrastrando los bloques hasta colocarlos en el orden correcto. La diversidad de posibilidades lo hace ideal para ejercicios matemáticos y otros retos educativos. Ideal para crear una prueba que desafíe las habilidades de razonamiento y orden.</p>'
chapter_type.description.15: '<p>Este juego es ideal para entrenar la memoria y la concentración. El objetivo es encontrar todas las parejas de cartas iguales. La ubicación de las cartas se crea de forma aleatoria, por lo que cada partida será diferente.</p>'
chapter_type.description.16: '<p>En este juego se presentarán una serie de palabras, frases o conceptos que se deberán asociar con la familia o grupo correspondiente que se muestra más abajo. Se pondrán a prueba las habilidades de asociación y rapidez mental mientras se compite contra el reloj.</p>'
chapter_type.description.17: '<p>En este juego de gramática y aprendizaje, el objetivo es rellenar los huecos de las oraciones con las palabras adecuadas para poner a prueba las habilidades lingüísticas y gramaticales. ¡Pero eso no es todo! Este juego es versátil y puede ser utilizado para muchos otros fines didácticos.</p>'
chapter_type.description.18: '<p>En este juego, se presentará una pregunta o un acertijo para resolver. La tarea consistirá en examinar cuidadosamente el enigma y utilizar las letras proporcionadas para averiguar la palabra correcta. Pero cuidado, porque el tiempo es limitado, lo que significa que habrá que ser rápido y preciso para poder ganar.</p>'
chapter_type.description.19: '<p>En este juego se tendrá que adivinar una palabra oculta en un máximo de seis intentos. Cada intento consistirá en ingresar una palabra válida, y después de cada intento, el color de las casillas cambiará para mostrar que letras son correctas y cuales están además en la posición correcta.</p>'
chapter_type.description.20: '<p>Este juego consiste en encontrar palabras escondidas en una sopa de letras. El objetivo es marcar una secuencia de letras en horizontal, vertical o diagonal. Se pueden encontrar palabras en ambas direcciones, de izquierda a derecha o de derecha a izquierda. Si la secuencia forma parte de una palabra oculta, se considerará como una respuesta correcta.</p>'
chapter_type.description.21: '<p>Durante la reproducción de un vídeo, se insertan preguntas interactivas que requieren que el espectador preste atención al contenido del vídeo para poder responder correctamente. En definitiva, combinar el poder del vídeo con la interactividad del cuestionario para ofrecer una experiencia de aprendizaje efectiva y atractiva.</p>'
chapter_type.add.1: 'Añadir Scorm'
chapter_type.add.2: 'Añadir contenido'
chapter_type.add.3: 'Crear juego'
chapter_type.add.4: 'Crear juego'
chapter_type.add.5: 'Crear quiz'
chapter_type.add.6: 'Crear juego'
chapter_type.add.7: 'Crear juego'
chapter_type.add.8: 'Añadir PDF'
chapter_type.add.9: 'Añadir vídeo'
chapter_type.add.10: 'Añadir slider'
chapter_type.add.11: 'Crear juego'
chapter_type.add.12: 'Crear juego'
chapter_type.add.13: 'Crear juego'
chapter_type.add.14: 'Crear juego'
chapter_type.add.15: 'Crear juego'
chapter_type.add.16: 'Crear juego'
chapter_type.add.17: 'Crear juego'
chapter_type.add.18: 'Crear juego'
chapter_type.add.19: 'Crear juego'
chapter_type.add.20: 'Crear juego'
chapter_type.add.21: 'Crear vídeo quiz'
chapter_type.all: Todos
chapter_type.content: Teoría
chapter_type.games_test: Evaluación
chapter_type.description_test: 'Descripción de la prueba'
chapter_type.type: 'Tipo de capítulo'
chapter.add_pdf: 'Añadir pdf'
chapter.chapter.show_video: 'Ver vídeo'
chapter.message_pdf_success: 'El PDF ha sido añadido correctamente'
chapter.message_pdf_error: 'Ocurrió un error al guardar el PDF'
chapter.chapter.materials: Materiales
chapter.chapter.show_pdf: 'Ver PDF'
announcements.label_in_singular: Convocatoria
announcements.label_in_plural: Convocatorias
announcements.configureFields.courses: Cursos
announcements.configureFields.start_at: Empieza
announcements.configureFields.finish_at: Finaliza
announcements.configureFields.called: llamado
announcements.configureFields.subsidized: Subvencionado
announcements.configureFields.subsidizer: Inspector
announcements.configureFields.subsidizer_entity: 'Entidad subvencionadora'
announcements.configureFields.subsidized_announcement: 'Convocatoria de subvención'
announcements.configureFields.max_users: 'Usuarios máximos'
announcements.configureFields.formative_action_type: 'Tipo de acción formativa'
announcements.configureFields.format: Formato
announcements.configureFields.total_hours: 'Total horas'
announcements.configureFields.place: Lugar
announcements.configureFields.training_center: 'Centro de formación'
announcements.configureFields.training_center_address: 'Dirección del centro de formación'
announcements.configureFields.training_center_nif: 'Centro de formación NIF'
announcements.configureFields.training_center_phone: 'Teléfono del centro de formación'
announcements.configureFields.training_center_email: 'Correo electrónico del centro de formación'
announcements.configureFields.training_center_teacher_dni: 'Centro de formación de profesores DNI'
announcements.configureFields.called_user: 'Personas convocadas'
announcements.configureFields.search: Buscar
announcements.configureFields.announcement_for: 'Convocatoria para'
announcements.configureFields.search_user_title: 'Buscar personas para convocar'
announcements.configureFields.placeholder_search_user: 'Buscar personas'
announcements.configureFields.placeholder_search_category: 'Buscar por categoría'
announcements.configureFields.placeholder_search_department: 'Buscar por departamento'
announcements.configureFields.placeholder_search_center: 'Buscar por centro'
announcements.configureFields.placeholder_search_country: 'Buscar por país'
announcements.configureFields.placeholder_search_division: 'Buscar por división'
announcements.configureFields.result_found: 'Resultados encontrados'
announcements.configureFields.clear_result: 'Limpiar resultados'
announcements.configureFields.error_already_called_user: 'Error: ¡La persona ya ha sido convocada!'
announcements.configureFields.error_already_called_user_date: 'Error: ¡La persona ya ha sido convocada en un rango de fecha similar!'
announcements.configureFields.notified: Notificado
announcements.configureFields.content_course: 'Contenido curso'
announcements.configureFields.report: Informe
announcements.configureFields.title_report: 'Informe de alumnos'
announcements.configureFields.direction: Dirección
announcements.configureFields.telephone: Teléfono
announcements.configureFields.nif: NIF
announcements.configureFields.tutor: Tutor/a
announcements.configureFields.apt: Apto
announcements.configureFields.time_total: 'Tiempo total'
question.label_in_singular: Pregunta
question.label_in_plural: Preguntas
question.configureFields.question: Pregunta
question.configureFields.random: Aleatorio
question.configureFields.answers: Respuestas
question.configureFields.image_file: 'Archivo imagen'
question.configureFields.question_for: 'Preguntas para'
question.configureFields.image_for: 'Imagen para'
question.configureFields.add_image_puzzle: 'Añadir imagen puzle'
question.configureFields.add_question: 'Crear pregunta'
question.configureFields.see_image: 'Ver imagen'
content.label_in_singular: Contenido
content.label_in_plural: Contenidos
content.configureFields.title: Titulo
content.configureFields.content: Contenido
content.configureFields.position: Posición
content.configureFields.add_content: 'Añadir contenido'
content.configureFields.content_for: 'Contenido para'
question_nps.label_in_singular: 'Pregunta NPS'
question_nps.label_in_plural: 'Preguntas NPS'
question_nps.configureFields.type: Tipo
question_nps.configureFields.position: Posición
question_nps.configureFields.question: Pregunta
question_nps.configureFields.course: Curso
question_nps.configureFields.name_question: 'Nombre pregunta'
question_nps.configureFields.translations: Traducciones
opinions.label_in_singular: Opinión
opinions.label_in_plural: Opiniones
opinions.configureFields.course: Curso
opinions.configureFields.question: Pregunta
opinions.configureFields.to_post: Publicar
opinions.configureFields.value: Valor
opinions.configureFields.valoration: Valoración
help_category.label_in_singular: 'Categoría ayuda'
help_category.label_in_plural: 'Categorías ayuda'
help_category.configureFields.category_name: 'Nombre categoría'
help_category.configureFields.translations: Traducciones
help_text_content.label_in_singular: 'Contenido ayuda'
help_text_content.label_in_plural: 'Contenidos ayuda'
help_text_content.configureFields.category: Categoría
help_text_content.configureFields.title: Título
help_text_content.configureFields.text: Texto
help_text_content.configureFields.translations: Traducciones
user.label_in_singular: Usuario
user.label_in_plural: Usuarios
user.configureFields.division: División
user.configureFields.country: País
user.configureFields.category: Categoría
user.configureFields.departament: Departamento
user.configureFields.center: Centro
user.configureFields.gender: Género
user.configureFields.first_name: Nombre
user.configureFields.last_name: Apellido
user.configureFields.code: Código
user.configureFields.password: Contraseña
user.configureFields.change_password: 'Cambiar contraseña'
user.configureFields.courses: Cursos
user.configureFields.extra: Extra
user.configureFields.announcements: Convocatorias
user.configureFields.extra_fields: 'Campos adicionales'
user.configureFields.avatar_image: 'Imagen perfil'
user.configureFields.new_password: 'Nueva contraseña'
user.configureFields.birthdate: 'Fecha de nacimiento'
user.configureFields.edit_user: 'Editar usuario'
user.configureFields.user_data: 'Datos del usuario'
user.configureFields.stats: Estadísticas
user.configureFields.chapter: Capítulos
user.configureFields.ratio_course: 'Ratio Cursos/Personas'
user.configureFields.avg_stars: 'Media de estrellas'
user.configureFields.time: Tiempo
user.configureFields.chapter_time: 'Tiempo invertido por capítulo'
user.configureFields.available: Disponible
user.configureFields.messages: Mensajes
user.configureFields.login_history: 'Historial de inicio de sesión'
user.configureFields.started_at: Inicia
user.configureFields.finished_at: Finaliza
user.configureFields.time_spent: 'Tiempo empleado'
user.configureFields.content_viewed: 'Contenidos vistos'
user.configureFields.interaction_with_teacher: 'Interacciones con el profesor'
user.configureFields.course_content: 'Contenido del curso'
user.configureFields.content_type: 'Tipo de contenido'
user.configureFields.finished: Finalizado
user.configureFields.teacher_interaction: 'Interacciones con los profesores'
user.configureFields.date: Fecha
user.configureFields.sender: Remitente
user.configureFields.recipient: Destinatario
user.configureFields.subject: Asunto
user.configureFields.questions: Preguntas
user.configureFields.chapter_type: 'Tipo de capítulos'
user.configureFields.finished_chapter_types: 'Tipos de capítulos terminados'
user.configureFields.button_validate: Validar
user.configureFields.open: 'Campus abierto'
user.configureFields.computer: Ordenador
user.configureFields.mobile: Móvil
user.configureFields.tablet: Tablet
user.manage.assign_data: 'Asignar datos'
user.gender.m: Masculino
user.gender.f: Femenino
user.configureFields.time_title: 'Dedicación por día'
user.configureFields.interaction_in_forum: 'Interacciones en foros'
user.configureFields.email: 'Correo electrónico'
user.configureFields.fullname: 'Nombre y apellidos'
user.filtersRequired: 'Selecciona al menos un filtro'
stats.general_stats: 'Estadísticas generales'
stats.total_times_spent: 'Tiempo total empleado'
stats.users_activity: 'Actividad de usuarios'
stats.users_active_last_30: 'Activos últimos 30 días'
stats.users_inactive_last_30: 'Sin actividad últimos 30 días'
stats.users_never_login: 'Nunca entraron'
stats.daily_chapter: 'Capítulos diarios terminados'
stats.finished_chapters: 'Capítulos terminados'
stats.daily_course: 'Cursos diarios terminados'
stats.finished_courses: 'Cursos terminados'
stats.daily_login: 'Inicio de sesión diario'
stats.daily_login_tooltip: 'Inicios de sesión'
stats.all_courses: 'Todos los cursos'
stats.all_countries: 'Todos los países'
stats.all_centers: 'Todos los centros'
stats.all_categories: 'Todas las categorías'
stats.all_departament: 'Todos los departamentos'
stats.all_gender: 'Todos los géneros'
stats.all_divisions: 'Todas las divisiones'
stats.filters: Filtros
stats.filter_by: 'Filtrar por'
stats.modal_close: Cerrar
stats.clear_filters: 'Limpiar filtros'
stats.apply_filters: 'Aplicar filtros'
stats.export_title: 'Exportar datos'
stats.export.start_date: 'Fecha inicio'
stats.export.end_date: 'Fecha final'
stats.export.filename: 'Nombre del archivo'
stats.export.request_date: 'Fecha de solicitud'
stats.export.available_until: 'Disponible hasta'
stats.export.loading_data: 'Cargando datos'
stats.export.no_data: 'No hay datos disponibles'
stats.export.download_file: 'Descargar archivo'
stats.export.abort_export_request: 'Anular solicitud de exportación'
stats.export.view_details: 'Ver detalles'
stats.export.reset_form: 'Resetear campos'
stats.export.error_start_date: 'La fecha de inicio no puede ser posterior a la fecha de finalización.'
stats.export.export_error: 'Se ha producido un error al generar el informe'
stats.export.export_success: 'El informe se ha añadido correctamente a la cola de descarga.'
stats.export.export_dir: 'Estadísticas / herramienta excel'
stats.devices_login: 'Inicio de sesión en dispositivos'
stats.distribution_ages: 'Distribución de edades'
stats.generation_babyboom: Babyboom
stats.generation_x: 'Generación X'
stats.generacion_milenials: Millennials
stats.generacion_z: 'Generación Z'
stats.title_information_user: 'Información de las personas'
stats.title_information_content: 'Información sobre los contenidos'
stats.title_information_courses: 'Información de los cursos'
stats.title_information_chapter: 'Información de los capítulos'
stats.distribution_country: 'Distribución por paises'
stats.title_finish_m: terminados
stats.title_made: realizados
stats.title_made_f: realizadas
stats.chapter_day: días
stats.chaper_hours: horas
stats.chapter_minutes: minutos
stats.chapter_total: TOTAL
stats.chapter_media: MEDIA
stats.content_active: Activos
stats.content_active_f: Activas
stats.totalLogin: Totales
stats.access: Accesos
stats.uniqueLogin: Únicos
stats.at_least_one_course_finished: 'Personas formadas'
stats.top_rated_courses: 'Cursos mejor valorados'
stats.lowest_rated_courses: 'Cursos menos valorados'
stats.most_completed_courses: 'Cursos más completados'
stats.users_more_actives: 'Personas más activas'
stats.users_less_actives: 'Personas menos activas'
stats.accumulative.title: 'Evolutivo y acumulado'
stats.accumulative.trained: 'Personas únicas formadas'
stats.accumulative.new: Nuevos
stats.accumulative.accumulated: Acumulados
stats.accumulative.chart: Gráfica
stats.accumulative.logins: 'Inicios de sesión'
stats.accumulative.courses: Cursos
stats.accumulative.courses_started: 'Cursos iniciados'
stats.accumulative.courses_finished: 'Cursos finalizados'
stats.accumulative.ratings: Valoraciones
stats.accumulative.time: 'Tiempo invertido (en horas)'
stats.accumulative.filters: 'Distribución por filtros'
'stats. daily_posts': 'Mensajes diarios en foros'
stats.most_active_threads: 'Hilos más activos'
stats.most_active_users: 'Personas más activas'
stats.forum_post_messages_count: Mensajes
stats.forum_post_title: Título
task.status.pending: Pendiente
task.status.in_progress: 'En progreso'
task.status.success: Completado
task.status.failure: Error
security.login_button_login: Entrar
security.login_button_create_account: 'Crear una cuenta'
security.login_title: 'Por favor escriba sus datos'
security.login_remember_me: 'Recordar mis datos'
security.login_question_password: '¿Has olvidado tu contraseña?'
security.button_register: Registrarme
security.button_exist_accoutn: 'Ya tengo una cuenta creada'
security.button_account: 'Aceptar las condiciones'
security.first_name: Nombre
security.last_name: Apellido
security.password: Contraseña
security.repeat_password: 'Repetir contraseña'
security.register: Registro
security.remembered_the_password: '¿Has recordado tu contraseña? Identifícate'
security.button_send_email: 'Enviar correo electrónico'
security.reset_your_password: 'Restablecer su contraseña'
security.text_reset_password: 'Introduce tu dirección de correo electrónico y enviaremos un enlace para restablecer tu contraseña.'
course_level.label_in_singular: Nivel
course_level.label_in_plural: Niveles
component_video.add_package_video: 'Añadir vídeo'
component_video.edit_package_video: 'Editar vídeo'
component_video.type: Tipo
component_video.url_video: 'Url video'
component_video.file_subtitle: 'Archivo de subtítulo'
component_video.button_save: Guardar
component_video.text_content_subtitle_video: 'Este vídeo ya tiene subtítulo cargado. Si añades uno nuevo, se sobrescribirá.'
component_video.upload_file_video: 'Seleccionar archivo vídeo'
component_video.preparing_file: 'Espere, preparando archivo'
component_video.package_video: 'Paquete de vídeo'
component_video.optimizing_video: 'El vídeo se está optimizando y estará disponible en breve'
component_video.text_good: Bien
filter_category.label_in_singular: 'Categoría filtro'
filter_category.label_in_plural: 'Categorías filtro'
filter_category.configureFields.name: Nombre
filter.label_in_singular: Filtro
filter.label_in_plural: Filtros
filter.configureFields.name: Nombre
filter.configureFields.action_add: 'Añadir filtro'
filter.extras.no_filters: 'No hay filtros asignados'
filter.extras.loadings: Cargando...
filter.extras.no_filter_selected: 'No hay ningun filtro seleccionado'
filter.extras.no_filter_assigned: 'No hay filtros para asignar'
news.form.title: Título
news.form.text: Texto
help.pdf.general: ADMIN_ES
help.video.general: '549279910'
segment_category.label_in_singular: 'Categoría segmento'
segment_category.label_in_plural: 'Categorías segmento'
segment_category.configureFields.name: Nombre
course_segmente.label_in_singular: Segmento
course_segmente.label_in_plural: Segmentos
course_segmente.configureFields.name: Nombre
course_segmente.configureFields.action_add: 'Añadir segmento'
documentation.label: Documentación
documentation.title: Título
documentation.description: Descripción
documentation.type: Tipo
documentation.file: Archivo
documentation.locale: Idioma
pdf.downloadable: Descargable
itinerary.label_in_singular: Itinerario
itinerary.label_in_plural: Itinerarios
itinerary.name: Nombre
itinerary.description: Description
itinerary.tab.courses: Cursos
itinerary.tab.users: Personas
itinerary.no_courses: 'No se han añadido cursos al itinerario'
itinerary.no_users: 'No se han añadido personas al itinerario'
itinerary.saving_courses: 'Guardando cursos'
itinerary.find_available_courses: 'Buscar cursos disponibles'
itinerary.find_selected_courses: 'Buscar cursos seleccionados'
itinerary.course.position_updated: 'Posición del curso actualizada'
itinerary.course.update_warning: 'Los cursos de este itinerario se van a actualizar'
itinerary.user.add_success: 'La persona usuaria ha sido creada con éxito'
itinerary.user.remove_success: 'La persona usuaria ha sido eliminada con éxito'
itinerary.user.confirm_delete: '¡Cuidado! Esta persona perderá el acceso al itinerario'
itinerary.user.confirm_delete_all: '¡Cuidado! Las personas vinculadas a este itinerario perderán el acceso'
itinerary.manager.add_success: 'Manager añadido con éxito'
itinerary.manager.remove_success: 'Manager eliminado con éxito'
itinerary.manager.edit_manager: 'Editar managers'
itinerary.manager.find_managers: 'Buscar managers'
itinerary.manager.confirm_delete: 'El manager va a perder el acceso al itinerario'
itinerary.manager.confirm_delete_all: 'Los managers van a perder acceso al itinerario'
itinerary.filter.added: 'Filtro añadido al itinerario correctamente'
itinerary.filter.removed: 'Filtro eliminado del itinerario correctamente'
itinerary.total_courses: 'Cursos totales'
common_areas.cancel: Cancelar
common_areas.add_all: 'Añadir todos'
common_areas.remove_all: 'Eliminar todos'
user_filter.modify_users: 'Modificar personas'
user_filter.find_by: 'Buscar por'
common_areas.total: Total
common_areas.confirm_delete: "<p style=\"font-size: 14px;\"><span>¿Realmente quieres borrar este elemento?</span></p>\n<p style=\"font-size: 14px;\"><span>Esta acción no se puede deshacer.</span></p>"
common_areas.confirm_save: '¿Realmente quieres guardar?'
challenges: Duelos
challenges.random: Aleatorio
challenges.question: Pregunta
challenges.correct: Correcta
challenges.answer1: 'Respuesta 1'
challenges.answer2: 'Respuesta 2'
challenges.answer3: 'Respuesta 3'
challenges.answer4: 'Respuesta 4'
challenges.answer5: 'Respuesta 5'
challenges.answer6: 'Respuesta 6'
material_course.configureFields.type: 'Tipo de archivo'
material_course.configureFields.save: 'El material se guardó correctamente'
material_course.configureFields.type_1: PDF
material_course.configureFields.type_2: Vídeo
material_course.configureFields.type_3: Zip/Rar
material_course.configureFields.type_4: Imagen
material_course.configureFields.type_5: 'Paquetes de office'
material_course.configureFields.type_6: 'Bloc de notas'
material_course.configureFields.file: Archivo
material_course.configureFields.no_material: 'No se han añadido materiales'
material_course.configureFields.question_delete: '¿Realmente quieres borrar este material?'
material_course.configureFields.question_decition: 'Esta acción no podrá deshacerse después'
material_course.configureFields.delete: 'Eliminar material'
material_course.placeholder.file: 'Seleccione archivo'
material_course.download: Descargar
taskCourse.configureFields.noFile: 'No hay archivos '
taskCourse.configureFields.question_delete: '¿Realmente deseas eliminar este archivo?'
taskCourse.labelInSingular: Tarea
taskCourse.labelInPlural: Tareas
taskCourse.configureFields.dateDelivery: 'Fecha de entrega'
taskCourse.configureFields.startDate: 'Fecha de inicio'
taskCourse.configureFields.visible: Visible
taskCourse.configureFields.senTask: 'La tarea ha sido enviada'
taskCourse.configureFields.senTaskUser: Enviado
taskCourse.configureFields.addFile: 'Añadir archivo'
taskCourse.configureFields.state_0: Pendiente
taskCourse.configureFields.state_1: Entregado
taskCourse.configureFields.state_2: 'En revisión'
taskCourse.configureFields.state_3: Rechazado
taskCourse.configureFields.state_4: Aprobado
taskCourse.configureFields.files_attachment: 'Archivos adjuntos'
taskCourse.configureFields.sendComment: 'El comentario ha sido enviado'
taskCourse.configureFields.stateTask: 'La tarea ha cambiado de estado'
taskCourse.configureFields.history: Historial
component_game.true_or_false: 'Verdadero o falso'
component_game.adivina_imagen: Adivinanza
component_game.ordenar_menorMayor: 'De mayor a menor'
component_game.parejas: Parejas
component_game.rouletteWheel: 'Rueda de letras'
component_game.categorized: '¿Dónde encaja?'
component_game.fillgaps: 'Rellenar huecos'
component_game.guessword: 'Ordena letras'
component_game.wordle: 'Palabra secreta'
component_game.lettersoup: 'Sopa de letras'
component_game.videoquiz: 'Video quiz'
games.letterwheel: 'Rueda de letras'
games.opciones: 'Elija una opción'
games.categorize: '¿Dónde encaja?'
games.optiones_empty: 'Debes añadir al menos dos opciones para poder configurar correctamente el desafío.'
games.validate_add_categorize: 'Debes editar el campo del enunciado, seleccionar una respuesta correcta o seleccionar una imagen'
games.add_category: 'Añadir una opción'
games.add_categories: 'Añadir grupo o familia'
games.add_word: 'Añadir palabra'
games.words: Palabras
games.edit_option: 'Editar opción'
games.text_common.answer: Respuesta
'games.text_common:correct': Correcto
games.text_common.time: Tiempo
games.text_common.word: Palabra
games.text_common.no_questions: 'No hay preguntas'
games.text_common.text_question: 'Texto de la pregunta'
games.text_common.word_question: 'Palabra de la pregunta'
games.text_common.message_guess_word_question: 'Debes escribir el texto de la pregunta'
games.text_common.message_guess_word_word: 'Debes escribir la palabra de la pregunta'
games.text_common.message_guess_word_time: 'Debes configurar el tiempo para la pregunta'
games.text_common.message_guess_word_answer: 'La respuesta debe contener una sola palabra'
games.text_common.select_image: 'Seleccionar imagen'
games.text_common.ilustre_category: 'Imagen para ilustrar la categoría'
games.text_common.ilustre_question: 'Imagen para ilustrar la pregunta'
games.text_common.message_higher_lower: 'Para personalizar el contenido del juego, crea las palabras que quieras que aparezcan y luego ordenarlas a tu gusto. Para hacerlo, simplemente arrastra las palabras para cambiar su orden.'
games.validate_memory_match: 'Falta por añadir un título o por añadir una imagen'
games.help: Ayuda
games.validate_hidden_image: 'La pregunta, la solución y la imagen son obligatorios'
games.fillgap.title: '¿Cómo construir el juego?'
games.fillgap.message: 'En el campo “Añadir frase”, puedes diseñar la estructura del juego y posteriormente decidir que palabra del texto será un hueco. Cuando añadas un hueco, se mostrará en azul para que puedas identificarlo fácilmente.'
games.fillgap.result_question: 'Resultado del juego'
games.fillgap.word: 'Añadir frase o hueco'
games.fillgap.add_filler: 'Añadir frase'
games.fillgap.add_gap: 'Añadir hueco'
games.fillgap.new_option: 'Nueva opción'
games.fillgap.validate_save: 'Debes añadir una frase y al menos dos huecos'
games.videoquiz.message_validate_answer: 'Debes añadir un título, mínimo dos respuestas y  debe contener una correcta'
games.videoquiz.time_video: 'Tiempo del video'
games.videoquiz.savell_all_changes: 'Guardar todos los cambios'
games.videoquiz.validate_to_add_question: 'Debes tener al menos una pregunta para poder guardar los cambios'
games.videoquiz.validate_letter_soup: 'Parece que te falta añadir el enunciado ó palabras'
chapter_type.1: Scorm
chapter_type.2: Contenido
chapter_type.3: Ruleta
chapter_type.4: 'Doble o nada'
chapter_type.5: Quiz
chapter_type.6: Puzle
chapter_type.7: 'Palabra secreta'
chapter_type.8: Pdf
chapter_type.9: Vídeo
chapter_type.10: Slider
chapter_type.11: 'Ruedas de letras'
chapter_type.12: 'Verdadero o Falso'
chapter_type.13: Adivinanza
chapter_type.14: 'De mayor a menor'
chapter_type.15: Parejas
chapter_type.16: '¿Dónde encaja?'
chapter_type.17: 'Rellena huecos'
chapter_type.18: 'Ordena letras'
chapter_type.19: Enigma
chapter_type.20: 'Sopa de letras'
chapter_type.21: 'Vídeo Quiz'
menu.users.exit_impersonate: 'Salir de impersonar'
menu.forum: Foro
course.export: 'Exportar cursos'
course.export.confirm: '¿Realmente quieres exportar la información de todos los cursos?'
announcements.configureFields.opinions: Opiniones
announcements.configureFields.no_messages: 'No hay mensajes'
announcements.configureFields.info_max_users: 'El número máximo de usuarios que se pueden llamar a la convocatoria es de: '
announcements.configureFields.annoucement_all: 'Convocar a todos'
question_nps.configureFields.source: 'Aplicar a'
user.actions.impersonate: Impersonar
user.show_cv: 'Ver CV'
user.delete_cv: 'Eliminar CV'
stats.export.download_file_pdf: 'Descargar PDF'
stats.export.download_file_xlsx: 'Descargar Excel'
stats.segmented.title: 'Estadísticas segmentadas'
filter.removed_filter: 'El filtro %s se ha eliminado con éxito.'
filter.added_filter: 'El filtro %s se ha añadido correctamente.'
filter.all_removed: 'Los filtros se han eliminado'
filter.all_added: 'Los filtros se han agregado'
itinerary.chart.users: 'personas han completado el itinerario'
itinerary.chart.users_process: 'personas en proceso'
itinerary.chart.users_incomplete: 'sin empezar'
itinerary.chart.users_title: ' personas asignadas de'
itinerary.chart.total_time: 'Tiempo total acumulado'
itinerary.chart.avg_time: 'Tiempo medio por persona'
itinerary.chart.by_country: 'Personas del itinerario por país'
itinerary.chart.by_hotel: 'Personas del itinerario por centro'
itinerary.chart.by_department: 'Personas del itinerario por departamento'
itinerary.chart.by_grouping: 'Personas del itinerario por agrupación'
itinerary.users_assign: 'Personas tienen asignado este itinerario'
itinerary.users.progress: 'Progreso en itinerario'
itinerary.users.download_user: 'Descargar excel'
itinerary.courses.selected: 'Cursos seleccionados'
itinerary.status.completed: Finalizado
itinerary.status.started: 'En proceso'
itinerary.status.unstarted: 'Sin empezar'
segmented_stats.title1: 'Personas formadas'
segmented_stats.title2: Horas
segmented_stats.title3: Cursos
segmented_stats.title4: Accesos
segmented_stats.distribution_by_country: 'Distribución por países'
segmented_stats.structure: Estructura
segmented_stats.hotel: Hotel
segmented_stats.by_department: 'Por Departamento'
segmented_stats.by_school: 'Por Escuela'
segmented_stats.total_hours: 'Horas totales'
segmented_stats.total_avg: 'Horas promedio'
segmented_stats.structure_avg: 'Estructura promedio'
segmented_stats.structure_total: 'Estructura total'
segmented_stats.hotel_avg: 'Hotel promedio'
segmented_stats.hotel_total: 'Hotel total'
segmented_stats.avg: Promedio
segmented_stats.courses_started: 'Cursos comenzados'
segmented_stats.courses_finished: 'Cursos finalizados'
segmented_stats.total_courses_started: 'Total de cursos comenzados'
segmented_stats.total_courses_finished: 'Total de cursos finalizados'
segmented_stats.access_totals: 'Accesos totales'
segmented_stats.access_uniques: 'Accesos únicos'
segmented_stats.certificates: Diplomas
segmented_stats.total_certificates: 'Total diplomas entregados'
library.createdAtView: 'Creado por: {email} el {date} a las {time}'
library.no_text_provided: 'No se ha ingresado texto'
library.maximum_allowed_size_exceeded: '%s: se ha superado el número máximo de caracteres permitidos.'
library.category.created: 'La categoría se ha creado con éxito'
library.category.updated: 'La categoría se ha actualizado con éxito'
library.category.deleted: 'La categoría se ha eliminado con éxito'
library.category.activated: 'La categoría se ha activado con éxito'
library.category.deactivated: 'La categoría se ha desactivado con éxito'
library.library.updated: 'La librería se ha actualizado con éxito'
library.library.created: 'La librería se ha creado con éxito'
library.library.deleted: 'La librería se ha eliminado con éxito'
library.library.name_required: 'El nombre es obligatorio y debe tener menos de 100 caracteres.'
library.library.type_required: 'El campo ''tipo'' es requerido'
library.library.link_required: 'Si el tipo es ''LINK'', debe especificarse una URL válida.'
forum.configureFields.thread: Hilo
forum.configureFields.message: Mensaje
forum.configureFields.comment: 'Comentario informe'
forum.configureFields.title_modal_add: 'Agregar foro'
forum.configureFields.title_modal_edit: 'Editar foro'
course_press.label_in_singular: 'Curso presencial'
course_press.label_in_plural: 'Cursos presenciales'
menu.courses_managment.course_sections: Secciones
common.write: 'Escribir algo'
common_areas.accept: Aceptar
common_results: resultados
games.answers: 'Añadir respuesta'
games.text_common.order_ramdom: 'Ordenar aleatoriamente'
games.puzzle.description_cropper: 'A continuación selecciona el área de la imagen que se utilizará para crear el puzle.'
games.validation_truefalse.question_or_image: 'Debe escribir la pregunta o seleccionar una imagen'
games.help.write_question: 'Escribir pregunta'
games.help.write_word: 'Escribir palabra'
games.help.write_title: 'Escribir un título'
games.help.write_answer: 'Escribir una respuesta'
games.true: Verdadero
games.false: Falso
games.edit_video_quiz: 'Ver y editar vídeo quiz'
games.delete_video_quiz: 'Eliminar vídeo quiz'
game.feedback.title: 'Activar retroalimentación'
game.feedback.title_positive: 'En caso de acierto'
game.feedback.title_negative: 'En caso de fallo (opcional)'
announcements.common.group: Grupo
announcements.common.action_denomination: 'Denominación acción'
announcements.common.modality: Modalidad
announcements.common.place_of_instruction: 'Lugar de impartición'
announcements.common.collaboration_type: 'Tipo de colaboración'
announcements.common.provider: Proveedor
announcements.common.provider_cif: 'CIF proveedor'
announcements.observations.costs: Costos
announcements.observations.course_status: 'Estado del curso'
announcements.observations.comunicado_fundae: 'Comunicado FUNDAE'
announcements.observations.comunicado_abilitia: 'Comunicado a ABILITIA'
announcements.observations.economic_module: 'Módulo económico'
announcements.observations.travel_and_maintenance: 'Desplazamiento y Manutención'
announcements.observations.provider_cost: 'Coste Proveedor'
announcements.observations.hedima_management_cost: 'Coste Gestión HEDIMA (10%)'
announcements.observations.travel_and_maintenance_cost: 'Coste desplazamiento y manutención'
announcements.observations.total_cost: 'Costo total'
announcements.observations.final_pax: 'PAX Finales'
announcements.observations.maximum_bonus: 'Máximo Bonificable (PAX Finales)'
announcements.observations.subsidized_amount: 'Importe Bonificado'
announcements.observations.private_amount: 'Importe Privado'
announcements.observations.provider_invoice_number: 'N° Factura Proveedor'
announcements.observations.hedima_management_invoice_number: 'N° Factura Gestión HEDIMA'
announcements.observations.invoice_status: 'Estado Factura'
announcements.observations.observations: Observaciones
announcements.observations.observation: Observación
announcements.course.no_chapter: 'Este curso no tiene capítulos, por que es un curso presencial'
announcements.formativeActionTypes.intern: Interna
announcements.formativeActionTypes.extern: Externa
announcements.formativeActionTypes.session_congress: Externa
common_areas.confirm_file_upload: 'Esta seguro(a) de subir el(los) documento(s)?'
common_areas.confirm_file_delete: 'Está seguro/a de eliminar?'
course_section.label_in_singular: Sección
course_section.label_in_plural: Secciones
course_section.configureFields.name: Nombre
course_section.configureFields.description: Descripción
course_section.configureFields.active: Activa
course_section.configureFields.sort: Orden
course_section.configureFields.translations: Traducciones
course_section.configureFields.section_name: 'Nombre de la sección'
course_section.configureFields.categories: Categorías
user.roles.administrator: Administrador
user.roles.user: Usuario
user.roles.tutor: Tutor
user.roles.subsidizer: Inspector
user.roles.manager: Manager
user.roles.manager_editor: 'Manager - Editor'
user.roles.team_manager: 'Responsable de equipo'
survey.label_in_plural: Encuestas
course.configureFields.is_main: 'Este curso solamente utilizará preguntas propias para la evaluación'
global.error: 'Ha ocurrido un error. Por favor, intentelo de nuevo más tarde'
quiz.configureFields.title_creation: 'Creación de pregunta'
quiz.configureFields.question: 'Enunciado de pregunta'
quiz.configureFields.question_placeholder: 'Escribir enunciado de pregunta'
quiz.configureFields.question_delete: '¿Deseas realmente eliminar esta pregunta?'
rouletteWord.configureFields.statement: Enunciado
rouletteWord.configureFields.answer: Respuesta
rouletteWord.configureFields.type_0: 'Comienza por la letra'
rouletteWord.configureFields.type_1: 'Contiene la letra'
rouletteWord.configureFields.error.statement.max: 'El enunciado no puede superar los ${max} caracteres.'
rouletteWord.configureFields.error.statement.empty: 'El enunciado no puede estar vacío'
rouletteWord.configureFields.error.answer.max: 'La respuesta no puede superar los {max} caracteres.'
rouletteWord.configureFields.error.answer.empty: 'La respuesta no puede estar vacía'
rouletteWord.configureFields.error.answer.starts: 'La respuesta debe comenzar por la letra'
rouletteWord.configureFields.error.answer.includes: 'La respuesta debe contener la letra'
rouletteWord.response.update_letter: 'Los datos se han actualizado correctamente'
rouletteWord.response.delete_letter: 'Los datos se han borrado correctamente'
trueorFalse.configureFields.true: Correcta
trueorFalse.configureFields.false: Falsa
enigma.configureFields.title_creation: 'Creación de enigma'
puzzle.configureFields.save_image: 'Imagen guardada correctamente'
puzzle.configureFields.select_correct_answer: 'Debe seleccionar una respuesta correcta'
puzzle.configureFields.recomendation: Recomendación
puzzle.configureFields.recomendation_dimentions: '<p>Recomendamos unas dimensiones <span class="text-primary"><b>mínimas de 1024 píxeles por lado</b></span> y <span class="text-primary"><b>máximas de 2000 píxeles por lado</b></span></p>'
puzzle.configureFields.recomendation_description: '<p> El formato de puzzle es <span class="text-primary"><b>cuadrado</b></span>, así que si seleccionamos una imagen con relación de aspecto distinta, por ejemplo una imagen apaisada, la herramienta nos permitirá recortar seleccionando el área que deseemos.</p>'
hiddenword.configureFields.title: 'Creación de palabra secreta'
hiddenword.configureFields.answers_title: 'Palabra secreta'
hiddenword.configureFields.answers_placeholder: 'Escribir palabra secreta'
categorize.configureFields.title_group: 'Grupos o familias'
fillgaps.configureFields.title: 'Creación de frase'
fillgaps.configureFields.fillgap: Hueco
fillgaps.configureFields.fillgaps: Huecos
fillgaps.configureFields.type_list: Lista
fillgaps.configureFields.type_drag: Arrastrar
guesword.configureFields.word_title: 'Palabra desordenada'
guesword.configureFields.word_title_placeholder: 'Escribir palabra que aparecerá desordenada'
guesword.configureFields.solution: Solución
guesword.configureFields.solution_placeholder: 'Escribir la solución del juego'
guesword.configureFields.help_placeholder: 'Escribir ayuda para el juego'
pairs.configureFields.title: 'Creación del juego'
pairs.configureFields.placeholder_title: 'Escribir el enunciado'
pairs.configureFields.create_game: 'Creación de juego'
chapter_type.description.22: '<p>La solución ideal para crear contenido dinámico y atractivo en tus cursos o píldoras formativas.Presenta la información de manera visual y con gran variedad de interacciones basadas en textos, imágenes, videos, audios, enlaces multimedia, tarjetas interactivas, escenas enlazadas, etc.</p>'
chapter_type.add.22: 'Crear VCMS'
games.videoquiz_exist_question: 'Ya existe una pregunta con este tiempo'
chapter_type.22: VCMS
video.configureFields.title: 'Creación de vídeo quiz'
video.configureFields.add_question: 'Añadir pregunta'
Next: Siguiente
hours: Horas
minutes: Minutos
seconds: Segundos
field_required: 'Campo requerido'
field_invalid: 'Campo inválido'
field_invalid_format: 'Formato inválido'
remaining_characters: 'Caracteres restantes'
minimiun_characters: 'Mínimo de caracteres'
menu.home.title: 'Ir al campus'
course.season.type.sequential: Secuencial
course.season.type.free: Libre
course.season.type.exam: Examen
games.fillgap.add_fillgap: 'Agregar huecos cliqueando sobre algunas palabras'
course_section.configureFields.hideCategoryName: 'Ocultar nombre de la categoría'
user.roles.super_administrator: SuperAdministrador
settings.menu.label: Configuración
settings.header.title: Configuración
setting.menu.general: General
setting.menu.catalog: Catálogos
share: Compartir
report.announcement.participants: Participantes
report.announcement.groupCode: 'Código grupo'
report.announcement.enterpriseProfile: 'Perfil empresa'
report.announcement.file: Expediente
report.announcement.totalStudents: 'Total estudiantes'
report.announcement.enterpriseCIF: 'CIF empresa'
report.announcement.advisor: Tutor
course.stats.started: Iniciado
course.stats.ended: Finalizado
course.stats.total_time: 'Tiempo total'
course.stats.avg_time: 'Tiempo medio'
course.stats.minutes: minutos
course.stats.minute: minuto
course.stats.hours: horas
course.stats.hour: hora
course.stats.second: segundo
course.stats.seconds: segundos
question.configureFields.quantity_max_question: 'Número máximo de preguntas a mostrar:'
user.configureFields.available_courses: 'Cursos disponibles'
user.configureFields.available_chapter: 'Capítulo disponible'
user.configureFields.courses_stats.finished: finalizados
user.configureFields.courses_stats.started: 'En proceso'
user.configureFields.courses_stats.available: disponibles
user.configureFields.courses_stats.sent_messages: enviados
user.configureFields.courses_stats.received_messages: recibidos
user.configureFields.courses_stats.others: Otros
user.configureFields.permissions: Permisos
course.configureFields.segments: Segmentos
stats.roles: Roles
course.configureFields.language: Idioma
game.feedback.wrong: 'Ejemplo: ¡Vaya!'
chapter_type.description.23: '<p>El "role-play" o "juego de roles" es una actividad en la cual los participantes asumen y actúan como personajes ficticios, a menudo dentro de un escenario o contexto específico. Durante el roleplay, los participantes adoptan temporalmente la personalidad, características y comportamientos de los personajes que representan, interactuando entre sí de acuerdo con las circunstancias y el entorno imaginario establecido. Esta práctica se utiliza en una variedad de contextos, como juegos, terapia, simulaciones educativas y actividades recreativas, con el propósito de fomentar la creatividad, la empatía, la resolución de problemas y la exploración de situaciones hipotéticas.</p>'
password.uppercase: 'Requerido 1 o más caracteres en mayúsculas'
password.number: 'Requerido 1 o más dígitos numéricos'
password.minimum: 'La contraseña debe tener un mínimo de %s caracteres.'
password.disable_3_consecutive_chars: 'No se permite repetir un carácter más de 3 veces seguidas'
password.lowercase: 'Requerido 1 o más caracteres en minúsculas'
chapter_type.23: Role-play
chapter_type.add.23: 'Crear role-play'
password.special_characters: 'Requerido 1 o más caracteres especiales'
roleplay.status.failure: 'Has suspendido'
roleplay.status.success: 'Has aprobado'
user.configureFields.locale: Idioma
course.created: 'El curso se ha guardado correctamente'
question.configureFields.do_all_questions: 'Usar todas las preguntas'
announcements.news.start_announcement: '¡El curso %course% falta poco para que inicie!'
announcements.news.finish_announcement: '¡El curso %course% falta poco para que finalice!'
user.configureFields.dni: DNI
course_section.configureFields.section_aditional: 'Formación adicional'
report.announcement.time_conexion: 'Tiempo de conexión'
report.announcement.init_finish: 'Inicio y finalización'
report.annnouncement.conexions: Conexiones
report.annnouncement.chat_tutor: 'Chat con el tutor'
report.annnouncement.first_conexion: 'Primera conexión'
report.annnouncement.last_conexion: 'Última conexión'
generic_token.assistance.success: 'Tu asistencia se ha registrado correctamente'
generic_token.assistance.user_not_in_group: 'Usuario no pertenece al grupo de la sesión'
chat.notification.number_of_messages: 'Tienes %s mensaje(s) sin leer'
certificate.notification.available: 'Diploma de la convocatoria del curso %s disponible para descargar'
user_fields_fundae.title: 'Campos adicionales FUNDAE'
user_fields_fundae.social_security_number: 'Número de la Seguridad Social'
user_fields_fundae.gender: Género
user_fields_fundae.email_work: 'Correo del trabajo'
user_fields_fundae.birthdate: 'Fecha de nacimiento'
user_fields_fundae.dni: DNI
user_fields_fundae.contribution_account: 'Cuenta de contribución'
user_fields_fundae.incapacity: Incapacidad
user_fields_fundae.victim_of_terrorism: 'Víctima de terrorismo'
user_fields_fundae.gender_violence: 'Víctima de violencia de género'
fundae_assistance_template.main_title: 'CONTROL DE ASISTENCIA FORMATIVA'
fundae_assistance_template.action_type: 'DENOMINACIÓN DE LA ACCIÓN FORMATIVA'
fundae_assistance_template.action_code: 'CÓDIGO DE ACCIÓN'
fundae_assistance_template.group: GRUPO
fundae_assistance_template.start_at: 'FECHA DE INICIO'
fundae_assistance_template.finish_at: 'FECHA FIN'
fundae_assistance_template.main_formation_teacher: 'FORMADOR/ RESPONSABLE DE FORMACIÓN'
fundae_assistance_template.session_number: 'SESIÓN N°'
fundae_assistance_template.date: FECHA
fundae_assistance_template.morning_afternoon: 'MAÑANA / TARDE'
fundae_assistance_template.signed: Firmado
fundae_assistance_template.info_signed_person: 'Formador/Resp. formación'
fundae_assistance_template.assistance_data: 'Datos de los asistentes'
fundae_assistance_template.signatures: FIRMAS
fundae_assistance_template.observations: OBSERVACIONES
fundae_catalogs.main_page.title: 'Catálogos FUNDAE'
fundae_catalogs.user_company.label_in_plural: Empresas
fundae_catalogs.user_company.label_in_singular: Empresa
fundae_catalogs.user_professional_category.label_in_plural: 'Categorías profesionales'
fundae_catalogs.user_professional_category.label_in_singular: 'Categoría profesional'
fundae_catalogs.user_study_level.label_in_plural: 'Niveles de estudio'
fundae_catalogs.user_study_level.label_in_singular: 'Nivel de estudio'
fundae_catalogs.user_work_center.label_in_plural: 'Centros de trabajo'
fundae_catalogs.user_work_center.label_in_singular: 'Centro de trabajo'
fundae_catalogs.user_work_department.label_in_plural: 'Departamentos de trabajo'
fundae_catalogs.user_work_department.label_in_singular: 'Departamento de trabajo'
fundae_catalogs.fields.state.title: Estado
fundae_catalogs.fields.state.active: Activo
fundae_catalogs.fields.state.inactive: Inactivo
excel.userAnnouncement.sheet1.title: 'Info general'
excel.userAnnouncement.sheet1.colum1: 'Cantidad de itinerarios'
excel.userAnnouncement.sheet1.colum2: 'Cantidad de usuarios que tienen itinerarios'
excel.userAnnouncement.sheet1.colum3: 'Cantidad de cursos en los itinerarios'
excel.userAnnouncement.sheet2.title: 'Catalogo itinerarios'
excel.userAnnouncement.sheet2.colum1: Id
excel.userAnnouncement.sheet2.colum2: 'Nombre itinerarios'
excel.userAnnouncement.sheet2.colum3: División
excel.userAnnouncement.sheet2.colum4: Categoría
excel.userAnnouncement.sheet2.colum5: 'Cursos asignados'
excel.userAnnouncement.sheet2.colum6: 'Personas asignadas'
excel.userAnnouncement.sheet2.colum7: 'Personas han completado itinerario'
excel.userAnnouncement.sheet2.colum8: 'Personas en proceso'
excel.userAnnouncement.sheet2.colum9: 'Personas sin empezar'
excel.userAnnouncement.sheet2.colum10: 'TIEMPO TOTAL ACUMULADO'
excel.userAnnouncement.sheet2.colum11: 'TIEMPO MEDIO PERSONA'
excel.userAnnouncement.sheet3.title: 'Cursos por itinerarios'
excel.userAnnouncement.sheet3.colum1: Id
excel.userAnnouncement.sheet3.colum2: 'Nombre itinerarios'
excel.userAnnouncement.sheet3.colum3: 'Nombre curso'
excel.userAnnouncement.sheet3.colum4: Completado
excel.userAnnouncement.sheet3.colum5: 'En proceso'
excel.userAnnouncement.sheet3.colum6: 'Sin empezar'
course.message_saved: 'Curso guardado'
chapter.configureFields.create_chapter: 'Crear capítulo'
user_filter.assign_manual: 'Asignar manualmente'
user_filter.assign_filters: 'Asignar por filtros'
user.configureFields.configureLocale: 'Configurar idiomas'
user.configureFields.configureLocaleAdmin: 'Configurar idioma para el panel de administración'
user.configureFields.configureLocaleCampus: 'Configurar idioma para campus'
stats.export.configsheet.title: Configuración
stats.export.configsheet.content_title: 'Informe general de estadísticas'
stats.export.configsheet.content_period: 'Periodo comprendido (Fecha de finalización)'
stats.export.configsheet.content_filters: 'Filtros activos'
stats.export.configsheet.content_period_from: Desde
stats.export.configsheet.content_period_to: Hasta
stats.export.datasheet.title: Datos
stats.export.filter.category: Categoría
stats.export.filter.departament: Departamento
stats.export.filter.gender: Género
stats.export.filter.activeUsers: Usuarios
stats.export.filter.activeUsers_val_yes: Activos
stats.export.filter.activeUsers_val_no: Inactivos
stats.export.filter.course_full_title: 'Curso al 100%'
stats.export.filter.course_full_val_yes: Sí
stats.export.filter.course_full_val_no: 'No'
stats.export.filter.course_full_descr: '(Incluir sólo los cursos que se hayan realizado por completo en el periodo indicado)'
stats.export.filter.course_intime_title: 'Vida del curso en el periodo'
stats.export.filter.course_intime_val_yes: Sí
stats.export.filter.course_intime_val_no: 'No'
stats.export.filter.course_intime_descr: '(Incluir sólo los cursos que hayan comenzado y terminado dentro del periodo indicado)'
stats.export.filter.course_started_in_period_title: 'Curso iniciado en el rango de fechas'
stats.export.filter.course_started_in_period_val_yes: Sí
stats.export.filter.course_started_in_period_val_no: 'No'
stats.export.filter.course_finished_in_period_title: 'Curso finalizado en el rango de fechas'
stats.export.filter.course_finished_in_period_val_yes: Sí
stats.export.filter.course_finished_in_period_val_no: 'No'
stats.export.filter.customFilters: Personalizados
stats.content_allusers: 'Todos los usuarios'
stats.content_inactive: Inactivos
stats.content_inactive_f: Inactivos
itinerary.user.assign_manual: 'Asignar manualmente'
itinerary.user.assign_filter: ' Asignar por filtros'
itinerary.user.modify_users: 'Asignar personas manualmente'
itinerary.user.filter_find_by: 'Buscar por'
common_areas.close: Cerrar
itinerary.chart.avg_time_active: '(Total activas)'
itinerary.chart.avg_time_all: '(Total asignadas)'
itinerary.courses.modify: 'Asignar cursos'
itinerary.courses.appliedfilter: ' curso/s mostrado/s (filtrados por '
itinerary.users.appliedfilter: ' persona/s mostrada/s (filtradas por '
itinerary.courses.available: 'Cursos disponibles'
excel.userAnnouncement.sheet1.colum2b: 'Cantidad de usuarios únicos que tienen itinerarios'
excel.userAnnouncement.sheet1.colum3b: 'Cantidad de cursos únicos en los itinerarios'
common_areas.select_choice: 'Seleccione una opción'
chapter_type.24: LTI
lti_chapter.title: 'Capítulo LTI'
lti_chapter.add: 'Añadir capítulo LTI'
lti_chapter.edit: 'Editar capítulo LTI'
lti_chapter.identifier: 'Identificador LTI'
lti_chapter.identifier_required: 'Identificador LTI requerido'
chapter_type.add.24: 'Añadir capítulo LTI'
categoryFilter.label: 'Categoría filtros'
categoryFilter.title: 'Categorias filtro'
user.configureFields.localeCampus: 'Idioma campus'
global.bulk.sheetValidation.error_tab_1: 'La primera pestaña no se llama "Listado Formaciones".'
global.bulk.sheetValidation.error_tab_2: 'La segunda pestaña no se llama "Participantes".'
stats.export.user_creation: 'Filtrar usuarios por fecha de creación'
stats.export.users_export_title: 'Estadísticas de usuarios'
chapter_type.validation_course: "\nEl capítulo no se puede eliminar porque algunos usuarios ya han registrado actividad en él."
user.configureFields.courses_stats.notstarted: 'sin iniciar'
course.configureFields.created_at: 'Fecha de creación'
course.configureFields.translate: Traducir
messages.configureFields.timezone: 'Zona horaria'
user.email: 'Correo electrónico'
course.diploma.index: 'Diplomas personalizados'
menu.stats.reports.diplomas: 'Informes y diplomas'
itinerary.succes.download: 'El informe de itinerarios se está procesando y podrás encontrarlo en "Informes y diplomas"'
user.diploma.generate: 'Generar diplomas'
filters.placeholder: 'Escribir búsqueda'
filters.remove_all: 'Quitar todos'
filters.add_all: 'Añadir todos'
announcement.report_group_resume_individual: 'Resumen ejecutivo individual'
announcement.report_downloaded_diploma: 'Diploma descargado'
announcements.configureFields.code: 'Nombre de la convocatoria'
task.status.review: 'En revisión'
task.status.error: 'Error de sistema'
email.error.subject: 'Error en %context% (ID: %id%) - Entorno: %appName%'
email.error.subject_no_id: 'Error en %context% - Entorno: %appName%'
email.error.title: 'Error al ejecutar la Tarea'
email.error.environment: Entorno
email.error.context: Contexto
email.error.task_id: 'ID de la Tarea'
email.error.error_details: 'Detalles del error'
email.error.error_message: 'Mensaje de error'
email.error.error_line: Línea
email.error.error_file: Archivo
email.error.additional_info: 'Información adicional'
email.error.regards: 'Saludos cordiales'
email.error.team: 'El equipo de %appName%'
email.zombie.subject: 'Tarea en estado ZOMBIE en %context% (ID: %id%) - Entorno: %appName%'
email.zombie.title: 'Notificación de tarea en estado ZOMBIE'
email.zombie.environment: Entorno
email.zombie.context: Contexto
email.zombie.task_id: 'ID de la Tarea'
email.zombie.marked_as: 'se ha marcado como'
email.zombie.zombie_status: ZOMBIE
email.zombie.timeout_reason: 'porque superó el tiempo permitido de ejecución'
email.zombie.check_details: 'Por favor, revisa el panel de administración o la consola para ver más detalles y tomar las acciones correspondientes'
email.zombie.additional_info: 'Información adicional'
email.zombie.regards: 'Saludos cordiales'
email.zombie.team: 'El equipo de %appName%'
season.delete: 'No es posible eliminar esta temporada, actualmente tiene capítulos vinculados'
delete.season.chapters.users: "No se puede eliminar la temporada %seasonName%,\n                     los capítulos (%ChaptesTitles%) tienen actividad de usuarios"
delete.season.danger: 'No se puede eliminar la temporada'
stats.task.queued: 'Petición de Task encolada'
itinerary.delete.confirm.validation: 'Actualmente el itinerario esta activo, para proceder con la acción debes de deshabilitar el itinerario'
itinerary.delete.confirm.title: '¿Realmente deseas eliminar el itinerario?'
course.publish.message.active: 'Curso publicado'
course.publish.message.unactive: 'Curso marcado como no publicado'
itinerary.delete.error: 'No se puede eliminar el itinerario'
courser.chaperts.orders.succes: 'Orden de capítulos actualizado exitosamente'
course.publish.message.unactive.chapters: 'No se puede publicar porque el curso esta incompleto'
course.undelete.message: 'El curso no se puede eliminar debido a que tiene contenido asignado'
user.roles.creator: Creador
vich_uploader.form_label.delete_confirm: '¿Eliminar?'
vich_uploader.link.download: Descargar
