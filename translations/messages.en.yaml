menu.title_platform: 'Training Campus'
complete: Completed
Show: View
Edit: Edit
Remove: Remove
Delete: Delete
total: Total
'Yes': 'Yes'
'No': 'No'
Actions: Actions
Clear: Clear
'No results found': 'No results found'
Configuration: Configuration
Limit: Limit
Close: Close
Save: Save
'Save and create other': 'Create and add another'
'Save changes': 'Save changes'
'Save and keep editing': 'Save and keep editing'
state: State
create: Create
cancelar: Cancel
back: Back
add: Add
no_content: 'No content'
no_result: 'No results found'
configure_simulator: 'Configure simulator'
edit_configure_simulator: 'Edit configuration'
configure_success: 'Configuration completed successfully'
save_success: 'Record saved successfully'
error_success: 'An error occurred while saving the record'
configure_completed: 'Configuration completed'
'Created At': Created
'Created By': 'Created by'
'Created by': 'Created by'
'Updated At': Updated
'Updated By': 'Updated by'
'Deleted By': 'Deleted by'
'Deleted At': Deleted
menu.courses_managment.title: 'Courses Management'
menu.courses_managment.Segments: Segments
menu.courses_managment.categories: Categories
menu.courses_managment.level: Levels
menu.courses_managment.courses: Courses
menu.courses_managment.announcements: 'Call for applications'
menu.courses_managment.nps_question: 'NPS Questions'
menu.courses_managment.opinion_course: 'Course reviews'
menu.help_managment.title: 'Help management'
menu.help_managment.content_help: 'Help content'
menu.help_managment.categories_help: 'Help categories'
menu.users_managment.title: 'Users management'
menu.users_managment.users: Users
menu.users_managment.managers: Managers
menu.users_managment.filter: Filters
menu.news.title: News
menu.stats.title: Statistics
menu.stats.export: 'Excel Tool'
menu.users.edit_profile: 'Edit Profile'
form.label.delete: Delete
action.save: 'Save now'
common_areas.created_at: Created
common_areas.updated_at: Updated
common_areas.deleted_at: Updated
common_areas.created_by: 'Created by'
common_areas.updated_by: 'Updated by'
common_areas.actions: Actions
common_areas.basic_information: 'Basic information'
common_areas.edit: Edit
common_areas.delete: Delete
common_areas.name: Name
common_areas.image: Image
common_areas.state: State
common_areas.create: Create
common_areas.save: Save
common_areas.back_list: 'Back to list'
course_category.label_in_singular: 'Course category'
course_category.label_in_plural: 'Course categories'
course_category.configureFields.category_name: 'Category name'
course_category.configureFields.category_order: 'Category order'
course_category.configureFields.translations: Translations
course.label_in_singular: Course
course.label_in_plural: Courses
course.back_to_course: 'Back to the course'
course.configureFields.basic_information: 'Basic information'
course.configureFields.code: Code
course.configureFields.name: Name
course.configureFields.description: Description
course.configureFields.basic: Basic
course.configureFields.access_level: 'Access level'
course.configureFields.clone: Clone
course.configureFields.open: Open
course.configureFields.open_visible: 'Visible in open campus'
course.configureFields.active: Published
course.configureFields.categories: Categories
course.configureFields.profesional_categories: 'Professional categories'
course.configureFields.image: Image
course.configureFields.chapter: Chapter
course.configureFields.translation: Translation
course.configureFields.general_information: 'General information'
course.configureFields.segment: Segment
course.configureFields.category: Category
course.configureFields.thumbnail_url: 'Thumbnail URL'
course.configureFields.locale: Language
course.configureFields.all_seasons: 'All seasons'
course.configureFields.chapters: Chapters
course.configureFields.seasons: Seasons
course.configureFields.courses_translate: Translations
course.configureFields.add_chapter: 'Add chapter'
course.configureFields.no_seasons: 'No season'
course.configureFields.add_seasons: 'Add season'
course.configureFields.add_annuncement: 'Add call'
course.configureFields.question_modal_translate: 'Do you really want to translate this course?'
course.configureFields.content_modal_translate: 'This action will create a copy of the course to be used as a guide for translation into another language.'
course.configureFields.translate_already: 'This course already has a translation in this language'
course.configureFields.tag_description: 'Separate tags by pressing enter'
course.configureFields.new: New
course.configureFields.add_material: 'Add Material'
course.configureFields.add_task: 'Add Task'
course.configureFields.task: Tasks
course.season_add: 'The season was added successfully'
course.season_update: 'The season was updated successfully'
course.season_add_error: 'An error occurred while adding the season'
course.panel.class: 'Course data'
chapter.label_in_plural: Chapters
chapter.configureFields.title: Title
chapter.configureFields.course: Course
chapter.configureFields.type: Type
chapter.configureFields.season: Season
chapter.configureFields.description: Description
chapter.configureFields.image: Image
chapter.configureFields.image_file: 'Image file'
chapter_type.description.1: '<p>The Scorm chapter is very interesting.</p><p>It allows us to upload diverse content generated with other tools, such as documents, interactive content, or even games.</p>'
chapter_type.description.2: '<p>This is one of the most versatile chapters.</p><p>On the left, the entered titles are displayed, serving as an index to quickly find content and facilitate reading.</p>'
chapter_type.description.3: '<p>It is a question game that adds an element of chance, as you have to complete segments of a roulette wheel to pass it.</p><p>It is based on creating a battery of questions to reinforce the learned knowledge. You can enter as many questions as you want and accompany them with images.</p><p>For the optimal functioning of the game, it is recommended to include a minimum of 10 questions.</p>'
chapter_type.description.4: '<p>This game presents a series of questions that include an additional risk factor. After each question, participants have the option to stop and keep their current score or take a risk and answer an additional question to earn more points. However, if answered incorrectly, all accumulated points will be lost.</p>'
chapter_type.description.5: '<p>This is the classic game chapter.</p><p>The idea is to create a battery of questions to reinforce the learned knowledge. You can enter unlimited questions accompanied by images, with a single correct answer.</p>'
chapter_type.description.6: '<p>Sort and rotate the pieces until they are in their corresponding position and orientation; otherwise, they will not join together.</p><p>At the top, there are four segments that correspond to the available time to complete the puzzle. When a time segment runs out, one of the introduced questions is presented. Answering the questions correctly gives you more time to solve the puzzle. The final score depends on a combination of the time taken to complete the puzzle itself, the number of correctly answered questions, and the number of incorrect questions.</p>'
chapter_type.description.7: '<p>This game presents a riddle based on an image. To solve it, you must carefully select the appropriate letters before time runs out.</p>'
chapter_type.description.8: '<p>Given that the PDF format is widely used for various types of content, such as protocols or manuals, PDF chapters are very interesting as they allow us to reuse pre-edited materials.</p>'
chapter_type.description.9: '<p>Audiovisual resources have great educational potential as they attract attention and stimulate curiosity.</p><p>The platform allows us to choose how to introduce the video: via URL or by selecting a file from our computer. In the latter case, we can also attach a subtitle file.</p>'
chapter_type.description.10: '<p>Chapter of the slider type with images.</p>'
chapter_type.description.11: '<p>The game consists of guessing words, each of which corresponds to a letter on the wheel. Sometimes the solution will be a word that starts with the letter, and other times it will simply contain the letter.</p>'
chapter_type.description.12: '<p>In this game, a series of questions in text or image format, or a combination of both, will be presented as statements. There are two possible answers: ''True'' or ''False'', and only one is correct. The time to solve the game is limited.</p>'
chapter_type.description.13: '<p>In this game, you have to solve a riddle before time runs out. The clue is hidden behind a blurry image that gradually focuses as you progress in the game. In addition to the image, there is also additional help in the form of text.</p>'
chapter_type.description.14: '<p>In this classic game, you have to arrange the elements by dragging the blocks until they are in the correct order. The variety of possibilities makes it ideal for mathematical exercises and other educational challenges. It''s perfect to test and challenge reasoning and organizational skills!</p>'
chapter_type.description.15: '<p>This game is ideal for training memory and concentration. The objective is to find all pairs of identical cards. The location of the cards is randomly generated, so each game will be different.</p>'
chapter_type.description.16: '<p>In this game, a series of words, phrases, or concepts will be presented, and they must be associated with the corresponding family or group displayed below. It will test association and mental speed skills while competing against the clock.</p>'
chapter_type.description.17: '<p>In this grammar and learning game, the objective is to fill in the blanks in sentences with the appropriate words to test linguistic and grammatical skills. But that''s not all! This game is versatile and can be used for many other educational purposes.</p>'
chapter_type.description.18: '<p>In this game, a question or riddle will be presented to solve. The task will be to carefully examine the puzzle and use the provided letters to figure out the correct word. But be careful, because time is limited, which means you have to be quick and accurate to win.</p>'
chapter_type.description.19: '<p>In this game, you have to guess a hidden word in a maximum of six attempts. Each attempt consists of entering a valid word, and after each attempt, the color of the boxes will change to show which letters are correct and in the correct position.</p>'
chapter_type.description.20: '<p>This game consists of finding hidden words in a word search puzzle. The goal is to mark a sequence of letters horizontally, vertically, or diagonally. Words can be found in both directions, from left to right or from right to left. If the sequence is part of a hidden word, it will be considered a correct answer.</p>'
chapter_type.description.21: '<p>During video playback, interactive questions are inserted that require the viewer to pay attention to the video content in order to answer correctly. In short, it combines the power of video with the interactivity of a quiz to offer an effective and engaging learning experience.</p>'
chapter_type.add.1: 'Add Scorm'
chapter_type.add.2: 'Add content'
chapter_type.add.3: 'Create game'
chapter_type.add.4: 'Create game'
chapter_type.add.5: 'Create quiz'
chapter_type.add.6: 'Create game'
chapter_type.add.7: 'Create game'
chapter_type.add.8: 'Add PDF'
chapter_type.add.9: 'Add video'
chapter_type.add.10: 'Add slider'
chapter_type.add.11: 'Create game'
chapter_type.add.12: 'Create game'
chapter_type.add.13: 'Create game'
chapter_type.add.14: 'Create game'
chapter_type.add.15: 'Create game'
chapter_type.add.16: 'Create game'
chapter_type.add.17: 'Create game'
chapter_type.add.18: 'Create game'
chapter_type.add.19: 'Create game'
chapter_type.add.20: 'Create game'
chapter_type.add.21: 'Create video quiz'
chapter_type.all: All
chapter_type.content: Theory
chapter_type.games_test: Assessment
chapter_type.description_test: 'Test description'
chapter_type.type: 'Chapter type'
chapter.add_pdf: 'Add PDF'
chapter.chapter.show_video: 'Show video'
chapter.message_pdf_success: 'The PDF has been added successfully'
chapter.message_pdf_error: 'An error occurred while saving the PDF'
chapter.chapter.materials: Materials
chapter.chapter.show_pdf: 'View PDF'
announcements.label_in_singular: 'Call for applications'
announcements.label_in_plural: 'Call for applications'
announcements.configureFields.courses: Courses
announcements.configureFields.start_at: 'Start at'
announcements.configureFields.finish_at: 'Finished at'
announcements.configureFields.called: Called
announcements.configureFields.subsidized: Subsidized
announcements.configureFields.subsidizer: Inspector
announcements.configureFields.subsidizer_entity: 'Subsidizing entity'
announcements.configureFields.subsidized_announcement: 'Call for grants'
announcements.configureFields.max_users: 'Maximum users'
announcements.configureFields.formative_action_type: 'Type of training action'
announcements.configureFields.format: Format
announcements.configureFields.total_hours: 'Total hours'
announcements.configureFields.place: Place
announcements.configureFields.training_center: 'Training center'
announcements.configureFields.training_center_address: 'Address of the training center'
announcements.configureFields.training_center_nif: 'Training center NIF'
announcements.configureFields.training_center_phone: 'Training center phone number'
announcements.configureFields.training_center_email: 'Training center e-mail address'
announcements.configureFields.training_center_teacher_dni: 'Teacher training center DNI'
announcements.configureFields.called_user: 'Users called'
announcements.configureFields.search: Search
announcements.configureFields.announcement_for: 'Announcements for'
announcements.configureFields.search_user_title: 'Search for users to call announcements'
announcements.configureFields.placeholder_search_user: 'Search for users'
announcements.configureFields.placeholder_search_category: 'Search by category'
announcements.configureFields.placeholder_search_department: 'Search by department'
announcements.configureFields.placeholder_search_center: 'Search by center'
announcements.configureFields.placeholder_search_country: 'Search by country'
announcements.configureFields.placeholder_search_division: 'Search by division'
announcements.configureFields.result_found: 'Results found'
announcements.configureFields.clear_result: 'Clear results'
announcements.configureFields.error_already_called_user: 'Error: The user has already been called!'
announcements.configureFields.error_already_called_user_date: 'Error: The user has already been called, in a similar date range!'
announcements.configureFields.notified: Notified
announcements.configureFields.content_course: 'Course content'
announcements.configureFields.report: Report
announcements.configureFields.title_report: 'Student report'
announcements.configureFields.direction: Address
announcements.configureFields.telephone: Phone
announcements.configureFields.nif: 'VAT ID'
announcements.configureFields.tutor: Tutor
announcements.configureFields.apt: Apt
announcements.configureFields.time_total: 'Total time'
question.label_in_singular: Question
question.label_in_plural: Questions
question.configureFields.question: Question
question.configureFields.random: Random
question.configureFields.answers: Answers
question.configureFields.image_file: 'Image file'
question.configureFields.question_for: 'Questions for'
question.configureFields.image_for: 'Image for'
question.configureFields.add_image_puzzle: 'Add puzzle image'
question.configureFields.add_question: 'Add question'
question.configureFields.see_image: 'View image'
content.label_in_singular: Content
content.label_in_plural: Contents
content.configureFields.title: Title
content.configureFields.content: Content
content.configureFields.position: Position
content.configureFields.add_content: 'Add content'
content.configureFields.content_for: 'Content for'
question_nps.label_in_singular: 'Nps question'
question_nps.label_in_plural: 'Nps questions'
question_nps.configureFields.type: Type
question_nps.configureFields.position: Position
question_nps.configureFields.question: Question
question_nps.configureFields.course: Course
question_nps.configureFields.name_question: 'Name question'
question_nps.configureFields.translations: Translations
opinions.label_in_singular: Review
opinions.label_in_plural: Reviews
opinions.configureFields.course: Course
opinions.configureFields.question: Question
opinions.configureFields.to_post: Post
opinions.configureFields.value: Value
opinions.configureFields.valoration: Valoration
help_category.label_in_singular: 'Category help'
help_category.label_in_plural: 'Categories help'
help_category.configureFields.category_name: 'Category name'
help_category.configureFields.translations: Translations
help_text_content.label_in_singular: 'Content help'
help_text_content.label_in_plural: 'Contents help'
help_text_content.configureFields.category: Category
help_text_content.configureFields.title: Title
help_text_content.configureFields.text: Text
help_text_content.configureFields.translations: Translations
user.label_in_singular: User
user.label_in_plural: Users
user.configureFields.division: Division
user.configureFields.country: Country
user.configureFields.category: Category
user.configureFields.departament: Department
user.configureFields.center: Center
user.configureFields.gender: Gender
user.configureFields.first_name: Name
user.configureFields.last_name: 'Last name'
user.configureFields.code: Code
user.configureFields.password: Password
user.configureFields.change_password: 'Change password'
user.configureFields.courses: Courses
user.configureFields.extra: Extra
user.configureFields.announcements: Announcements
user.configureFields.extra_fields: 'Additional fields'
user.configureFields.avatar_image: 'Profile image'
user.configureFields.new_password: 'New password'
user.configureFields.birthdate: 'Date of birth'
user.configureFields.edit_user: 'Edit user'
user.configureFields.user_data: 'User data'
user.configureFields.stats: Stats
user.configureFields.chapter: Chapter
user.configureFields.ratio_course: 'Ratio Course/Persons'
user.configureFields.avg_stars: 'Average stars'
user.configureFields.time: Time
user.configureFields.chapter_time: 'Time spent per chapter'
user.configureFields.available: Available
user.configureFields.messages: Messages
user.configureFields.login_history: 'Login history'
user.configureFields.started_at: Start
user.configureFields.finished_at: Finished
user.configureFields.time_spent: 'Time spent'
user.configureFields.content_viewed: 'Contents viewed'
user.configureFields.interaction_with_teacher: 'Interactions with the teacher'
user.configureFields.course_content: 'Course contents'
user.configureFields.content_type: 'Type of content'
user.configureFields.finished: Finished
user.configureFields.teacher_interaction: 'Interactions with teachers'
user.configureFields.date: Date
user.configureFields.sender: Sender
user.configureFields.recipient: Recipient
user.configureFields.subject: Subject
user.configureFields.questions: Questions
user.configureFields.chapter_type: 'Chapter type'
user.configureFields.finished_chapter_types: 'Finished chapter types'
user.configureFields.button_validate: Validate
user.configureFields.open: 'Open campus'
user.configureFields.computer: Computer
user.configureFields.mobile: Phone
user.configureFields.tablet: Tablet
user.manage.assign_data: 'Assign data'
user.gender.m: Male
user.gender.f: Female
user.configureFields.time_title: 'Dedication per day'
user.configureFields.interaction_in_forum: 'Forum interactions'
user.configureFields.email: Email
user.configureFields.fullname: 'Name and surname'
user.filtersRequired: 'Select at least one filter'
stats.general_stats: 'General statistics'
stats.total_times_spent: 'Total time spent'
stats.users_activity: 'Users activity'
stats.users_active_last_30: 'Active last 30 days'
stats.users_inactive_last_30: 'Inactive last 30 days'
stats.users_never_login: 'Never log in'
stats.daily_chapter: 'Completed daily chapters'
stats.finished_chapters: 'Completed chapters'
stats.daily_course: 'Completed daily courses'
stats.finished_courses: 'Completed courses'
stats.daily_login: 'Daily login'
stats.daily_login_tooltip: Logins
stats.all_courses: 'All courses'
stats.all_countries: 'All countries'
stats.all_centers: 'All centers'
stats.all_categories: 'All categories'
stats.all_departament: 'All departments'
stats.all_gender: 'All genders'
stats.all_divisions: 'All divisions'
stats.filters: Filters
stats.filter_by: 'Filter by'
stats.modal_close: Close
stats.clear_filters: 'Clear filters'
stats.apply_filters: 'Apply filters'
stats.export_title: 'Export data'
stats.export.start_date: 'Start date'
stats.export.end_date: 'End date'
stats.export.filename: 'File name'
stats.export.request_date: 'Date of application'
stats.export.available_until: 'Available until'
stats.export.loading_data: 'Loading data'
stats.export.no_data: 'No data available'
stats.export.download_file: 'Download file'
stats.export.abort_export_request: 'Cancel export request'
stats.export.view_details: 'View details'
stats.export.reset_form: 'Reset form'
stats.export.error_start_date: 'The start date cannot be later than the end date.'
stats.export.export_error: 'An error has occurred generating the report'
stats.export.export_success: 'The report has been successfully added to the download queue.'
stats.export.export_dir: 'Stats / excel tool'
stats.devices_login: 'Device login'
stats.distribution_ages: 'Age distribution'
stats.generation_babyboom: Babyboom
stats.generation_x: 'Generation X'
stats.generacion_milenials: Millennials
stats.generacion_z: 'Generation Z'
stats.title_information_user: 'Information about users'
stats.title_information_content: 'Information on contents'
stats.title_information_courses: 'Information on courses'
stats.title_information_chapter: 'Information on chapter'
stats.distribution_country: 'Distribution by country'
stats.title_finish_m: finished
stats.title_made: made
stats.title_made_f: made
stats.chapter_day: day
stats.chaper_hours: hours
stats.chapter_minutes: minutes
stats.chapter_total: TOTAL
stats.chapter_media: HALF
stats.content_active: enabled
stats.content_active_f: enabled
stats.totalLogin: Total
stats.access: Access
stats.uniqueLogin: Unique
stats.at_least_one_course_finished: 'Trained users'
stats.top_rated_courses: 'Top rated courses'
stats.lowest_rated_courses: 'Lowest rated courses'
stats.most_completed_courses: 'Most completed courses'
stats.users_more_actives: 'Most active users'
stats.users_less_actives: 'Less active users'
stats.accumulative.title: 'Evolutionary and accumulated'
stats.accumulative.trained: 'Unique users trained'
stats.accumulative.new: News
stats.accumulative.accumulated: Accumulated
stats.accumulative.chart: Graph
stats.accumulative.logins: Login
stats.accumulative.courses: Courses
stats.accumulative.courses_started: 'Courses started'
stats.accumulative.courses_finished: 'Completed courses'
stats.accumulative.ratings: Ratings
stats.accumulative.time: 'Time invested (hours)'
stats.accumulative.filters: 'Filter distribution'
'stats. daily_posts': 'Daily forum posts'
stats.most_active_threads: 'Most active threads'
stats.most_active_users: 'Most active users'
stats.forum_post_messages_count: Messages
stats.forum_post_title: Title
task.status.pending: Pending
task.status.in_progress: 'In progress'
task.status.success: Done
task.status.failure: Error
security.login_button_login: 'Enter in'
security.login_button_create_account: 'Create an account'
security.login_title: 'Please enter your data'
security.login_remember_me: 'Remember me'
security.login_question_password: 'Forgot your password?'
security.button_register: Register
security.button_exist_accoutn: 'I already have an account'
security.button_account: 'Accept conditions'
security.first_name: 'First name'
security.last_name: 'Last name'
security.password: Password
security.repeat_password: 'Repeat password'
security.register: Register
security.remembered_the_password: 'Have you remembered your password?'
security.button_send_email: 'Send email'
security.reset_your_password: 'Reset your password'
security.text_reset_password: 'Enter your email address and we will send you a link to reset your password.'
course_level.label_in_singular: Level
course_level.label_in_plural: Levels
component_video.add_package_video: 'Add video package'
component_video.edit_package_video: 'Edit video package'
component_video.type: Type
component_video.url_video: 'Url video'
component_video.file_subtitle: 'Subtitle file'
component_video.button_save: Save
component_video.text_content_subtitle_video: 'This video already has a subtitle if you add a new one, it will be replaced by the previous one.'
component_video.upload_file_video: 'Select video file'
component_video.preparing_file: 'Wait preparing file'
component_video.package_video: 'Package video'
component_video.optimizing_video: 'The video is being optimized and will be available shortly.'
component_video.text_good: Well
filter_category.label_in_singular: 'Filter category'
filter_category.label_in_plural: 'Filter categories'
filter_category.configureFields.name: Name
filter.label_in_singular: Filter
filter.label_in_plural: Filters
filter.configureFields.name: Name
filter.configureFields.action_add: 'Add filter'
filter.extras.no_filters: 'No filters assigned'
filter.extras.loadings: Loading...
filter.extras.no_filter_selected: 'No filter is selected'
filter.extras.no_filter_assigned: 'No filters to assign'
news.form.title: Title
news.form.text: Text
help.pdf.general: ADMIN_EN
help.video.general: '549279910'
segment_category.label_in_singular: 'Category segment'
segment_category.label_in_plural: 'Categories segment'
segment_category.configureFields.name: Name
course_segmente.label_in_singular: Segment
course_segmente.label_in_plural: Segments
course_segmente.configureFields.name: Name
course_segmente.configureFields.action_add: 'Add segment'
documentation.label: Documentation
documentation.title: Title
documentation.description: Description
documentation.type: Type
documentation.file: File
documentation.locale: Language
pdf.downloadable: Downloadable
itinerary.label_in_singular: Itinerary
itinerary.label_in_plural: Itineraries
itinerary.name: Name
itinerary.description: Description
itinerary.tab.courses: Courses
itinerary.tab.users: Users
itinerary.no_courses: 'No courses have been added to the itinerary'
itinerary.no_users: 'No users have been added to the itinerary'
itinerary.saving_courses: 'Saving courses'
itinerary.find_available_courses: 'Search available courses'
itinerary.find_selected_courses: 'Search selected courses'
itinerary.course.position_updated: 'Course position updated'
itinerary.course.update_warning: 'The courses in this itinerary will be updated'
itinerary.user.add_success: 'User successfully added'
itinerary.user.remove_success: 'User successfully removed'
itinerary.user.confirm_delete: 'The user will lose access to the itinerary'
itinerary.user.confirm_delete_all: 'Users will lose access to the itinerary'
itinerary.manager.add_success: 'Manager successfully added'
itinerary.manager.remove_success: 'Manager successfully removed'
itinerary.manager.edit_manager: 'Edit managers'
itinerary.manager.find_managers: 'Find managers'
itinerary.manager.confirm_delete: 'The manager will lose access to the itinerary'
itinerary.manager.confirm_delete_all: 'Managers will lose access to the itinerary'
itinerary.filter.added: 'Filter added to itinerary'
itinerary.filter.removed: 'Filter removed from itinerary'
itinerary.total_courses: 'Total courses'
common_areas.cancel: Cancel
common_areas.add_all: 'Add all'
common_areas.remove_all: 'Remove All'
user_filter.modify_users: 'Modify Users'
user_filter.find_by: 'Search by'
common_areas.total: Total
common_areas.confirm_delete: "<p style=\"font-size: 14px;\">&lt;p&gt;&lt;b&gt;<span>Do you really want to delete?</span>&lt;/b&gt;&lt;br&gt;</p>\n<p style=\"font-size: 14px;\"><span>This action cannot be undone.</span>&lt;/p&gt;</p>"
common_areas.confirm_save: 'Do you really want to save?'
challenges: Duels
challenges.random: Random
challenges.question: Question
challenges.correct: Correct
challenges.answer1: 'Answer 1'
challenges.answer2: 'Answer 2'
challenges.answer3: 'Answer 3'
challenges.answer4: 'Answer 4'
challenges.answer5: 'Answer 5'
challenges.answer6: 'Answer 6'
material_course.configureFields.type: 'Type file'
material_course.configureFields.save: 'The material was stored correctly'
material_course.configureFields.type_1: PDF
material_course.configureFields.type_2: Video
material_course.configureFields.type_3: Zip/Rar
material_course.configureFields.type_4: Image
material_course.configureFields.type_5: 'Office packages'
material_course.configureFields.type_6: Notepad
material_course.configureFields.file: File
material_course.configureFields.no_material: 'No added materials'
material_course.configureFields.question_delete: 'Do you really want to delete this material?'
material_course.configureFields.question_decition: 'This action cannot be undone'
material_course.configureFields.delete: 'Delete material'
material_course.placeholder.file: 'Select file'
material_course.download: Download
taskCourse.configureFields.noFile: 'No files added'
taskCourse.configureFields.question_delete: 'Do you really want to delete this file?'
taskCourse.labelInSingular: Task
taskCourse.labelInPlural: Tasks
taskCourse.configureFields.dateDelivery: 'Delivery date'
taskCourse.configureFields.startDate: 'Start date'
taskCourse.configureFields.visible: Avalaible
taskCourse.configureFields.senTask: 'The task has been sent'
taskCourse.configureFields.senTaskUser: Sent
taskCourse.configureFields.addFile: 'Add file'
taskCourse.configureFields.state_0: Pending
taskCourse.configureFields.state_1: Delivered
taskCourse.configureFields.state_2: 'In Review'
taskCourse.configureFields.state_3: Rejected
taskCourse.configureFields.state_4: Approved
taskCourse.configureFields.files_attachment: Attachments
taskCourse.configureFields.sendComment: 'The comment has been sent'
taskCourse.configureFields.stateTask: 'The task has changed status'
taskCourse.configureFields.history: History
component_game.true_or_false: 'True or false'
component_game.adivina_imagen: Riddle
component_game.ordenar_menorMayor: 'Higher or lower'
component_game.parejas: Match
component_game.rouletteWheel: 'Letter wheels'
component_game.categorized: 'Where does it fit?'
component_game.fillgaps: 'Fill in the blanks'
component_game.guessword: 'Sort letters'
component_game.wordle: 'Secret word'
component_game.lettersoup: 'Word search'
component_game.videoquiz: 'Video quiz'
games.letterwheel: 'Letter wheels'
games.opciones: 'Select an option'
games.categorize: 'Where does it fit?'
games.optiones_empty: 'You must add at least two options in order to correctly configure the challenge.'
games.validate_add_categorize: 'You must edit the statement field, select a correct answer, or select an image.'
games.add_category: 'Add option'
games.add_categories: 'Add group or family'
games.add_word: 'Add word'
games.words: Words
games.edit_option: 'Edit option'
games.text_common.answer: Answer
'games.text_common:correct': Correct
games.text_common.time: Time
games.text_common.word: Word
games.text_common.no_questions: 'There are no questions'
games.text_common.text_question: 'Question text'
games.text_common.word_question: 'Word of the question'
games.text_common.message_guess_word_question: 'You must enter the text of the question'
games.text_common.message_guess_word_word: 'You must enter the word of the question'
games.text_common.message_guess_word_time: 'You must set the time for the question'
games.text_common.message_guess_word_answer: 'The answer must be a single word'
games.text_common.select_image: 'Select image'
games.text_common.ilustre_category: 'Image to illustrate the category'
games.text_common.ilustre_question: 'Image to illustrate the question'
games.text_common.message_higher_lower: 'To customize the game content, create the words you want to appear and then arrange them as desired. To do this, simply drag the words to change their order.'
games.validate_memory_match: 'You are missing a title or an image to add'
games.help: Help
games.validate_hidden_image: 'The question, solution, and image are required'
games.fillgap.title: 'How to build the game?'
games.fillgap.message: 'In the "Add phrase" field, you can design the structure of the game and later decide which word in the text will be a gap. When you add a gap, it will be displayed in blue for easy identification.'
games.fillgap.result_question: 'Game result'
games.fillgap.word: 'Add phrase or gap'
games.fillgap.add_filler: 'Add phrase'
games.fillgap.add_gap: 'Add gap'
games.fillgap.new_option: 'New option'
games.fillgap.validate_save: 'You must add a phrase and at least two gaps'
games.videoquiz.message_validate_answer: 'You must add a title, at least two answers, and one correct answer'
games.videoquiz.time_video: 'Video time'
games.videoquiz.savell_all_changes: 'Save all changes'
games.videoquiz.validate_to_add_question: 'You must have at least one question to save the changes'
games.videoquiz.validate_letter_soup: 'It seems you forgot to add the title or words'
chapter_type.1: Scorm
chapter_type.2: Content
chapter_type.3: Wheel
chapter_type.4: 'Double or Nothing'
chapter_type.5: Quiz
chapter_type.6: Puzzle
chapter_type.7: 'Secret Word'
chapter_type.8: Pdf
chapter_type.9: Video
chapter_type.10: Slider
chapter_type.11: 'Letter Wheels'
chapter_type.12: 'True or False'
chapter_type.13: Riddle
chapter_type.14: 'Higher or Lower'
chapter_type.15: Match
chapter_type.16: 'Where Does It Fit?'
chapter_type.17: 'Fill in the Blanks'
chapter_type.18: 'Sort Letters'
chapter_type.19: Enigma
chapter_type.20: 'Word Search'
chapter_type.21: 'Video Quiz'
menu.users.exit_impersonate: 'Exit Impersonation'
menu.forum: Forum
course.export: 'Export Courses'
course.export.confirm: 'Do you really want to export all course information?'
announcements.configureFields.opinions: Review
announcements.configureFields.no_messages: 'No messages'
announcements.configureFields.info_max_users: 'The maximum number of users that can be called to the call is:'
announcements.configureFields.annoucement_all: 'Summoning all'
question_nps.configureFields.source: 'Apply to'
user.actions.impersonate: Impersonate
user.show_cv: 'View CV'
user.delete_cv: 'Delete CV'
stats.export.download_file_pdf: 'Download PDF'
stats.export.download_file_xlsx: 'Download Excel'
stats.segmented.title: 'Segmented statistics'
filter.removed_filter: 'Filter %s removed'
filter.added_filter: 'Filter %s added'
filter.all_removed: 'Filters have been removed'
filter.all_added: 'Filters have been added'
itinerary.chart.users: 'people have completed the itinerary'
itinerary.chart.users_process: 'in progress'
itinerary.chart.users_incomplete: unstarted
itinerary.chart.users_title: 'people assigned out of'
itinerary.chart.total_time: 'Total accumulated time'
itinerary.chart.avg_time: 'Average time per person'
itinerary.chart.by_country: 'Itineraries per country'
itinerary.chart.by_hotel: 'Itineraries by center'
itinerary.chart.by_department: 'Itineraries by departament'
itinerary.chart.by_grouping: 'Itineraries by group'
itinerary.users_assign: 'People have been assigned this itinerary'
itinerary.users.progress: 'Itinerary progression'
itinerary.users.download_user: 'Download Excel'
itinerary.courses.selected: 'Selected courses'
itinerary.status.completed: Completed
itinerary.status.started: 'In progress'
itinerary.status.unstarted: Unstarted
segmented_stats.title1: 'Trained Persons'
segmented_stats.title2: Hours
segmented_stats.title3: Courses
segmented_stats.title4: Access
segmented_stats.distribution_by_country: 'Distribution by countries'
segmented_stats.structure: Structure
segmented_stats.hotel: Hotel
segmented_stats.by_department: 'By Department'
segmented_stats.by_school: 'By School'
segmented_stats.total_hours: 'Total Hours'
segmented_stats.total_avg: 'Average Hours'
segmented_stats.structure_avg: 'Average Structure'
segmented_stats.structure_total: 'Total Structure'
segmented_stats.hotel_avg: 'Average Hotel'
segmented_stats.hotel_total: 'Total Hotel'
segmented_stats.avg: Average
segmented_stats.courses_started: 'Courses Started'
segmented_stats.courses_finished: 'Completed courses'
segmented_stats.total_courses_started: 'Total courses started'
segmented_stats.total_courses_finished: 'Total courses completed'
segmented_stats.access_totals: 'Total Accesses'
segmented_stats.access_uniques: 'Single Accesses'
segmented_stats.certificates: Certificates
segmented_stats.total_certificates: 'Total diplomas awarded'
library.createdAtView: 'Created by: {email} on {date} at {time}'
library.no_text_provided: 'No text has been entered'
library.maximum_allowed_size_exceeded: '%s: The maximum number of characters allowed has been exceeded.'
library.category.created: 'The category has been successfully created'
library.category.updated: 'The category has been successfully updated'
library.category.deleted: 'The category has been successfully deleted'
library.category.activated: 'The category has been successfully activated'
library.category.deactivated: 'The category has been successfully deactivated'
library.library.updated: 'The library has been successfully updated'
library.library.created: 'The library has been successfully created'
library.library.deleted: 'The library has been successfully deleted'
library.library.name_required: 'The name is required and length must be less than 100 characters'
library.library.type_required: 'Field ''type'' is required'
library.library.link_required: 'When type is ''LINK'' a valid URL must be specified'
forum.configureFields.thread: Thread
forum.configureFields.message: Message
forum.configureFields.comment: 'Report Comment'
forum.configureFields.title_modal_add: 'Add Forum'
forum.configureFields.title_modal_edit: 'Edit Forum'
course_press.label_in_singular: 'On-site Course'
course_press.label_in_plural: 'On-site Courses'
menu.courses_managment.course_sections: Sections
common.write: 'Write something'
common_areas.accept: Accept
common_results: results
games.answers: 'Add answer'
games.text_common.order_ramdom: 'Sort Randomly'
games.puzzle.description_cropper: 'Please select the area of the image that will be used to create the puzzle.'
games.validation_truefalse.question_or_image: 'You must write the question or select an image'
games.help.write_question: 'Write question'
games.help.write_word: 'Write word'
games.help.write_title: 'Write a title'
games.help.write_answer: 'Write an answer'
games.true: 'True'
games.false: 'False'
games.edit_video_quiz: 'View and edit video quiz'
games.delete_video_quiz: 'Delete video quiz'
game.feedback.title: 'Enable feedback'
game.feedback.title_positive: 'In case of correct answer'
game.feedback.title_negative: 'In case of wrong answer (optional)'
announcements.common.group: Group
announcements.common.action_denomination: 'Action Denomination'
announcements.common.modality: Modality
announcements.common.place_of_instruction: 'Place of Instruction'
announcements.common.collaboration_type: 'Collaboration Type'
announcements.common.provider: Provider
announcements.common.provider_cif: 'Provider CIF'
announcements.observations.costs: Costs
announcements.observations.course_status: 'Course Status'
announcements.observations.comunicado_fundae: 'FUNDAE Communication'
announcements.observations.comunicado_abilitia: 'ABILITIA Communication'
announcements.observations.economic_module: 'Economic Module'
announcements.observations.travel_and_maintenance: 'Travel and Maintenance'
announcements.observations.provider_cost: 'Provider Cost'
announcements.observations.hedima_management_cost: 'HEDIMA Management Cost (10%)'
announcements.observations.travel_and_maintenance_cost: 'Travel and Maintenance Cost'
announcements.observations.total_cost: 'Total Cost'
announcements.observations.final_pax: 'Final PAX'
announcements.observations.maximum_bonus: 'Maximum Bonusable (Final PAX)'
announcements.observations.subsidized_amount: 'Subsidized Amount'
announcements.observations.private_amount: 'Private Amount'
announcements.observations.provider_invoice_number: 'Provider Invoice Number'
announcements.observations.hedima_management_invoice_number: 'HEDIMA Management Invoice Number'
announcements.observations.invoice_status: 'Invoice Status'
announcements.observations.observations: Observations
announcements.observations.observation: Observation
announcements.course.no_chapter: 'This course has no chapters as it is an in-person course.'
announcements.formativeActionTypes.intern: Internal
announcements.formativeActionTypes.extern: External
announcements.formativeActionTypes.session_congress: External
common_areas.confirm_file_upload: 'Are you sure you want to upload the document(s)?'
common_areas.confirm_file_delete: 'Are you sure you want to delete?'
course_section.label_in_singular: Section
course_section.label_in_plural: Sections
course_section.configureFields.name: Name
course_section.configureFields.description: Description
course_section.configureFields.active: Active
course_section.configureFields.sort: Order
course_section.configureFields.translations: Translations
course_section.configureFields.section_name: 'Section Name'
course_section.configureFields.categories: Categories
user.roles.administrator: Administrator
user.roles.user: User
user.roles.tutor: Tutor
user.roles.subsidizer: Inspector
user.roles.manager: Manager
user.roles.manager_editor: 'Manager - Editor'
user.roles.team_manager: 'Team Manager'
survey.label_in_plural: Surveys
course.configureFields.is_main: 'This course will only use its own questions for evaluation'
global.error: 'An error has occurred. Please try again later'
quiz.configureFields.title_creation: 'Question Creation'
quiz.configureFields.question: 'Question Statement'
quiz.configureFields.question_placeholder: 'Enter question statement'
quiz.configureFields.question_delete: 'Do you really want to delete this question?'
rouletteWord.configureFields.statement: Statement
rouletteWord.configureFields.answer: Answer
rouletteWord.configureFields.type_0: 'Starts with letter'
rouletteWord.configureFields.type_1: 'Contains letter'
rouletteWord.configureFields.error.statement.max: 'The statement cannot exceed ${max} characters.'
rouletteWord.configureFields.error.statement.empty: 'The statement cannot be empty'
rouletteWord.configureFields.error.answer.max: 'The answer cannot exceed ${max} characters.'
rouletteWord.configureFields.error.answer.empty: 'The answer cannot be empty'
rouletteWord.configureFields.error.answer.starts: 'The answer must start with the letter'
rouletteWord.configureFields.error.answer.includes: 'The answer must contain the letter'
rouletteWord.response.update_letter: 'The data has been updated successfully'
rouletteWord.response.delete_letter: 'The data has been deleted successfully'
trueorFalse.configureFields.true: Correct
trueorFalse.configureFields.false: 'False'
enigma.configureFields.title_creation: 'Enigma Creation'
puzzle.configureFields.save_image: 'Image saved successfully'
puzzle.configureFields.select_correct_answer: 'You must select a correct answer'
puzzle.configureFields.recomendation: Recommendation
puzzle.configureFields.recomendation_dimentions: '<p>We recommend a minimum dimension of <span class="text-primary"><b>1024 pixels per side</b></span> and a maximum dimension of <span class="text-primary"><b>2000 pixels per side.</b></span></p>'
puzzle.configureFields.recomendation_description: '<p>The puzzle format is <span class="text-primary"><b>square</b></span>, so if we select an image with a different aspect ratio, such as a landscape image, the tool will allow us to crop by selecting the desired area.</p>'
hiddenword.configureFields.title: 'Secret Word Creation'
hiddenword.configureFields.answers_title: 'Secret Word'
hiddenword.configureFields.answers_placeholder: 'Enter secret word'
categorize.configureFields.title_group: 'Groups or Families'
fillgaps.configureFields.title: 'Phrase Creation'
fillgaps.configureFields.fillgap: Gap
fillgaps.configureFields.fillgaps: Gaps
fillgaps.configureFields.type_list: List
fillgaps.configureFields.type_drag: Drag
guesword.configureFields.word_title: 'Unscrambled Word'
guesword.configureFields.word_title_placeholder: 'Enter the word that will appear unscrambled'
guesword.configureFields.solution: Solution
guesword.configureFields.solution_placeholder: 'Enter the solution of the game'
guesword.configureFields.help_placeholder: 'Enter help for the game'
pairs.configureFields.title: 'Game Creation'
pairs.configureFields.placeholder_title: 'Enter the statement'
pairs.configureFields.create_game: 'Create Game'
chapter_type.description.22: '<p>The ideal solution for creating dynamic and engaging content in your courses or training modules. It presents information visually and offers a wide variety of interactions based on text, images, videos, audios, multimedia links, interactive cards, linked scenes, etc.</p>'
chapter_type.add.22: 'Create Vcms'
games.videoquiz_exist_question: 'A question with this time already exists'
chapter_type.22: VCMS
video.configureFields.title: 'Video Quiz Creation'
video.configureFields.add_question: 'Add Question'
Next: Next
hours: Hours
minutes: Minutes
seconds: Seconds
field_required: 'Field required'
field_invalid: 'Invalid field'
field_invalid_format: 'Invalid format'
remaining_characters: 'Remaining characters'
minimiun_characters: 'Minimum number of characters'
menu.home.title: 'Go to Campus'
course.season.type.sequential: Sequential
course.season.type.free: Free
course.season.type.exam: Exam
games.fillgap.add_fillgap: 'Add gaps by clicking on some words'
course_section.configureFields.hideCategoryName: 'Hide category name'
user.roles.super_administrator: SuperAdmin
settings.menu.label: Configuration
settings.header.title: Configuration
setting.menu.general: General
setting.menu.catalog: Catalogues
share: Share
report.announcement.participants: Participants
report.announcement.groupCode: 'Group code'
report.announcement.enterpriseProfile: 'Company profile'
report.announcement.file: File
report.announcement.totalStudents: 'Total students'
report.announcement.enterpriseCIF: 'Company VAT number'
report.announcement.advisor: Tutor
course.stats.started: Started
course.stats.ended: Finished
course.stats.total_time: 'Total time'
course.stats.avg_time: 'Average time'
course.stats.minutes: minutes
course.stats.minute: minute
course.stats.hours: hours
course.stats.hour: hours
course.stats.second: second
course.stats.seconds: seconds
question.configureFields.quantity_max_question: 'Maximum number of questions to be displayed:'
user.configureFields.available_courses: 'Available courses'
user.configureFields.available_chapter: 'Available chapter'
user.configureFields.courses_stats.finished: completed
user.configureFields.courses_stats.started: started
user.configureFields.courses_stats.available: available
user.configureFields.courses_stats.sent_messages: sent
user.configureFields.courses_stats.received_messages: received
user.configureFields.courses_stats.others: Others
user.configureFields.permissions: Permissions
course.configureFields.segments: Segments
stats.roles: Roles
course.configureFields.language: Language
game.feedback.wrong: 'Example: Wow!'
chapter_type.description.23: '<p>Role-play is an activity in which participants assume and act as fictional characters, often within a specific setting or context. During roleplay, participants temporarily adopt the personality, characteristics and behaviors of the characters they represent, interacting with each other according to the circumstances and the imaginary environment established. This practice is used in a variety of contexts, such as games, therapy, educational simulations and recreational activities, with the purpose of fostering creativity, empathy, problem solving and exploration of hypothetical situations.</p>'
password.uppercase: 'Required 1 or more characters in uppercase letters.'
password.number: '1 or more numeric digits required'
password.minimum: 'The password must have a minimum of %s characters.'
password.disable_3_consecutive_chars: 'It is not allowed to repeat a character more than 3 times in a row.'
password.lowercase: 'Required 1 or more lower case characters'
chapter_type.23: Role-play
chapter_type.add.23: 'Create role-play'
password.special_characters: '1 or more special characters required'
roleplay.status.failure: 'You have failed'
roleplay.status.success: 'You have passed'
user.configureFields.locale: Language
course.created: 'The course has been successfully saved'
question.configureFields.do_all_questions: 'Use all questions'
announcements.news.start_announcement: 'The course %course% is about to start!'
announcements.news.finish_announcement: 'The %course% is almost over!'
user.configureFields.dni: 'ID CARD'
course_section.configureFields.section_aditional: 'Additional training'
report.announcement.time_conexion: 'Connection time'
report.announcement.init_finish: 'Start and end'
report.annnouncement.conexions: Connections
report.annnouncement.chat_tutor: 'Chat with the tutor'
report.annnouncement.first_conexion: 'First connection'
report.annnouncement.last_conexion: 'Last connection'
generic_token.assistance.success: 'Tu asistencia se ha registrado correctamente'
generic_token.assistance.user_not_in_group: 'User does not belong to the session group'
chat.notification.number_of_messages: 'You have %s unread message(s)'
certificate.notification.available: 'Diploma of the %s course convocation available for downloading'
user_fields_fundae.title: 'Additional fields FUNDAE'
user_fields_fundae.social_security_number: 'Social Insurance Number'
user_fields_fundae.gender: Gender
user_fields_fundae.email_work: 'Work mail'
user_fields_fundae.birthdate: 'Date of birth'
user_fields_fundae.dni: 'ID CARD'
user_fields_fundae.contribution_account: 'Contribution account'
user_fields_fundae.incapacity: Incapacity
user_fields_fundae.victim_of_terrorism: 'Victim of terrorism'
user_fields_fundae.gender_violence: 'Victim of gender-based violence'
fundae_assistance_template.main_title: 'TRAINING ATTENDANCE CONTROL'
fundae_assistance_template.action_type: 'NAME OF THE TRAINING ACTION'
fundae_assistance_template.action_code: 'ACTION CODE'
fundae_assistance_template.group: GROUP
fundae_assistance_template.start_at: 'START DATE'
fundae_assistance_template.finish_at: 'END DATE'
fundae_assistance_template.main_formation_teacher: 'TRAINER/ TRAINING MANAGER'
fundae_assistance_template.session_number: 'SESSION NO.'
fundae_assistance_template.date: DATE
fundae_assistance_template.morning_afternoon: 'MORNING/ TODAY'
fundae_assistance_template.signed: Signed
fundae_assistance_template.info_signed_person: 'Trainer/Training Rep.'
fundae_assistance_template.assistance_data: 'Attendee data'
fundae_assistance_template.signatures: SIGNATURES
fundae_assistance_template.observations: OBSERVATIONS
fundae_catalogs.main_page.title: 'FUNDAE Catalogues'
fundae_catalogs.user_company.label_in_plural: Companies
fundae_catalogs.user_company.label_in_singular: Company
fundae_catalogs.user_professional_category.label_in_plural: 'Professional categories'
fundae_catalogs.user_professional_category.label_in_singular: 'Professional category'
fundae_catalogs.user_study_level.label_in_plural: 'Education levels'
fundae_catalogs.user_study_level.label_in_singular: 'Level of education'
fundae_catalogs.user_work_center.label_in_plural: Workplaces
fundae_catalogs.user_work_center.label_in_singular: Workplace
fundae_catalogs.user_work_department.label_in_plural: 'Working departments'
fundae_catalogs.user_work_department.label_in_singular: 'Department of Labour'
fundae_catalogs.fields.state.title: State
fundae_catalogs.fields.state.active: Active
fundae_catalogs.fields.state.inactive: Inactive
excel.userAnnouncement.sheet1.title: 'General Info'
excel.userAnnouncement.sheet1.colum1: 'Number of Itineraries'
excel.userAnnouncement.sheet1.colum2: 'Number of Users who have itineraries'
excel.userAnnouncement.sheet1.colum3: 'Number of courses in the pathways'
excel.userAnnouncement.sheet2.title: 'Catalogue Itineraries'
excel.userAnnouncement.sheet2.colum1: Id
excel.userAnnouncement.sheet2.colum2: 'Name Itineraries'
excel.userAnnouncement.sheet2.colum3: Division
excel.userAnnouncement.sheet2.colum4: Category
excel.userAnnouncement.sheet2.colum5: 'Courses assigned'
excel.userAnnouncement.sheet2.colum6: 'Assigned persons'
excel.userAnnouncement.sheet2.colum7: 'People have completed itinerary'
excel.userAnnouncement.sheet2.colum8: 'People in Process'
excel.userAnnouncement.sheet2.colum9: 'People not started'
excel.userAnnouncement.sheet2.colum10: 'TOTAL ACCUMULATED TIME'
excel.userAnnouncement.sheet2.colum11: 'AVERAGE PERSON TIME'
excel.userAnnouncement.sheet3.title: 'Itinerary Courses'
excel.userAnnouncement.sheet3.colum1: Id
excel.userAnnouncement.sheet3.colum2: 'Name Itineraries'
excel.userAnnouncement.sheet3.colum3: 'Name Course'
excel.userAnnouncement.sheet3.colum4: Completed
excel.userAnnouncement.sheet3.colum5: 'In progress'
excel.userAnnouncement.sheet3.colum6: 'Not started'
course.message_saved: 'Course saved'
chapter.configureFields.create_chapter: 'Create chapter'
user_filter.assign_manual: 'Assign manually'
user_filter.assign_filters: 'Assign by filters'
user.configureFields.configureLocale: 'Set up languages'
user.configureFields.configureLocaleAdmin: 'Set language for the administration panel'
user.configureFields.configureLocaleCampus: 'Set language for campus'
stats.export.configsheet.title: Configuration
stats.export.configsheet.content_title: 'General Statistics Report'
stats.export.configsheet.content_period: 'Period Encompassed (End Date)'
stats.export.configsheet.content_filters: 'Active Filters'
stats.export.configsheet.content_period_from: From
stats.export.configsheet.content_period_to: To
stats.export.datasheet.title: Data
stats.export.filter.category: Category
stats.export.filter.departament: Department
stats.export.filter.gender: Genre
stats.export.filter.activeUsers: Users
stats.export.filter.activeUsers_val_yes: Active
stats.export.filter.activeUsers_val_no: Inactive
stats.export.filter.course_full_title: '100% of the course'
stats.export.filter.course_full_val_yes: 'Yes'
stats.export.filter.course_full_val_no: 'No'
stats.export.filter.course_full_descr: '(Include only courses that have been completed in the indicated period)'
stats.export.filter.course_intime_title: 'Course life in the period'
stats.export.filter.course_intime_val_yes: 'Yes'
stats.export.filter.course_intime_val_no: 'No'
stats.export.filter.course_intime_descr: '(Include only courses that have started and finished within the indicated period)'
stats.export.filter.course_started_in_period_title: 'Course started in the date range'
stats.export.filter.course_started_in_period_val_yes: 'Yes'
stats.export.filter.course_started_in_period_val_no: 'No'
stats.export.filter.course_finished_in_period_title: 'Course completed in the date range'
stats.export.filter.course_finished_in_period_val_yes: 'Yes'
stats.export.filter.course_finished_in_period_val_no: 'No'
stats.export.filter.customFilters: Personalized
stats.content_allusers: 'All users'
stats.content_inactive: Inactive
stats.content_inactive_f: Inactive
itinerary.user.assign_manual: 'Assign manually'
itinerary.user.assign_filter: ' Assign by filters'
itinerary.user.modify_users: 'Modify people'
itinerary.user.filter_find_by: 'Search by'
common_areas.close: Close
itinerary.chart.avg_time_active: '(Total Assets)'
itinerary.chart.avg_time_all: '(Total Assigned)'
itinerary.courses.modify: 'Assign courses'
itinerary.courses.appliedfilter: ' course/s shown (filtered by'
itinerary.users.appliedfilter: ' person/s shown (filtered by'
itinerary.courses.available: 'Courses available'
excel.userAnnouncement.sheet1.colum2b: 'Number of single users who have itineraries'
excel.userAnnouncement.sheet1.colum3b: 'Number of unique courses in the itineraries'
common_areas.select_choice: 'Select an option'
chapter_type.24: Lti
chapter_type.description.24: Lti
lti_chapter.title: 'Chapter LTI'
lti_chapter.add: 'Add LTI chapter'
lti_chapter.edit: 'Edit LTI chapter'
lti_chapter.identifier: 'LTI Identifier'
lti_chapter.identifier_required: 'LTI identifier required'
chapter_type.add.24: 'Add LTI chapter'
categoryFilter.label: 'Category Filter'
categoryFilter.title: 'Category Filter'
user.configureFields.localeCampus: 'Campus language'
global.bulk.sheetValidation.error_tab_1: 'The first tab is not named "Training List".'
global.bulk.sheetValidation.error_tab_2: 'The second tab is not named "Participants".'
stats.export.user_creation: 'Filter users by creation date'
stats.export.users_export_title: 'User statistics'
chapter_type.validation_course: "\nThe chapter cannot be deleted because some users have already registered activity in it."
user.configureFields.courses_stats.notstarted: unstarted
course.configureFields.created_at: 'Date of creation'
course.configureFields.translate: Translate
messages.configureFields.timezone: 'Time zone'
user.email: Email
course.diploma.index: 'Personalized diplomas'
menu.stats.reports.diplomas: 'Reports and Diplomas'
itinerary.succes.download: 'The itinerary report is being processed and you can find it in "Reports and diplomas"'
user.diploma.generate: 'Generate diplomas'
filters.placeholder: 'Type search'
filters.remove_all: 'Remove all'
filters.add_all: 'Add all'
announcement.report_group_resume_individual: 'Individual executive summary'
announcement.report_downloaded_diploma: 'Downloaded diploma'
announcements.configureFields.code: 'Name of the call'
task.status.review: 'Under review'
task.status.error: 'System error'
email.error.subject: 'Error in %context% (ID: %id%) - Environment: %appName%'
email.error.subject_no_id: 'Error in %context% - Environment: %appName%'
email.error.title: 'Task Execution Error'
email.error.environment: Environment
email.error.context: Context
email.error.task_id: 'Task ID'
email.error.error_details: 'Error Details'
email.error.error_message: 'Error Message'
email.error.error_line: Line
email.error.error_file: File
email.error.additional_info: 'Additional Information'
email.error.regards: 'Best regards'
email.error.team: 'The %appName% team'
email.zombie.subject: 'Task in ZOMBIE state in %context% (ID: %id%) - Environment: %appName%'
email.zombie.title: 'ZOMBIE Status Task Notification'
email.zombie.environment: Environment
email.zombie.context: Context
email.zombie.task_id: 'Task ID'
email.zombie.marked_as: 'has been marked as'
email.zombie.zombie_status: ZOMBIE
email.zombie.timeout_reason: 'because it exceeded the allowed execution time'
email.zombie.check_details: 'Please check the admin panel or console for more details and take appropriate actions'
email.zombie.additional_info: 'Additional Information'
email.zombie.regards: 'Best regards'
email.zombie.team: 'The %appName% team'
season.delete: 'It is not possible to delete this season, it currently has linked chapters'
delete.season.chapters.users: "Cannot delete season %seasonName%,\nchapters (%ChaptesTitles%) have user activity"
delete.season.danger: 'The season cannot be eliminated'
stats.task.queued: 'Request for glued Task'
itinerary.delete.confirm.validation: 'The itinerary is currently active, to proceed with the action you must disable the itinerary'
itinerary.delete.confirm.title: 'Do you really want to delete the itinerary?'
course.publish.message.active: 'Published course'
course.publish.message.unactive: 'Course marked as unpublished'
itinerary.delete.error: 'The itinerary cannot be deleted.'
courser.chaperts.orders.succes: 'Chapter order successfully updated'
course.publish.message.unactive.chapters: 'Cannot be published because the course is incomplete'
course.undelete.message: 'The course cannot be deleted because it has assigned content'
user.roles.creator: Creator
