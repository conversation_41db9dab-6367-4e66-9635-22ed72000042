menu.title_platform: Ausbildungs-Campus
complete: Abgeschlossen
Show: Anzeigen
Edit: Bearbeiten
Remove: Entfernen
Delete: Löschen
total: Insgesamt
'Yes': Ja
'No': Nein
Actions: Aktionen
Clear: Deutlich
'No results found': 'Keine Ergebnisse gefunden'
Configuration: Konfiguration
Limit: Grenze
Close: Schließen
Save: Speichern
'Save and create other': 'Erstellen und Hinzufügen eines weiteren'
'Save changes': 'Änderungen speichern'
'Save and keep editing': 'Speichern und weiter bearbeiten'
state: Staat
create: Erstellen
cancelar: Abbrechen
back: Zurück
add: Hinzufügen
no_content: 'Kein Inhalt'
no_result: 'Es wurden keine Ergebnisse gefunden'
configure_simulator: 'Simulator konfigurieren'
edit_configure_simulator: 'Konfiguration bearbeiten'
configure_success: 'Die Konfiguration war erfolgreich'
save_success: 'Registrierung erfolgreich gespeichert'
error_success: 'Beim Speichern des Datensatzes ist ein Fehler aufgetreten'
configure_completed: 'Konfiguration abgeschlossen'
'Created At': Erstellt
'Created By': 'Erstellt von'
'Created by': 'Erstellt von'
'Updated At': Aktualisiert
'Updated By': 'Aktualisiert durch'
'Deleted By': 'Gelöscht von'
'Deleted At': Gelöscht
menu.courses_managment.title: Kursverwaltung
menu.courses_managment.Segments: Segmente
menu.courses_managment.categories: Kategorien
menu.courses_managment.level: Ebenen
menu.courses_managment.courses: Kurse
menu.courses_managment.announcements: Ankündigungen
menu.courses_managment.nps_question: 'Nps Frage'
menu.courses_managment.opinion_course: Meinungskurs
menu.help_managment.title: Hilfe-Management
menu.help_managment.content_help: 'Inhaltliche Hilfe'
menu.help_managment.categories_help: 'Hilfe zur Kategorie'
menu.users_managment.title: Benutzerverwaltung
menu.users_managment.users: Benutzer
menu.users_managment.managers: Manager
menu.users_managment.filter: Filter
menu.news.title: 'E-Mail an'
menu.stats.title: Statistiken
menu.stats.export: Excel-Tool
menu.users.edit_profile: 'Profil bearbeiten'
form.label.delete: Löschen
action.save: 'Jetzt speichern'
common_areas.created_at: 'Erstellt am'
common_areas.updated_at: 'Aktualisiert am'
common_areas.deleted_at: 'Gelöscht am'
common_areas.created_by: 'Erstellt von'
common_areas.updated_by: 'Aktualisiert durch'
common_areas.actions: Aktionen
common_areas.basic_information: 'Grundlegende Informationen'
common_areas.edit: Bearbeiten
common_areas.delete: Löschen
common_areas.name: Name
common_areas.image: Imate
common_areas.state: Staat
common_areas.create: Erstellen
common_areas.save: Speichern
common_areas.back_list: 'Zurück zur Liste'
course_category.label_in_singular: 'Kategorie des Kurses'
course_category.label_in_plural: 'Kategorien der Kurse'
course_category.configureFields.category_name: 'Name Kategorie'
course_category.configureFields.category_order: 'Bestellung Kategorie'
course_category.configureFields.translations: Übersetzungen
course.label_in_singular: Kurs
course.label_in_plural: Kurse
course.back_to_course: 'Zurück zum Kurs'
course.configureFields.basic_information: 'Grundlegende Informationen'
course.configureFields.code: Code
course.configureFields.name: Name
course.configureFields.description: Beschreibung
course.configureFields.basic: Grundlegend
course.configureFields.access_level: Zugangsebene
course.configureFields.clone: Klonen
course.configureFields.open: Öffnen
course.configureFields.open_visible: 'Sichtbar in Open Campus'
course.configureFields.active: Aktiv
course.configureFields.categories: Kategorien
course.configureFields.profesional_categories: 'Berufliche Kategorien'
course.configureFields.image: Bild
course.configureFields.chapter: Kapitel
course.configureFields.translation: Übersetzung
course.configureFields.general_information: 'Allgemeine Informationen'
course.configureFields.segment: Segment
course.configureFields.category: Kategorie
course.configureFields.thumbnail_url: 'Miniaturansicht Url'
course.configureFields.locale: Standort
course.configureFields.all_seasons: 'Alle Saisonen'
course.configureFields.chapters: Kapitel
course.configureFields.seasons: Saisons
course.configureFields.courses_translate: Übersetzungen
course.configureFields.add_chapter: 'Kapitel hinzufügen'
course.configureFields.no_seasons: 'Keine Saison'
course.configureFields.add_seasons: 'Saison hinzufügen'
course.configureFields.add_annuncement: 'Anruf hinzufügen'
course.configureFields.question_modal_translate: 'Wollen Sie diesen Kurs wirklich übersetzen?'
course.configureFields.content_modal_translate: 'Mit dieser Aktion wird eine Kopie des Kurses erstellt, die als Leitfaden für die Übersetzung in eine andere Sprache verwendet werden kann.'
course.configureFields.translate_already: 'Dieser Kurs hat bereits eine Übersetzung in diese Sprache'
course.configureFields.tag_description: 'Tags mit Enter trennen'
course.configureFields.new: Neu
course.configureFields.add_material: 'Material hinzufügen'
course.configureFields.add_task: 'Aufgabe hinzufügen'
course.configureFields.task: Aufgabe
course.season_add: 'Die Saison wurde korrekt hinzugefügt'
course.season_update: 'Die Saison wurde korrekt aktualisiert'
course.season_add_error: 'Beim Hinzufügen der Saison ist ein Fehler aufgetreten'
course.panel.class: 'Details zum Kurs'
chapter.label_in_plural: Kapitel
chapter.configureFields.title: Titel
chapter.configureFields.course: Kurs
chapter.configureFields.type: Typ
chapter.configureFields.season: Saison
chapter.configureFields.description: Beschreibung
chapter.configureFields.image: Bild
chapter.configureFields.image_file: 'Bild Datei'
chapter_type.description.1: '<p>Das Scorm-Kapitel ist sehr interessant:</p><p>Es ermöglicht uns, eine breite Palette von Inhalten hochzuladen, die mit anderen Tools erstellt wurden, zum Beispiel Dokumente, interaktive Inhalte und sogar Spiele.</p>'
chapter_type.description.2: '<p>Dies ist eines der vielseitigsten Kapitel.</p><p>Auf der linken Seite werden die eingegebenen Titel angezeigt, die als Index dienen, um Inhalte schnell zu finden und das Lesen zu erleichtern.</p>'
chapter_type.description.3: '<p>Es handelt sich um ein Quizspiel, bei dem eine Zufallskomponente hinzukommt, da man die Segmente eines Roulette-Rades vervollständigen muss, um es zu bestehen.</p><p>Es basiert auf der Erstellung einer Batterie von Fragen, um das gelernte Wissen zu festigen. Sie können so viele Fragen eingeben, wie Sie wollen, und diese auch mit Bildern versehen.</p><p>Damit das Spiel richtig funktioniert, ist es ratsam, mindestens 10 Fragen aufzunehmen.</p>'
chapter_type.description.4: '<p>Dieses Spiel besteht aus einer Reihe von Fragen, die einen zusätzlichen Risikofaktor enthalten. Nach jeder Frage haben die Teilnehmer die Möglichkeit, stehen zu bleiben und den aktuellen Punktestand zu behalten oder das Risiko einzugehen, eine zusätzliche Frage zu beantworten, um mehr Punkte zu erhalten. Bei einer falschen Antwort verfallen jedoch alle bis dahin gesammelten Punkte.</p>'
chapter_type.description.5: '<p>Dies ist das klassischste Spielkapitel.</p><p>Die Idee ist, eine Batterie von Fragen zu erstellen, um das gelernte Wissen zu festigen. Sie können beliebig viele Fragen eingeben, die von einem Bild begleitet werden und auf die es nur eine richtige Antwort gibt.</p>'
chapter_type.description.6: '<p>Sortieren und drehen Sie die Teile, bis sie in der richtigen Position und Ausrichtung sind, sonst passen sie nicht zusammen.</p><p>Oben gibt es vier Segmente, die der Zeit entsprechen, die für die Vervollständigung des Puzzles zur Verfügung steht. Wenn ein Zeitabschnitt abläuft, wird eine der eingegebenen Fragen gestellt. Wer die Fragen richtig beantwortet, hat mehr Zeit, das Rätsel zu lösen. Die endgültige Punktzahl ergibt sich aus einer Kombination aus der für das Rätsel selbst benötigten Zeit, der Anzahl der richtig beantworteten Fragen und der Anzahl der ausgelassenen Fragen.</p>'
chapter_type.description.7: '<p>Dieses Spiel stellt ein auf einem Bild basierendes Rätsel dar. Um es zu lösen, musst du die richtigen Buchstaben sorgfältig auswählen, bevor die Zeit abläuft.</p>'
chapter_type.description.8: '<p>Da das PDF-Format für verschiedene Arten von Inhalten, wie Protokolle oder Handbücher, weit verbreitet ist, sind PDF-Kapitel sehr interessant, da sie die Wiederverwendung von bereits veröffentlichtem Material ermöglichen.</p> '
chapter_type.description.9: '<p>Audiovisuelle Ressourcen haben ein großes pädagogisches Potenzial, sie ziehen an, fesseln die Aufmerksamkeit und wecken die Neugier.</p><p>Die Plattform ermöglicht es uns, zu wählen, wie wir das Video über "url" einführen, oder indem wir eine Datei auf unserem Computer auswählen. Im letzteren Fall können wir eine Untertiteldatei anhängen.</p>'
chapter_type.description.10: '<p>Schiebereglerartiges Kapitel mit Bildern.</p> '
chapter_type.description.11: '<p>Das Spiel besteht darin, die Wörter zu finden, von denen jedes einem Buchstaben auf dem Rad entspricht. Manchmal ist die Lösung ein Wort, das mit dem Buchstaben beginnt, und manchmal enthält sie einfach den Buchstaben.</p>'
chapter_type.description.12: '<p>In diesem Spiel werden Ihnen eine Reihe von Fragen in Form von Aussagen in Text, Bild oder einer Kombination aus beidem gestellt. Es gibt zwei mögliche Antworten "Richtig" oder "Falsch" und nur eine ist richtig. Die Zeit, um das Spiel zu lösen, ist begrenzt.</p>'
chapter_type.description.13: '<p>In diesem Spiel müssen Sie ein Rätsel lösen, bevor die Zeit abläuft. Der Hinweis ist hinter einem unscharfen Bild versteckt, das im Laufe des Spiels immer deutlicher wird. Neben dem Bild gibt es auch eine zusätzliche Hilfe in Form von Text.</p> <p>Zusätzlich zum Bild gibt es auch eine zusätzliche Hilfe in Form von Text.</p>'
chapter_type.description.14: '<p>In diesem klassischen Spiel müssen Sie die Elemente anordnen, indem Sie die Blöcke in die richtige Reihenfolge ziehen. Die Vielfalt der Möglichkeiten macht es ideal für mathematische Übungen und andere pädagogische Herausforderungen. Ideal, um einen Test zu erstellen, der das logische Denken und die Ordnungsfähigkeit herausfordert.</p>'
chapter_type.description.15: '<p>Dieses Spiel ist ideal, um Gedächtnis und Konzentration zu trainieren. Das Ziel ist es, alle Paare von passenden Karten zu finden. Die Lage der Karten wird nach dem Zufallsprinzip erstellt, so dass jedes Spiel anders sein wird.</p>'
chapter_type.description.16: '<p>In diesem Spiel wird Ihnen eine Reihe von Wörtern, Sätzen oder Begriffen vorgelegt, die Sie der entsprechenden Familie oder Gruppe zuordnen müssen, die unten abgebildet ist. Sie werden Ihre Assoziationsfähigkeiten und Ihre geistige Schnelligkeit testen, während Sie gegen die Zeit antreten.</p>'
chapter_type.description.17: '<p>In diesem Grammatik- und Lernspiel geht es darum, die Lücken in Sätzen mit den richtigen Wörtern zu füllen, um deine Sprach- und Grammatikkenntnisse zu testen. Aber das ist noch nicht alles! Dieses Spiel ist vielseitig und kann für viele andere pädagogische Zwecke verwendet werden.</p>'
chapter_type.description.18: '<p>In diesem Spiel wird Ihnen eine Frage oder ein Rätsel gestellt, das Sie lösen müssen. Die Aufgabe besteht darin, das Rätsel sorgfältig zu untersuchen und mit Hilfe der vorgegebenen Buchstaben das richtige Wort zu finden. Aber seien Sie vorsichtig, denn die Zeit ist begrenzt, was bedeutet, dass Sie schnell und genau sein müssen, um zu gewinnen.</p>'
chapter_type.description.19: '<p>In diesem Spiel müssen Sie ein verstecktes Wort in maximal sechs Versuchen erraten. Jeder Versuch besteht darin, ein gültiges Wort einzugeben. Nach jedem Versuch ändert sich die Farbe der Quadrate, um zu zeigen, welche Buchstaben richtig sind und welche sich auch an der richtigen Stelle befinden.</p>'
chapter_type.description.20: '<p>Bei diesem Spiel geht es darum, versteckte Wörter in einer Wortsuche zu finden. Ziel ist es, eine Folge von Buchstaben horizontal, vertikal oder diagonal zu markieren. Die Wörter können in beide Richtungen, von links nach rechts oder von rechts nach links, gefunden werden. Wenn die Folge Teil eines versteckten Wortes ist, wird sie als richtige Antwort gewertet.</p>'
chapter_type.description.21: '<p>Während der Wiedergabe eines Videos werden interaktive Fragen eingefügt, die vom Betrachter verlangen, dass er sich mit dem Inhalt des Videos befasst, um richtig zu antworten. Kurz gesagt, die Kombination von Video mit der Interaktivität eines Quiz bietet eine effektive und fesselnde Lernerfahrung.</p>'
chapter_type.add.1: 'Schnecke hinzufügen'
chapter_type.add.2: 'Inhalt hinzufügen'
chapter_type.add.3: 'Roulette hinzufügen'
chapter_type.add.4: 'Spiel hinzufügen'
chapter_type.add.5: 'Fragebogen hinzufügen'
chapter_type.add.6: 'Rätsel hinzufügen'
chapter_type.add.7: 'Verstecktes Wort hinzufügen'
chapter_type.add.8: 'PDF hinzufügen'
chapter_type.add.9: 'Buchstabensuppe hinzufügen'
chapter_type.add.10: Agregar-Schieberegler
chapter_type.add.11: 'Roulette hinzufügen'
chapter_type.add.12: 'Spiel hinzufügen'
chapter_type.add.13: 'Spiel hinzufügen'
chapter_type.add.14: 'Spiel hinzufügen'
chapter_type.add.15: 'Spiel hinzufügen'
chapter_type.add.16: 'Kategorisierung hinzufügen'
chapter_type.add.17: 'Hinzufügen von Lücken'
chapter_type.add.18: 'Spiel hinzufügen'
chapter_type.add.19: 'Spiel hinzufügen'
chapter_type.add.20: 'Buchstabensuppe hinzufügen'
chapter_type.add.21: 'Video-Quiz hinzufügen'
chapter_type.all: Alle
chapter_type.content: Inhalt
chapter_type.games_test: 'Gamifizierte Prüfung'
chapter_type.description_test: 'Beschreibung des Tests'
chapter_type.type: 'Kapitel Typ'
chapter.add_pdf: 'pdf hinzufügen'
chapter.chapter.show_video: 'Video ansehen'
chapter.message_pdf_success: 'Das pdf wurde erfolgreich hinzugefügt'
chapter.message_pdf_error: 'Beim Speichern der PDF-Datei ist ein Fehler aufgetreten'
chapter.chapter.materials: Materialien
chapter.chapter.show_pdf: 'Ansicht pdf'
announcements.label_in_singular: Ankündigungen
announcements.label_in_plural: Ankündigungen
announcements.configureFields.courses: Kurse
announcements.configureFields.start_at: 'Start bei'
announcements.configureFields.finish_at: 'Beendet am'
announcements.configureFields.called: Aufgerufen
announcements.configureFields.subsidized: Subventioniert
announcements.configureFields.subsidizer: Subvention
announcements.configureFields.subsidizer_entity: 'Subventionierende Einrichtung'
announcements.configureFields.subsidized_announcement: 'Ankündigungen von Zuschüssen'
announcements.configureFields.max_users: 'Maximale Benutzer'
announcements.configureFields.formative_action_type: 'Art der Ausbildungsmaßnahme'
announcements.configureFields.format: Format
announcements.configureFields.total_hours: 'Stunden insgesamt'
announcements.configureFields.place: Ort
announcements.configureFields.training_center: Ausbildungszentrum
announcements.configureFields.training_center_address: 'Adresse der Ausbildungsstätte'
announcements.configureFields.training_center_nif: 'Ausbildungszentrum NIF'
announcements.configureFields.training_center_phone: 'Telefonnummer des Schulungszentrums'
announcements.configureFields.training_center_email: 'E-Mail Adresse des Schulungszentrums'
announcements.configureFields.training_center_teacher_dni: 'Lehrerfortbildungszentrum DNI'
announcements.configureFields.called_user: 'Benutzer genannt'
announcements.configureFields.search: Suche
announcements.configureFields.announcement_for: 'Ankündigungen für'
announcements.configureFields.search_user_title: 'Suche nach Benutzern, die Ankündigungen aufrufen'
announcements.configureFields.placeholder_search_user: 'Suche nach Benutzern'
announcements.configureFields.placeholder_search_category: 'Suche nach Kategorie'
announcements.configureFields.placeholder_search_department: 'Suche nach Abteilung'
announcements.configureFields.placeholder_search_center: 'Suche nach Zentrum'
announcements.configureFields.placeholder_search_country: 'Suche nach Land'
announcements.configureFields.placeholder_search_division: 'Suche nach Abteilung'
announcements.configureFields.result_found: 'Gefundene Ergebnisse'
announcements.configureFields.clear_result: 'Klare Ergebnisse'
announcements.configureFields.error_already_called_user: 'Fehler: Der Benutzer wurde bereits aufgerufen!'
announcements.configureFields.error_already_called_user_date: 'Fehler: Der Benutzer wurde bereits aufgerufen, in einem ähnlichen Datumsbereich!'
announcements.configureFields.notified: Angemeldet
announcements.configureFields.content_course: 'Inhalt des Kurses'
announcements.configureFields.report: Bericht
announcements.configureFields.title_report: 'Bericht der Studenten'
announcements.configureFields.direction: Adresse
announcements.configureFields.telephone: Telefon
announcements.configureFields.nif: NIF
announcements.configureFields.tutor: Nachhilfelehrer
announcements.configureFields.apt: Apt
announcements.configureFields.time_total: Gesamtdauer
question.label_in_singular: Frage
question.label_in_plural: Fragen
question.configureFields.question: Frage
question.configureFields.random: Zufällig
question.configureFields.answers: Antworten
question.configureFields.image_file: Bilddatei
question.configureFields.question_for: 'Fragen für'
question.configureFields.image_for: 'Bild für'
question.configureFields.add_image_puzzle: 'Rätselbild hinzufügen'
question.configureFields.add_question: 'Frage hinzufügen'
question.configureFields.see_image: 'Bild anzeigen'
content.label_in_singular: Inhalt
content.label_in_plural: Inhalte
content.configureFields.title: Titel
content.configureFields.content: Inhalt
content.configureFields.position: Position
content.configureFields.add_content: 'Inhalt hinzufügen'
content.configureFields.content_for: 'Inhalt für'
question_nps.label_in_singular: 'Nps Frage'
question_nps.label_in_plural: 'Nps Fragen'
question_nps.configureFields.type: Typ
question_nps.configureFields.position: Position
question_nps.configureFields.question: Frage
question_nps.configureFields.course: Kurs
question_nps.configureFields.name_question: 'Name Frage'
question_nps.configureFields.translations: 'Übersetzungen '
opinions.label_in_singular: Meinung
opinions.label_in_plural: Meinungen
opinions.configureFields.course: Kurs
opinions.configureFields.question: Frage
opinions.configureFields.to_post: Beitrag
opinions.configureFields.value: Wert
opinions.configureFields.valoration: Wertschätzung
help_category.label_in_singular: 'Kategorie Hilfe'
help_category.label_in_plural: 'Kategorien Hilfe'
help_category.configureFields.category_name: 'Name der Kategorie'
help_category.configureFields.translations: Übersetzungen
help_text_content.label_in_singular: 'Inhaltliche Hilfe'
help_text_content.label_in_plural: 'Inhaltliche Hilfe'
help_text_content.configureFields.category: Kategorie
help_text_content.configureFields.title: Titel
help_text_content.configureFields.text: Text
help_text_content.configureFields.translations: Übersetzungen
user.label_in_singular: Benutzer
user.label_in_plural: Usersenutzer
user.configureFields.division: Abteilung
user.configureFields.country: Land
user.configureFields.category: Kategorie
user.configureFields.departament: Departament
user.configureFields.center: Zentrum
user.configureFields.gender: Geschlecht
user.configureFields.first_name: Name
user.configureFields.last_name: Nachname
user.configureFields.code: Code
user.configureFields.password: Passwort
user.configureFields.change_password: 'Passwort ändern'
user.configureFields.courses: Kurse
user.configureFields.extra: Extra
user.configureFields.announcements: Ankündigungen
user.configureFields.extra_fields: 'Zusätzliche Felder'
user.configureFields.avatar_image: Profilbild
user.configureFields.new_password: 'Neues Passwort'
user.configureFields.birthdate: Geburtsdatum
user.configureFields.edit_user: 'Benutzer bearbeiten'
user.configureFields.user_data: Benutzerdaten
user.configureFields.stats: Statistiken
user.configureFields.chapter: Kapitel
user.configureFields.ratio_course: 'Verhältnis Kurs/Personen'
user.configureFields.avg_stars: 'Durchschnittliche Sterne'
user.configureFields.time: Zeit
user.configureFields.chapter_time: 'Zeitaufwand pro Kapitel'
user.configureFields.available: verfügbar
user.configureFields.messages: Nachrichten
user.configureFields.login_history: Login-Verlauf
user.configureFields.started_at: Start
user.configureFields.finished_at: Fertiggestellt
user.configureFields.time_spent: 'Verbrachte Zeit'
user.configureFields.content_viewed: 'Gesehene Inhalte'
user.configureFields.interaction_with_teacher: 'Interaktionen mit der Lehrkraft'
user.configureFields.course_content: 'Inhalt des Kurses'
user.configureFields.content_type: 'Art des Inhalts'
user.configureFields.finished: Fertiggestellt
user.configureFields.teacher_interaction: 'Interaktionen mit Lehrkräften'
user.configureFields.date: Datum
user.configureFields.sender: Sender
user.configureFields.recipient: Empfänger
user.configureFields.subject: Thema
user.configureFields.questions: Fragen
user.configureFields.chapter_type: 'Art des Kapitels'
user.configureFields.finished_chapter_types: 'Abgeschlossene Kapiteltypen'
user.configureFields.button_validate: Validieren
user.configureFields.open: 'Offener Campus'
user.configureFields.computer: Computer
user.configureFields.mobile: Telefon
user.configureFields.tablet: Tablet
user.manage.assign_data: 'Daten zuweisen'
user.gender.m: Männlich
user.gender.f: Weiblich
user.configureFields.time_title: 'Hingabe pro Tag'
user.configureFields.interaction_in_forum: 'Interaktionen im Forum'
user.configureFields.email: E-Mail
user.configureFields.fullname: 'Vorname und Nachname'
user.filtersRequired: 'Mindestens einen Filter auswählen'
stats.general_stats: 'Allgemeine Statistik'
stats.total_times_spent: 'Insgesamt aufgewendete Zeit'
stats.users_activity: 'Aktivität der Benutzer'
stats.users_active_last_30: 'Aktiv in den letzten 30 Tagen'
stats.users_inactive_last_30: 'Inaktiv in den letzten 30 Tagen'
stats.users_never_login: 'Niemals anmelden'
stats.daily_chapter: 'Abgeschlossene tägliche Kapitel'
stats.finished_chapters: 'Abgeschlossene Kapitel'
stats.daily_course: 'Abgeschlossene tägliche Kurse'
stats.finished_courses: 'Abgeschlossene Kurse'
stats.daily_login: 'Tägliche Anmeldung'
stats.daily_login_tooltip: Anmeldungen
stats.all_courses: 'Alle Kurse'
stats.all_countries: 'Alle Länder'
stats.all_centers: 'Alle Zentren'
stats.all_categories: 'Alle Kategorien'
stats.all_departament: 'Alle Bereiche'
stats.all_gender: 'Alle Geschlechter'
stats.all_divisions: 'Alle Abteilungen'
stats.filters: Filter
stats.filter_by: 'Filtern nach'
stats.modal_close: Schließen
stats.clear_filters: 'Clear filters'
stats.apply_filters: 'Filter anwenden'
stats.export_title: 'Daten exportieren'
stats.export.start_date: 'Datum des Beginns'
stats.export.end_date: 'Datum des Endes'
stats.export.filename: 'Name der Datei'
stats.export.request_date: 'Datum der Anmeldung'
stats.export.available_until: 'Verfügbar bis'
stats.export.loading_data: 'Laden von Daten'
stats.export.no_data: 'Keine Daten verfügbar'
stats.export.download_file: 'Datei herunterladen'
stats.export.abort_export_request: 'Exportantrag abbrechen'
stats.export.view_details: 'Details anzeigen'
stats.export.reset_form: 'Formular zurücksetzen'
stats.export.error_start_date: 'Das Anfangsdatum kann nicht nach dem Enddatum liegen.'
stats.export.export_error: 'Bei der Erstellung des Berichts ist ein Fehler aufgetreten'
stats.export.export_success: 'Der Bericht wurde erfolgreich in die Download-Warteschlange aufgenommen.'
stats.export.export_dir: 'Statistiken / Excel-Tool'
stats.devices_login: 'Anmeldung des Geräts'
stats.distribution_ages: Altersverteilung
stats.generation_babyboom: BabyBoom
stats.generation_x: 'Generation X'
stats.generacion_milenials: Millennials
stats.generacion_z: 'Generation Z'
stats.title_information_user: 'Informationen über Benutzer'
stats.title_information_content: 'Informationen zum Inhalt'
stats.title_information_courses: 'Informationen über Kurse'
stats.title_information_chapter: 'Informationen zum Kapitel'
stats.distribution_country: 'Verteilung nach Ländern'
stats.title_finish_m: beendet
stats.title_made: gemacht
stats.title_made_f: gemacht
stats.chapter_day: Tag
stats.chaper_hours: Stunden
stats.chapter_minutes: Minuten
stats.chapter_total: GESAMT
stats.chapter_media: HÄLFTE
stats.content_active: aktiviert
stats.content_active_f: aktiviert
stats.totalLogin: Gesamt
stats.access: Zugang
stats.uniqueLogin: Einzigartig
stats.at_least_one_course_finished: 'Geschulte Benutzer'
stats.top_rated_courses: 'Top bewertete Kurse'
stats.lowest_rated_courses: 'Am schlechtesten bewertete Kurse'
stats.most_completed_courses: 'Die meisten abgeschlossenen Kurse'
stats.users_more_actives: 'Die meisten aktiven Nutzer'
stats.users_less_actives: 'Weniger aktive Nutzer'
stats.accumulative.title: 'Evolutionär und kumulativ'
stats.accumulative.trained: 'Geschulte Einzelanwender'
stats.accumulative.new: Neu
stats.accumulative.accumulated: Kumulierte
stats.accumulative.chart: Grafik
stats.accumulative.logins: Anmeldung
stats.accumulative.courses: Kurse
stats.accumulative.courses_started: 'Begonnene Kurse'
stats.accumulative.courses_finished: 'Abgeschlossene Kurse'
stats.accumulative.ratings: Bewertungen
stats.accumulative.time: 'Investierte Zeit (in Stunden)'
stats.accumulative.filters: Filter-Verteilung
'stats. daily_posts': 'Tägliche Forumsbeiträge'
stats.most_active_threads: 'Die aktivsten Threads'
stats.most_active_users: 'Die aktivsten Nutzer'
stats.forum_post_messages_count: Nachrichten
stats.forum_post_title: Titel
task.status.pending: Ausstehend
task.status.in_progress: 'In Arbeit'
task.status.success: Erledigt
task.status.failure: Fehler
security.login_button_login: Eingeben
security.login_button_create_account: 'Ein Konto erstellen'
security.login_title: 'Bitte geben Sie Ihre Daten ein'
security.login_remember_me: 'Erinnern Sie sich an mich'
security.login_question_password: 'Haben Sie Ihr Passwort vergessen?'
security.button_register: Registrieren
security.button_exist_accoutn: 'Ich habe bereits ein Konto'
security.button_account: 'Bedingungen akzeptieren'
security.first_name: Vorname
security.last_name: Nachname
security.password: Passwort
security.repeat_password: 'Passwort wiederholen'
security.register: Registrieren
security.remembered_the_password: 'Haben Sie sich an Ihr Passwort erinnert?'
security.button_send_email: 'E-Mail senden'
security.reset_your_password: 'Ihr Passwort zurücksetzen'
security.text_reset_password: 'Geben Sie Ihre E-Mail-Adresse ein und wir senden Ihnen einen Link zum Zurücksetzen Ihres Passworts.'
course_level.label_in_singular: Ebene
course_level.label_in_plural: Ebenen
component_video.add_package_video: 'Videopaket hinzufügen'
component_video.edit_package_video: 'Videopaket bearbeiten'
component_video.type: Typ
component_video.url_video: Url-Video
component_video.file_subtitle: Untertiteldatei
component_video.button_save: Speichern
component_video.text_content_subtitle_video: 'Dieses Video hat bereits einen Untertitel, wenn Sie einen neuen hinzufügen, wird dieser durch den vorherigen ersetzt.'
component_video.upload_file_video: 'Videodatei auswählen'
component_video.preparing_file: 'Warten auf die Vorbereitung der Datei'
component_video.package_video: Video-Paket
component_video.optimizing_video: 'Das Video wird derzeit optimiert und steht in Kürze zur Verfügung.'
component_video.text_good: Quelle
filter_category.label_in_singular: 'Kategorien Filter'
filter_category.label_in_plural: 'Filter Kategorie'
filter_category.configureFields.name: Name
filter.label_in_singular: Filter
filter.label_in_plural: Filter
filter.configureFields.name: Name
filter.configureFields.action_add: 'Filter hinzufügen'
filter.extras.no_filters: 'Keine Filter zugewiesen'
filter.extras.loadings: Laden...
filter.extras.no_filter_selected: 'Es ist kein Filter ausgewählt'
filter.extras.no_filter_assigned: 'Keine Filter zuzuordnen'
news.form.title: Titel
news.form.text: Text
help.pdf.general: ADMIN_DE
help.video.general: '549279910'
segment_category.label_in_singular: 'Kategorie Segment'
segment_category.label_in_plural: 'Kategorien Segment'
segment_category.configureFields.name: Name
course_segmente.label_in_singular: Segment
course_segmente.label_in_plural: Segmente
course_segmente.configureFields.name: Name
course_segmente.configureFields.action_add: 'Segment hinzufügen'
documentation.label: Dokumentation
documentation.title: Titel
documentation.description: Beschreibung
documentation.type: Typ
documentation.file: Datei
documentation.locale: Sprache
pdf.downloadable: Herunterladbar
itinerary.label_in_singular: Reiseplan
itinerary.label_in_plural: Reiserouten
itinerary.name: Name
itinerary.description: Beschreibung
itinerary.tab.courses: Kurse
itinerary.tab.users: Benutzer
itinerary.no_courses: 'Es wurden keine Kurse zum Reiseplan hinzugefügt'
itinerary.no_users: 'Der Reiseroute sind keine Benutzer hinzugefügt worden'
itinerary.saving_courses: 'Speichern von Kursen'
itinerary.find_available_courses: 'Verfügbare Kurse finden'
itinerary.find_selected_courses: 'Ausgewählte Kurse finden'
itinerary.course.position_updated: 'Kurslage aktualisiert'
itinerary.course.update_warning: 'Die Kurse der Reiseroute werden aktualisiert'
itinerary.user.add_success: 'Benutzer erfolgreich hinzugefügt'
itinerary.user.remove_success: 'Benutzer erfolgreich entfernt'
itinerary.user.confirm_delete: 'Der Benutzer verliert den Zugriff auf die Reiseroute'
itinerary.user.confirm_delete_all: 'Die Nutzer verlieren den Zugriff auf die Reiseroute'
itinerary.manager.add_success: 'Manager erfolgreich hinzugefügt'
itinerary.manager.remove_success: 'Manager erfolgreich entfernt'
itinerary.manager.edit_manager: 'Manager bearbeiten'
itinerary.manager.find_managers: 'Manager finden'
itinerary.manager.confirm_delete: 'Der Manager verliert den Zugriff auf den Reiseplan'
itinerary.manager.confirm_delete_all: 'Die Manager verlieren den Zugriff auf die Reiseroute'
itinerary.filter.added: 'Filter zur Reiseroute hinzugefügt'
itinerary.filter.removed: 'Filter von der Reiseroute entfernt'
itinerary.total_courses: 'Kurse insgesamt'
common_areas.cancel: Abbrechen
common_areas.add_all: 'Alle hinzufügen'
common_areas.remove_all: 'Alle entfernen'
user_filter.modify_users: 'Benutzer bearbeiten'
user_filter.find_by: 'Finden durch'
common_areas.total: Insgesamt
common_areas.confirm_delete: "<p style=\"font-size: 14px;\">&lt;p&gt;&lt;b&gt;<span>Wollen Sie diesen Eintrag wirklich löschen?</span>&lt;/b&gt;&lt;br&gt;</p>\n<p style=\"font-size: 14px;\"><span>Diese Aktion kann nicht rückgängig gemacht werden.</span>&lt;/p&gt;</p>"
common_areas.confirm_save: 'Wollen Sie wirklich speichern?'
challenges: Herausforderungen
challenges.random: Zufällig
challenges.question: Frage
challenges.correct: Richtig
challenges.answer1: 'Antwort 1'
challenges.answer2: 'Antwort 2'
challenges.answer3: 'Antwort 3'
challenges.answer4: 'Antwort 4'
challenges.answer5: 'Antwort 5'
challenges.answer6: 'Antwort 6'
material_course.configureFields.type: Dateityp
material_course.configureFields.save: 'Das Material wurde korrekt gelagert'
material_course.configureFields.type_1: PDF
material_course.configureFields.type_2: Video
material_course.configureFields.type_3: Zip/Rar
material_course.configureFields.type_4: Bild
material_course.configureFields.type_5: Office-Pakete
material_course.configureFields.type_6: Notepad
material_course.configureFields.file: Datei
material_course.configureFields.no_material: 'Keine zusätzlichen Materialien'
material_course.configureFields.question_delete: 'Wollen Sie dieses Material wirklich löschen?'
material_course.configureFields.question_decition: 'Diese Aktion kann nicht rückgängig gemacht werden'
material_course.configureFields.delete: 'Material löschen'
material_course.placeholder.file: 'Datei auswählen'
material_course.download: Download
taskCourse.configureFields.noFile: 'Keine Dateien hinzugefügt'
taskCourse.configureFields.question_delete: 'Wollen Sie diese Datei wirklich löschen?'
taskCourse.labelInSingular: Aufgabe
taskCourse.labelInPlural: Aufgaben
taskCourse.configureFields.dateDelivery: Liefertermin
taskCourse.configureFields.startDate: 'Datum des Beginns'
taskCourse.configureFields.visible: Sichtbar
taskCourse.configureFields.senTask: 'Die Aufgabe wurde gesendet'
taskCourse.configureFields.senTaskUser: Senden
taskCourse.configureFields.addFile: 'Datei hinzufügen'
taskCourse.configureFields.state_0: Ausstehend
taskCourse.configureFields.state_1: Ausgeliefert
taskCourse.configureFields.state_2: 'Im Rückblick'
taskCourse.configureFields.state_3: Abgelehnt
taskCourse.configureFields.state_4: Genehmigt
taskCourse.configureFields.files_attachment: Anhänge
taskCourse.configureFields.sendComment: 'Der Kommentar wurde abgeschickt'
taskCourse.configureFields.stateTask: 'Die Aufgabe hat ihren Status geändert'
taskCourse.configureFields.history: Geschichte
component_game.true_or_false: 'Richtig oder falsch'
component_game.adivina_imagen: 'Erraten Sie das Bild'
component_game.ordenar_menorMayor: 'Reihenfolge von oben nach unten'
component_game.parejas: 'Memory Spiel'
component_game.rouletteWheel: 'Rad der Buchstaben'
component_game.categorized: Kategorisiert
component_game.fillgaps: 'Lücken füllen'
component_game.guessword: 'Wort erraten'
component_game.wordle: 'Geheimes Wort'
component_game.lettersoup: 'Buchstaben Suppe'
component_game.videoquiz: 'Video Quiz'
games.letterwheel: Buchstaben-Roulette
games.opciones: Optionen
games.categorize: 'Kategorisieren Sie'
games.optiones_empty: 'Sie müssen mindestens zwei Optionen hinzufügen, um kategorisieren zu können.'
games.validate_add_categorize: 'Sie müssen eine richtige Antwort auswählen und das Fragefeld ausfüllen oder ein Bild auswählen.'
games.add_category: 'Option hinzufügen'
games.add_categories: 'Kategorie hinzufügen'
games.add_word: 'Wort hinzufügen'
games.words: Wörter
games.edit_option: 'Option bearbeiten'
games.text_common.answer: 'Antwort '
'games.text_common:correct': Richtig
games.text_common.time: Zeit
games.text_common.word: Wort
games.text_common.no_questions: 'Keine Fragen'
games.text_common.text_question: Fragetext
games.text_common.word_question: 'Wort der Frage'
games.text_common.message_guess_word_question: 'Sie müssen den Text der Frage eingeben'
games.text_common.message_guess_word_word: 'Sie müssen das Wort der Frage eingeben'
games.text_common.message_guess_word_time: 'Sie müssen die Uhrzeit für die Frage eingeben'
games.text_common.message_guess_word_answer: 'Die Antwort darf nur ein Wort enthalten'
games.text_common.select_image: 'Bild auswählen'
games.text_common.ilustre_category: 'Bild zur Veranschaulichung der Kategorie'
games.text_common.ilustre_question: 'Bild zur Veranschaulichung der Frage'
games.text_common.message_higher_lower: 'Erstelle die Wörter, die im Spiel erscheinen sollen, und ordne sie dann nach Belieben an. Sie können die Reihenfolge durch Ziehen der Wörter ändern.'
games.validate_memory_match: 'Sie müssen einen Titel hinzufügen oder ein Bild hinzufügen'
games.help: Hilfe
games.validate_hidden_image: 'Titel, Text und Bild sind obligatorisch'
games.fillgap.title: 'Wie wird das Spiel aufgebaut?'
games.fillgap.message: 'In der Eingabezeile Satz oder Lücke hinzufügen können Sie die Struktur des Spiels festlegen und entscheiden, ob der Text ein Satz oder eine Lücke sein soll. Wenn Sie eine Lücke in das Ergebnis der Frage einfügen, wird diese blau angezeigt.'
games.fillgap.result_question: 'Ergebnis des Spiels'
games.fillgap.word: 'Satz oder Lücke hinzufügen'
games.fillgap.add_filler: 'Satz hinzufügen'
games.fillgap.add_gap: 'Lücke hinzufügen'
games.fillgap.new_option: 'Neue Option'
games.fillgap.validate_save: 'Sie müssen mindestens einen Satz, eine Lücke und zwei Optionen hinzufügen'
games.videoquiz.message_validate_answer: 'Sie müssen mindestens zwei Antworten hinzufügen und die richtige Antwort darf nicht leer sein.'
games.videoquiz.time_video: 'Video Zeit'
games.videoquiz.savell_all_changes: 'Alle Änderungen speichern'
games.videoquiz.validate_to_add_question: 'Sie müssen mindestens eine Frage haben, um Änderungen speichern zu können.'
games.videoquiz.validate_letter_soup: 'Sie scheinen den Titel oder die Worte zu vermissen'
chapter_type.1: Scorm
chapter_type.2: Inhalt
chapter_type.3: Roulette
chapter_type.4: x2
chapter_type.5: Fragebogen
chapter_type.6: Puzzle
chapter_type.7: 'Versteckte Wörter'
chapter_type.8: Pdf
chapter_type.9: Video
chapter_type.10: Schieberegler
chapter_type.11: Roulette
chapter_type.12: 'Richtig oder Falsch'
chapter_type.13: 'Bild erraten'
chapter_type.14: Hierarchie
chapter_type.15: Paare
chapter_type.16: 'Kategorisieren Sie'
chapter_type.17: Löcher
chapter_type.18: 'Wort sortieren'
chapter_type.19: 'Geheimes Wort'
chapter_type.20: 'Buchstaben Suppe'
chapter_type.21: 'Video Quiz'
menu.users.exit_impersonate: 'Impersonate beenden'
menu.forum: Forum
course.export: 'Kurse exportieren'
course.export.confirm: 'Wollen Sie die Kurse wirklich exportieren?'
announcements.configureFields.opinions: Meinungen
announcements.configureFields.no_messages: 'Keine Meldungen'
announcements.configureFields.info_max_users: 'Die maximale Anzahl der Teilnehmer, die an dem Anruf teilnehmen können, beträgt: '
announcements.configureFields.annoucement_all: 'Alle einberufen'
question_nps.configureFields.source: 'Bewerben bei'
user.actions.impersonate: 'Sich als  ausgeben'
user.show_cv: 'Siehe Lebenslauf'
user.delete_cv: 'Lebenslauf löschen'
stats.export.download_file_pdf: 'PDF herunterladen'
stats.export.download_file_xlsx: 'Excel herunterladen'
stats.segmented.title: 'Segmentierte Statistik'
filter.removed_filter: 'Der Filter %s wurde erfolgreich entfernt.'
filter.added_filter: 'Der %s-Filter wurde erfolgreich hinzugefügt.'
filter.all_removed: 'Die Filter wurden entfernt'
filter.all_added: 'Es wurden Filter hinzugefügt'
itinerary.chart.users: 'Menschen haben die Reiseroute abgeschlossen'
itinerary.chart.users_process: 'in Bearbeitung'
itinerary.chart.users_incomplete: 'ohne Anfahren'
itinerary.chart.users_title: ' zugewiesene Personen'
itinerary.chart.total_time: 'Kumulative Gesamtzeit'
itinerary.chart.avg_time: 'Durchschnittliche Zeit pro Person'
itinerary.chart.by_country: 'Reiserouten nach Land'
itinerary.chart.by_hotel: 'Reiserouten nach Zentrum'
itinerary.chart.by_department: 'Reiserouten nach Departement'
itinerary.chart.by_grouping: 'Reiserouten nach Gruppierung'
itinerary.users_assign: 'Diese Reiseroute wurde Personen zugewiesen'
itinerary.users.progress: 'Fortschritte bei der Reiseroute'
itinerary.users.download_user: 'Excel herunterladen'
itinerary.courses.selected: 'Ausgewählte Kurse'
itinerary.status.completed: Abgeschlossen
itinerary.status.started: 'In Bearbeitung'
itinerary.status.unstarted: 'Ohne Anfahren'
segmented_stats.title1: 'Ausgebildete Personen'
segmented_stats.title2: Stunden
segmented_stats.title3: Kurse
segmented_stats.title4: Zugang
segmented_stats.distribution_by_country: 'Verteilung nach Ländern'
segmented_stats.structure: Struktur
segmented_stats.hotel: Hotel
segmented_stats.by_department: 'Nach Abteilung'
segmented_stats.by_school: 'Nach Schule'
segmented_stats.total_hours: 'Stunden insgesamt'
segmented_stats.total_avg: 'Durchschnittliche Arbeitsstunden'
segmented_stats.structure_avg: 'Durchschnittliche Struktur'
segmented_stats.structure_total: Gesamtstruktur
segmented_stats.hotel_avg: 'Durchschnittliches Hotel'
segmented_stats.hotel_total: 'Hotel Gesamt'
segmented_stats.avg: Durchschnitt
segmented_stats.courses_started: 'Begonnene Kurse'
segmented_stats.courses_finished: 'Abgeschlossene Kurse'
segmented_stats.total_courses_started: 'Begonnene Kurse insgesamt'
segmented_stats.total_courses_finished: 'Abgeschlossene Kurse insgesamt'
segmented_stats.access_totals: 'Zugriffe insgesamt'
segmented_stats.access_uniques: Einzelanmeldung
segmented_stats.certificates: Diplome
segmented_stats.total_certificates: 'Ausgestellte Diplome insgesamt'
library.createdAtView: 'Erstellt von {email} am {date} um {time}:'
library.no_text_provided: 'Es wurde kein Text eingegeben'
library.maximum_allowed_size_exceeded: '%s: Die maximal zulässige Anzahl von Zeichen wurde überschritten.'
library.category.created: 'Die Kategorie wurde erfolgreich erstellt'
library.category.updated: 'Kategorie wurde erfolgreich aktualisiert'
library.category.deleted: 'Die Kategorie wurde erfolgreich gelöscht'
library.category.activated: 'Die Kategorie wurde erfolgreich aktiviert'
library.category.deactivated: 'Die Kategorie wurde erfolgreich deaktiviert'
library.library.updated: 'Die Bibliothek wurde erfolgreich aktualisiert'
library.library.created: 'Die Bibliothek wurde erfolgreich erstellt'
library.library.deleted: 'Die Bibliothek wurde erfolgreich entfernt'
library.library.name_required: 'Der Name ist obligatorisch und muss weniger als 100 Zeichen umfassen.'
library.library.type_required: 'Das Feld "Typ" ist erforderlich'
library.library.link_required: 'Wenn der Typ ''LINK'' ist, muss eine gültige URL angegeben werden.'
forum.configureFields.thread: Thema
forum.configureFields.message: Nachricht
forum.configureFields.comment: 'Kommentar Bericht'
forum.configureFields.title_modal_add: 'Forum hinzufügen'
forum.configureFields.title_modal_edit: 'Forum bearbeiten'
course_press.label_in_singular: Klassenzimmer-Kurs
course_press.label_in_plural: 'Kurse vor Ort'
menu.courses_managment.course_sections: Rubriken
common.write: 'Schreiben Sie etwas'
common_areas.accept: Akzeptieren
common_results: Ergebnisse
games.answers: 'Antworten hinzufügen'
games.text_common.order_ramdom: 'Zufällig sortieren'
games.puzzle.description_cropper: 'Wählen Sie den Bereich des Bildes aus, der im Puzzle erscheinen soll.'
games.validation_truefalse.question_or_image: 'Sie müssen die Frage eingeben oder ein Bild auswählen'
games.help.write_question: 'Frage schreiben'
games.help.write_word: 'Wort schreiben'
games.help.write_title: 'Einen Titel schreiben'
games.help.write_answer: 'Eine Antwort schreiben'
games.true: Wahr
games.false: Wahr
games.edit_video_quiz: 'Video-Quiz ansehen und bearbeiten'
games.delete_video_quiz: 'Video-Quiz entfernen'
game.feedback.title: 'Feedback hinzufügen (optional)'
game.feedback.title_positive: 'Rückmeldung im Falle eines Treffers'
game.feedback.title_negative: 'Rückmeldung im Falle eines Fehlers (optional)'
announcements.common.group: Gruppe
announcements.common.action_denomination: 'Titel Aktion'
announcements.common.modality: Modalität
announcements.common.place_of_instruction: 'Ort der Lieferung'
announcements.common.collaboration_type: 'Art der Zusammenarbeit'
announcements.common.provider: Anbieter
announcements.common.provider_cif: 'CIF Anbieter'
announcements.observations.costs: Kosten
announcements.observations.course_status: 'Status des Kurses'
announcements.observations.comunicado_fundae: 'FUNDAE Kommuniqué'
announcements.observations.comunicado_abilitia: 'Mitteilung an ABILITIA'
announcements.observations.economic_module: 'Modul Wirtschaft'
announcements.observations.travel_and_maintenance: 'Verdrängung und Wartung'
announcements.observations.provider_cost: Kostenlieferant
announcements.observations.hedima_management_cost: 'HEDIMA Verwaltungskosten (10%)'
announcements.observations.travel_and_maintenance_cost: 'Reise- und Aufenthaltskosten'
announcements.observations.total_cost: Gesamtkosten
announcements.observations.final_pax: PAX-Finale
announcements.observations.maximum_bonus: 'Maximaler Bonus (letzter PAX)'
announcements.observations.subsidized_amount: 'Subventionierter Betrag'
announcements.observations.private_amount: 'Privater Betrag'
announcements.observations.provider_invoice_number: 'Rechnungs-Nr. Lieferant'
announcements.observations.hedima_management_invoice_number: 'Rechnungs-Nr. HEDIMA Management'
announcements.observations.invoice_status: 'Status der Rechnung'
announcements.observations.observations: Bemerkungen
announcements.observations.observation: Beobachtung
announcements.course.no_chapter: 'Dieser Kurs hat keine Kapitel, da es sich um einen Klassenzimmerkurs handelt.'
announcements.formativeActionTypes.intern: Intern
announcements.formativeActionTypes.extern: Extern
announcements.formativeActionTypes.session_congress: Extern
common_areas.confirm_file_upload: 'Sind Sie sicher, dass Sie das/die Dokument(e) hochgeladen haben?'
common_areas.confirm_file_delete: 'Sind Sie sicher, dass Sie löschen wollen?'
course_section.label_in_singular: Sektion
course_section.label_in_plural: Sektionen
course_section.configureFields.name: Name
course_section.configureFields.description: Beschreibung
course_section.configureFields.active: Activa
course_section.configureFields.sort: Bestellung
course_section.configureFields.translations: Übersetzungen
course_section.configureFields.section_name: 'Name der Sektion'
course_section.configureFields.categories: Kategorien
user.roles.administrator: Verwalter
user.roles.user: Benutzer
user.roles.tutor: Nachhilfelehrer
user.roles.subsidizer: Inspektor
user.roles.manager: Manager
user.roles.manager_editor: 'Manager - Redakteur'
user.roles.team_manager: Teamleiter
survey.label_in_plural: Erhebungen
course.configureFields.is_main: 'In diesem Kurs werden nur die eigenen Fragen zur Bewertung herangezogen.'
global.error: 'Es ist ein Fehler aufgetreten. Bitte versuchen Sie es später noch einmal.'
quiz.configureFields.title_creation: 'Erstellung von Fragen'
quiz.configureFields.question: 'Wortlaut der Frage'
quiz.configureFields.question_placeholder: 'Frageanweisung schreiben'
quiz.configureFields.question_delete: 'Wollen Sie diese Frage wirklich eliminieren?'
rouletteWord.configureFields.statement: Erklärung
rouletteWord.configureFields.answer: Antwort
rouletteWord.configureFields.type_0: 'Beginnen Sie mit dem Buchstaben'
rouletteWord.configureFields.type_1: 'Enthält den Brief'
rouletteWord.configureFields.error.statement.max: 'Die Anweisung darf nicht länger als ${max} Zeichen sein.'
rouletteWord.configureFields.error.statement.empty: 'Die Anweisung darf nicht leer sein'
rouletteWord.configureFields.error.answer.max: 'Die Antwort darf {max} Zeichen nicht überschreiten.'
rouletteWord.configureFields.error.answer.empty: 'Die Antwort kann nicht leer sein'
rouletteWord.configureFields.error.answer.starts: 'Die Antwort muss mit einem Buchstaben beginnen'
rouletteWord.configureFields.error.answer.includes: 'Die Antwort muss den Buchstaben'
rouletteWord.response.update_letter: 'Die Daten sind korrekt aktualisiert worden'
rouletteWord.response.delete_letter: 'Die Daten wurden erfolgreich gelöscht'
trueorFalse.configureFields.true: Richtig
trueorFalse.configureFields.false: Falsch
enigma.configureFields.title_creation: Enigma-Erstellung
puzzle.configureFields.save_image: 'Bild erfolgreich gespeichert'
puzzle.configureFields.select_correct_answer: 'Sie müssen eine richtige Antwort auswählen'
puzzle.configureFields.recomendation: Empfehlung
puzzle.configureFields.recomendation_dimentions: '<p>Wir empfehlen eine <span class="text-primary"><b>Mindestgröße von 1024 Pixel pro Seite</b></span> und eine <span class="text-primary"><b>Höchstgröße von 2000 Pixel pro Seite.</b></span></p>'
puzzle.configureFields.recomendation_description: '<p>Das Format des Puzzles ist <span class="text-primary"><b>quadratisch.</b></span> Wenn wir also ein Bild mit einem anderen Seitenverhältnis auswählen, z. B. ein Querformat, können wir mit dem Werkzeug den gewünschten Bereich auswählen.</p>'
hiddenword.configureFields.title: 'Erstellung eines versteckten Wortes'
hiddenword.configureFields.answers_title: 'Verstecktes Wort'
hiddenword.configureFields.answers_placeholder: 'Verstecktes Wort schreiben'
categorize.configureFields.title_group: 'Gruppen oder Familien'
fillgaps.configureFields.title: Phrasenbildung
fillgaps.configureFields.fillgap: Hohle
fillgaps.configureFields.fillgaps: Löcher
fillgaps.configureFields.type_list: Liste
fillgaps.configureFields.type_drag: Ziehen
guesword.configureFields.word_title: 'Ungeordnetes Wort'
guesword.configureFields.word_title_placeholder: 'Schreiben Sie ein Wort, das ungeordnet erscheinen soll'
guesword.configureFields.solution: Lösung
guesword.configureFields.solution_placeholder: 'Schreiben Sie die Lösung des Spiels'
guesword.configureFields.help_placeholder: 'Hilfe für das Spiel schreiben'
pairs.configureFields.title: 'Entwicklung des Spiels'
pairs.configureFields.placeholder_title: 'Schreiben Sie die Erklärung'
pairs.configureFields.create_game: 'Erstellung von Spielen'
chapter_type.description.22: '<p>Die ideale Lösung für die Erstellung dynamischer und attraktiver Inhalte in Ihren Kursen oder Trainingspillen, die Informationen visuell und mit einer Vielzahl von Interaktionen auf der Grundlage von Text, Bildern, Videos, Audio, Multimedia-Links, interaktiven Karten, verknüpften Szenen usw. präsentieren.</p> '
chapter_type.add.22: 'Interaktive Inhalte erstellen'
games.videoquiz_exist_question: 'Es wurde bereits eine Frage im gleichen Zeitfenster erstellt.'
chapter_type.22: VCMS
video.configureFields.title: 'Video-Quiz erstellen'
video.configureFields.add_question: 'Frage hinzufügen'
Next: Nächste
hours: Stunden
minutes: Minuten
seconds: Sekunden
field_required: 'Erforderliches Feld'
field_invalid: 'Ungültiges Feld'
field_invalid_format: 'Ungültiges Format'
remaining_characters: 'Verbleibende Zeichen'
minimiun_characters: 'Minimum Zeichen'
menu.home.title: 'Zum Benutzerbereich gehen'
course.season.type.sequential: 'Sequentielle Navigation'
course.season.type.free: 'Freie Navigation'
course.season.type.exam: Prüfungsmodus
games.fillgap.add_fillgap: 'Lücken durch Klicken auf bestimmte Wörter hinzufügen'
course_section.configureFields.hideCategoryName: 'Kategorienamen ausblenden'
user.roles.super_administrator: SuperAdministrator
settings.menu.label: Konfiguration
settings.header.title: Konfiguration
setting.menu.general: Allgemein
setting.menu.catalog: Kataloge
share: 'Teilen Sie'
report.announcement.participants: Teilnehmer
report.announcement.groupCode: 'Code der Gruppe'
report.announcement.enterpriseProfile: 'Profil des Unternehmens'
report.announcement.file: Datei
report.announcement.totalStudents: 'Studenten insgesamt'
report.announcement.enterpriseCIF: 'Umsatzsteuer-Identifikationsnummer des Unternehmens'
report.announcement.advisor: Nachhilfelehrer
course.stats.started: Gestartet
course.stats.ended: Abgeschlossen
course.stats.total_time: Gesamtdauer
course.stats.avg_time: 'Durchschnittliche Zeit'
course.stats.minutes: minuten
course.stats.minute: minute
course.stats.hours: stunden
course.stats.hour: zeit
course.stats.second: zweite
course.stats.seconds: sekunden
question.configureFields.quantity_max_question: 'Maximale Anzahl von Fragen, die angezeigt werden sollen:'
user.configureFields.available_courses: 'Verfügbare Kurse'
user.configureFields.available_chapter: 'Kapitel verfügbar'
user.configureFields.courses_stats.finished: abgeschlossen
user.configureFields.courses_stats.started: gestartet
user.configureFields.courses_stats.available: verfügbar
user.configureFields.courses_stats.sent_messages: 'gesendet an'
user.configureFields.courses_stats.received_messages: erhalten
user.configureFields.courses_stats.others: Andere
user.configureFields.permissions: Erlaubt
course.configureFields.segments: Segmente
stats.roles: Rollen
course.configureFields.language: Sprache
game.feedback.wrong: 'Beispiel: Wow!'
chapter_type.description.23: '<p>Rollenspiele sind Aktivitäten, bei denen die Teilnehmer in die Rolle fiktiver Charaktere schlüpfen und handeln, oft in einem bestimmten Umfeld oder Kontext. Während des Rollenspiels nehmen die Teilnehmer vorübergehend die Persönlichkeit, die Eigenschaften und das Verhalten der von ihnen dargestellten Figuren an und interagieren je nach den Umständen und der geschaffenen imaginären Umgebung miteinander. Diese Praxis wird in einer Vielzahl von Kontexten eingesetzt, z. B. in Spielen, Therapien, Bildungssimulationen und Freizeitaktivitäten, um Kreativität, Empathie, Problemlösung und die Erforschung hypothetischer Situationen zu fördern.</p>'
password.uppercase: 'Erforderlich 1 oder mehr Zeichen in Großbuchstaben'
password.number: '1 oder mehr numerische Ziffern erforderlich'
password.minimum: 'Das Passwort muss aus mindestens %s Zeichen bestehen.'
password.disable_3_consecutive_chars: 'Es ist nicht erlaubt, ein Zeichen mehr als 3 Mal hintereinander zu wiederholen.'
password.lowercase: '1 oder mehr Kleinbuchstaben erforderlich'
chapter_type.23: Rollenspiel
chapter_type.add.23: 'Crear Rollenspiel'
password.special_characters: '1 oder mehr Sonderzeichen erforderlich'
roleplay.status.failure: 'Sie haben versagt'
roleplay.status.success: 'Sie haben bestanden'
user.configureFields.locale: Sprache
course.created: 'Der Kurs wurde erfolgreich gespeichert'
question.configureFields.do_all_questions: 'Benutzen Sie alle Fragen'
announcements.news.start_announcement: 'Der kurs %course% ist gleich um die Ecke!'
announcements.news.finish_announcement: 'Der kurs %course% ist fast vorbei!'
user.configureFields.dni: DNI
course_section.configureFields.section_aditional: 'Zusätzliche Ausbildung'
report.announcement.time_conexion: Verbindungszeit
report.announcement.init_finish: 'Anfang und Ende'
report.annnouncement.conexions: Verbindungen
report.annnouncement.chat_tutor: 'Chat mit dem Tutor'
report.annnouncement.first_conexion: 'Erste Verbindung'
report.annnouncement.last_conexion: 'Letzte Verbindung'
generic_token.assistance.success: 'Ihre Anwesenheit wurde erfolgreich registriert'
generic_token.assistance.user_not_in_group: 'Benutzer gehört nicht zur Sitzungsgruppe'
chat.notification.number_of_messages: 'Sie haben %s ungelesene Nachricht(en)'
certificate.notification.available: 'Diplom der %s Kurseinberufung zum Herunterladen verfügbar'
user_fields_fundae.title: 'Zusätzliche Felder FUNDAE'
user_fields_fundae.social_security_number: Sozialversicherungsnummer
user_fields_fundae.gender: Geschlecht
user_fields_fundae.email_work: Arbeitspost
user_fields_fundae.birthdate: 'Datum der Geburt'
user_fields_fundae.dni: DNI
user_fields_fundae.contribution_account: Beitragskonto
user_fields_fundae.incapacity: Arbeitsunfähigkeit
user_fields_fundae.victim_of_terrorism: 'Opfer des Terrorismus'
user_fields_fundae.gender_violence: 'Opfer von geschlechtsspezifischer Gewalt'
fundae_assistance_template.main_title: 'ANWESENHEITSKONTROLLE BEI SCHULUNGEN'
fundae_assistance_template.action_type: 'NAME DER SCHULUNGSMASSNAHME'
fundae_assistance_template.action_code: AKTIONSCODE
fundae_assistance_template.group: GRUPPE
fundae_assistance_template.start_at: ANFANGSDATUM
fundae_assistance_template.finish_at: 'DATUM ENDE'
fundae_assistance_template.main_formation_teacher: AUSBILDER/AUSBILDUNGSLEITER
fundae_assistance_template.session_number: 'SITZUNG NR.'
fundae_assistance_template.date: DATUM
fundae_assistance_template.morning_afternoon: MORGEN/NACHMITTAGS
fundae_assistance_template.signed: Unterzeichnet
fundae_assistance_template.info_signed_person: Trainer/Ausbildungsbeauftragter.
fundae_assistance_template.assistance_data: 'Daten des Teilnehmers'
fundae_assistance_template.signatures: UNTERSCHRIFTEN
fundae_assistance_template.observations: BEMERKUNGEN
fundae_catalogs.main_page.title: 'FUNDAE Kataloge'
fundae_catalogs.user_company.label_in_plural: Unternehmen
fundae_catalogs.user_company.label_in_singular: Unternehmen
fundae_catalogs.user_professional_category.label_in_plural: 'Berufliche Kategorien'
fundae_catalogs.user_professional_category.label_in_singular: Berufsgruppe
fundae_catalogs.user_study_level.label_in_plural: 'Stufen des Studiums'
fundae_catalogs.user_study_level.label_in_singular: 'Niveau des Studiums'
fundae_catalogs.user_work_center.label_in_plural: Arbeitsstätten
fundae_catalogs.user_work_center.label_in_singular: Arbeitsplatz
fundae_catalogs.user_work_department.label_in_plural: 'Abteilungen der Arbeit'
fundae_catalogs.user_work_department.label_in_singular: 'Ministerium für Arbeit'
fundae_catalogs.fields.state.title: Staat
fundae_catalogs.fields.state.active: Aktiv
fundae_catalogs.fields.state.inactive: Inaktiv
excel.userAnnouncement.sheet1.title: 'Allgemeine Informationen'
excel.userAnnouncement.sheet1.colum1: 'Anzahl der Reiserouten'
excel.userAnnouncement.sheet1.colum2: 'Anzahl der Nutzer, die Reiserouten haben'
excel.userAnnouncement.sheet1.colum3: 'Anzahl der Kurse im Rahmen der Berufsbildungsabschnitte'
excel.userAnnouncement.sheet2.title: 'Katalog Reiserouten'
excel.userAnnouncement.sheet2.colum1: Id
excel.userAnnouncement.sheet2.colum2: 'Name Reiserouten'
excel.userAnnouncement.sheet2.colum3: Abteilung
excel.userAnnouncement.sheet2.colum4: Kategorie
excel.userAnnouncement.sheet2.colum5: 'Zugewiesene Kurse'
excel.userAnnouncement.sheet2.colum6: 'Zugewiesene Personen'
excel.userAnnouncement.sheet2.colum7: 'Die Leute haben die Reiseroute abgeschlossen'
excel.userAnnouncement.sheet2.colum8: 'Menschen im Prozess'
excel.userAnnouncement.sheet2.colum9: Nicht-Starter
excel.userAnnouncement.sheet2.colum10: 'KUMULIERTE GESAMTZEIT'
excel.userAnnouncement.sheet2.colum11: 'DURCHSCHNITTLICHE PERSONENZEIT'
excel.userAnnouncement.sheet3.title: 'Reiseroute Kurse'
excel.userAnnouncement.sheet3.colum1: Id
excel.userAnnouncement.sheet3.colum2: 'Name Reiserouten'
excel.userAnnouncement.sheet3.colum3: 'Name Kurs'
excel.userAnnouncement.sheet3.colum4: Abgeschlossen
excel.userAnnouncement.sheet3.colum5: 'In Arbeit'
excel.userAnnouncement.sheet3.colum6: 'Kein Start'
course.message_saved: 'Gespeicherter Kurs'
chapter.configureFields.create_chapter: 'Kapitel erstellen'
user_filter.assign_manual: 'Manuell zuweisen'
user_filter.assign_filters: 'Nach Filtern zuordnen'
user.configureFields.configureLocale: Spracheinstellungen
user.configureFields.configureLocaleAdmin: 'Sprache für das Administrationspanel konfigurieren'
user.configureFields.configureLocaleCampus: 'Sprache für den Campus festlegen'
stats.export.configsheet.title: Konfiguration
stats.export.configsheet.content_title: 'Allgemeiner Statistikbericht'
stats.export.configsheet.content_period: 'Erfasster Zeitraum (Enddatum)'
stats.export.configsheet.content_filters: 'Aktive Filter'
stats.export.configsheet.content_period_from: Von
stats.export.configsheet.content_period_to: An
stats.export.datasheet.title: Daten
stats.export.filter.category: Kategorie
stats.export.filter.departament: Abteilung
stats.export.filter.gender: Geschlecht
stats.export.filter.activeUsers: Benutzer
stats.export.filter.activeUsers_val_yes: Vermögenswerte
stats.export.filter.activeUsers_val_no: Inaktiv
stats.export.filter.course_full_title: '100%iger Kurs'
stats.export.filter.course_full_val_yes: Ja
stats.export.filter.course_full_val_no: Nein
stats.export.filter.course_full_descr: '(Geben Sie nur Kurse an, die in dem angegebenen Zeitraum absolviert wurden)'
stats.export.filter.course_intime_title: 'Lebensdauer des Kurses in der Periode'
stats.export.filter.course_intime_val_yes: Ja
stats.export.filter.course_intime_val_no: Nein
stats.export.filter.course_intime_descr: '(Geben Sie nur Kurse an, die innerhalb des angegebenen Zeitraums begonnen und beendet wurden)'
stats.export.filter.course_started_in_period_title: 'Kursbeginn im Datumsbereich'
stats.export.filter.course_started_in_period_val_yes: Ja
stats.export.filter.course_started_in_period_val_no: Nein
stats.export.filter.course_finished_in_period_title: 'Kurs im Datumsbereich abgeschlossen'
stats.export.filter.course_finished_in_period_val_yes: Ja
stats.export.filter.course_finished_in_period_val_no: Nein
stats.export.filter.customFilters: Personalisierte
stats.content_allusers: 'Alle Benutzer'
stats.content_inactive: Inaktiv
stats.content_inactive_f: Inaktiv
itinerary.user.assign_manual: 'Manuell zuweisen'
itinerary.user.assign_filter: ' Nach Filtern zuordnen'
itinerary.user.modify_users: 'Personen manuell zuweisen'
itinerary.user.filter_find_by: 'Suche nach'
common_areas.close: 'Schließen Sie'
itinerary.chart.avg_time_active: '(Insgesamt aktiv)'
itinerary.chart.avg_time_all: '(Insgesamt zugewiesen)'
itinerary.courses.modify: 'Kurse zuweisen'
itinerary.courses.appliedfilter: ' kurs/e angezeigt (gefiltert nach'
itinerary.users.appliedfilter: ' angezeigte Person/en (gefiltert nach'
itinerary.courses.available: 'Verfügbare Kurse'
excel.userAnnouncement.sheet1.colum2b: 'Anzahl der eindeutigen Nutzer, die Reiserouten haben'
excel.userAnnouncement.sheet1.colum3b: 'Anzahl der einzelnen Kurse in den Berufsbildungsabschnitten'
common_areas.select_choice: 'Wählen Sie eine Option'
chapter_type.24: LTI
chapter_type.description.24: LTI
lti_chapter.title: 'Kapitel LTI'
lti_chapter.add: 'LTI-Kapitel hinzufügen'
lti_chapter.edit: 'LTI-Kapitel bearbeiten'
lti_chapter.identifier: LTI-Bezeichner
lti_chapter.identifier_required: 'LTI-Kennung erforderlich'
chapter_type.add.24: 'LTI-Kapitel hinzufügen'
categoryFilter.label: Kategorie-Filter
categoryFilter.title: 'Kategorien filtern'
user.configureFields.localeCampus: Campus-Sprache
global.bulk.sheetValidation.error_tab_1: 'Die erste Registerkarte heißt nicht "Liste der Formationen".'
global.bulk.sheetValidation.error_tab_2: 'Die zweite Registerkarte heißt nicht "Teilnehmer".'
stats.export.user_creation: 'Benutzer nach Erstellungsdatum filtern'
stats.export.users_export_title: Benutzerstatistiken
chapter_type.validation_course: "\nDas Kapitel kann nicht gelöscht werden, da einige Benutzer bereits Aktivitäten in diesem Kapitel registriert haben."
user.configureFields.courses_stats.notstarted: 'nicht gestartet'
course.configureFields.created_at: 'Datum der Erstellung'
course.configureFields.translate: 'Übersetzen Sie'
messages.configureFields.timezone: Zeitzone
user.email: 'E-Mail Adresse'
course.diploma.index: Inhaltsverzeichnis
menu.stats.reports.diplomas: 'Berichte und Diplome'
itinerary.succes.download: 'Der Bericht über den Studienverlauf ist in Bearbeitung und kann unter "Berichte und Diplome" eingesehen werden'
user.diploma.generate: 'Diplome generieren'
filters.placeholder: Typensuche
filters.remove_all: 'Alle entfernen'
filters.add_all: 'Alle hinzufügen'
announcement.report_group_resume_individual: 'Individuelle Zusammenfassung'
announcement.report_downloaded_diploma: 'Heruntergeladenes Diplom'
announcements.configureFields.code: 'Name des Anrufs'
task.status.review: 'Wird überprüft'
task.status.error: Systemfehler
email.error.subject: 'Fehler in %context% (ID: %id%) - Umgebung: %appName% - Fehler in %context% (ID: %id%) - Umgebung: %appName%'
email.error.subject_no_id: 'Fehler in %context% - Umgebung: %appName%'
email.error.title: 'Fehler bei der Ausführung des Tasks'
email.error.environment: Umwelt
email.error.context: Kontext
email.error.task_id: Aufgaben-ID
email.error.error_details: 'Einzelheiten des Fehlers'
email.error.error_message: Fehlermeldung
email.error.error_line: Leitung
email.error.error_file: Archiv
email.error.additional_info: 'Zusätzliche Informationen'
email.error.regards: 'Mit freundlichen Grüßen'
email.error.team: 'Das %appName%-Team'
email.zombie.subject: 'Aufgabe im Status ZOMBIE in %context% (ID: %id%) - Umgebung: %appName% (ID: %id%) - Umgebung: %appName% (ID: %id%)'
email.zombie.title: 'Benachrichtigung über eine Aufgabe im Zustand ZOMBIE'
email.zombie.environment: Umwelt
email.zombie.context: Kontext
email.zombie.task_id: Aufgaben-ID
email.zombie.marked_as: 'wurde markiert als'
email.zombie.zombie_status: ZOMBIE
email.zombie.timeout_reason: 'weil sie die zulässige Ausführungszeit überschritten hat'
email.zombie.check_details: 'Bitte überprüfen Sie das Administrationspanel oder die Konsole auf weitere Details und ergreifen Sie entsprechende Maßnahmen'
email.zombie.additional_info: 'Zusätzliche Informationen'
email.zombie.regards: 'Mit freundlichen Grüßen'
email.zombie.team: 'Das %appName%-Team'
season.delete: 'Es ist nicht möglich, diese Staffel zu löschen, sie hat derzeit verknüpfte Episoden'
delete.season.chapters.users: "Saison %SaisonName% kann nicht gelöscht werden,\n                     kapitel (%ChaptesTitles%) haben Benutzeraktivität"
delete.season.danger: 'Die Saison kann nicht beendet werden'
stats.task.queued: 'Geklebte Aufgabenstellung'
itinerary.delete.confirm.validation: 'Derzeit ist der Reiseplan aktiv. Um die Aktion durchzuführen, müssen Sie den Reiseplan deaktivieren'
itinerary.delete.confirm.title: 'wollen Sie die Reiseroute wirklich löschen?'
course.publish.message.active: 'Veröffentlichter Kurs'
course.publish.message.unactive: 'Kurs als unveröffentlicht markiert'
itinerary.delete.error: 'Reiseplan kann nicht gelöscht werden'
courser.chaperts.orders.succes: 'Kapitelauftrag erfolgreich aktualisiert'
course.publish.message.unactive.chapters: 'Kann nicht veröffentlicht werden, da der Kurs unvollständig ist'
course.undelete.message: 'Der Kurs kann nicht gelöscht werden, da er über zugewiesene Inhalte verfügt'
user.roles.creator: Schöpfer
