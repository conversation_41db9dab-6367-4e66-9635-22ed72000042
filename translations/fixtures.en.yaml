nps_question.text.description: 'Give us your feedback'
nps_question.nps.description: 'Course evaluation'
type_course.teleformacion.name: E-learning
type_course.teleformacion.description: 'For e-learning courses'
type_course.presencial.name: On-site
type_course.presencial.description: 'On-site courses'
type_course.mixto.name: Mixed
type_course.mixto.description: 'It is a combination of e-learning and face-to-face training'
type_course.aula_virtual.name: 'Virtual Classroom'
type_course.aula_virtual.description: 'Classes are conducted via videoconference'
alert_type_tutor.1.name: 'The summoned person has not accessed the course'
alert_type_tutor.1.description: 'An alert will be sent to the tutor if the person summoned has not accessed the course'
alert_type_tutor.2.name: '50% of the call has elapsed and 25% of the contents have not been completed'
alert_type_tutor.2.description: 'An alert will be sent to the tutor if 50% of the call has elapsed and 25% of the content has not been completed'
alert_type_tutor.3.name: '80% of the call has elapsed and 50% of the content has not been completed'
alert_type_tutor.3.description: 'An alert will be sent to the tutor if 80% of the call has elapsed and 50% of the contents have not been completed'
alert_type_tutor.4.name: 'There are only a few days left until the end of the call and the course has not yet been completed'
alert_type_tutor.4.description: 'You have to evaluate the number of days that are considered few days'
alert_type_tutor.5.name: 'The person summoned has completed the course but has not responded to the survey.'
alert_type_tutor.5.description: 'If the platform has surveys, an alert will be sent to the tutor if the person called has completed the course but has not answered the survey'
alert_type_tutor.6.name: 'The person has completed the course but has not downloaded the diploma'
alert_type_tutor.6.description: 'An alert will be sent to the tutor if the person summoned has completed the course but has not downloaded the diploma.'
announcement_configuration_type.temporalizacion.name: Timing
announcement_configuration_type.temporalizacion.description: 'In order to facilitate the follow-up of the course, we will assign a time to each block of contents and activities, thus being able to detect which of the participants are working at an adequate pace or are lagging behind in the training process'
announcement_configuration_type.curso_bonificado.name: 'Subsidized course'
announcement_configuration_type.curso_bonificado.description: 'Subsidized courses are those carried out through the Fundación Tripartita and financed by companies through Social Security contributions'
announcement_configuration_type.chat.name: Chat
announcement_configuration_type.chat.description: 'Chat is a synchronous communication tool that allows course participants to interact in real time via text messages'
announcement_configuration_type.notificaciones.name: Notifications
announcement_configuration_type.notificaciones.description: 'Notifications are messages sent to course participants to inform them of important news or events'
announcement_configuration_type.mensajeria.name: Messaging
announcement_configuration_type.mensajeria.description: 'A messaging system is a communication system that allows course participants to send and receive private messages.'
announcement_configuration_type.foros.name: Forums
announcement_configuration_type.foros.description: 'A forum is an asynchronous communication space that allows course participants to exchange messages on a given topic'
announcement_configuration_type.diploma.name: Diploma
announcement_configuration_type.diploma.description: 'Diplomas are certificates given to course participants to certify their completion of a course.'
announcement_configuration_type.tutor_alerts.name: 'Activate tutor alerts'
announcement_configuration_type.tutor_alerts.description: 'Alerts are messages sent to the tutor of a course to inform him/her of important news or events'
announcement_configuration_type.encuesta_satisfaccion.name: 'Satisfaction survey'
announcement_configuration_type.encuesta_satisfaccion.description: 'Satisfaction surveys are questionnaires sent to course participants to find out their opinion about the course.'
announcement_configuration_type.finalizar_convocatoria.name: 'The course will remain active at the end of the call'
announcement_configuration_type.finalizar_convocatoria.description: 'The user will be able to access the course content after the course has ended'
announcement_configuration_type.firma_digital.name: 'Digital signature'
announcement_configuration_type.firma_digital.description: 'The digital signature is required to sign the attendance to a classroom course'
announcement_configuration_type.gestion_costes.name: 'Cost management'
announcement_configuration_type.gestion_costes.description: 'The cost management allows groups to indicate the cost of the call'
announcement_configuration_type.EMAIL_NOTIFICATION_ON_ANNOUNCEMENT.name: EMAIL_NOTIFICATION_ON_ANNOUNCEMENT
announcement_configuration_type.EMAIL_NOTIFICATION_ON_ANNOUNCEMENT.description: 'Enable e-mail notifications.'
announcement_configuration_type.NOTIFICATION_ON_ANNOUNCEMENT.name: NOTIFICATION_ON_ANNOUNCEMENT
announcement_configuration_type.NOTIFICATION_ON_ANNOUNCEMENT.description: 'Enable normal notifications'
announcement_configuration_type.objetivos_contenidos.name: 'Objectives and contents'
announcement_configuration_type.objetivos_contenidos.description: 'The objectives and contents of the course will be included in the diploma.'
announcement_configuration_type.dni.name: Dni
announcement_configuration_type.dni.description: 'The student''s ID will be included in the diploma'
announcement_configuration_type.template_excel.name: 'Registration template excel'
announcement_configuration_type.template_excel.description: 'This template will be used for student enrollment, using the HRBP Code, instead of the DNI.'
announcement_configuration_type.report_zip.name: ZIP
announcement_configuration_type.report_zip.description: 'Allowing the tutor to download group reports in ZIP format'
announcement_criteria.1.name: 'Minimum number of chapters to complete'
announcement_criteria.1.description: 'The minimum grade can be, for example, 70 out of 100'
announcement_criteria.2.name: 'Completing tasks'
announcement_criteria.2.description: 'Evaluation controls'
announcement_criteria.3.name: 'Maximum idle time'
announcement_criteria.3.description: 'For example, the user cannot be inactive for more than 10 minutes'
announcement_criteria.4.name: 'Complete activities'
announcement_criteria.4.description: 'The user must complete the proposed activities'
announcement_criteria.5.name: 'Hours of training completed'
announcement_criteria.5.description: 'For example, if the program has 20 hours, the user must complete the 20 hours.'
announcement_step_creation.ANNOUNCEMENT_COURSE.description: 'First step in the creation of the call for proposals'
announcement_step_creation.ANNOUNCEMENT_GENERAL_INFO.description: 'Second step of the call creation, where the general information is filled in'
announcement_step_creation.ANNOUNCEMENT_BONUS.description: 'This step depends on whether the customer has activated the bonus'
announcement_step_creation.ANNOUNCEMENT_STUDENTS.description: 'This is where students are added to the call, who may be assigned to a group'
announcement_step_creation.ANNOUNCEMENT_GROUPS.description: 'In this step the student groups created in the previous step are configured'
announcement_step_creation.ANNOUNCEMENT_COMMUNICATION.description: 'In this step the survey to be sent to the students is configured'
announcement_step_creation.ANNOUNCEMENT_SURVEY.description: 'In this step the survey to be sent to the students is configured'
announcement_step_creation.ANNOUNCEMENT_CERTIFICATE.description: 'In this step, the diplomas available on the platform are displayed for the customer to select the one he/she wants'
announcement_step_creation.ANNOUNCEMENT_ALERTS.description: 'These alerts are special alerts to inform the tutor of the events of the call'
class_room_virtual.zoom.name: Zoom
class_room_virtual.zoom.description: 'Online web conferencing platform, allows video calls in high definition, with the functionality to share desktop, whiteboard, chat, record the conference, share documents, and be able to access from anywhere as it is available for mobile devices'
class_room_virtual.clickmeeting.name: ClickMeeting
class_room_virtual.clickmeeting.description: 'ClickMeeting''s platform is one of the easiest to use webinar interfaces on the market and offers a multitude of flexible customization options'
class_room_virtual.jitsi.name: Jitsi
class_room_virtual.jitsi.description: 'Open Source solution for videoconferencing with encrypted connections and available for several operating systems'
class_room_virtual.plugnmeet.name: PlugnMeet
class_room_virtual.plugnmeet.description: 'Easy-to-integrate and highly customizable open source video conferencing software'
configuration_cliente_announcement.COMMUNICATION.description: 'This enables communications to be enabled within the call'
configuration_cliente_announcement.CERTIFICATE.description: 'This enables the downloading of diplomas within the call for applications'
configuration_cliente_announcement.SURVEY.description: 'This allows the polls to be enabled, to be inherited in the call'
configuration_cliente_announcement.ALERT.description: 'This allows you to enable alerts to be inherited in the alerts section of the tutor'
configuration_cliente_announcement.TEMPORALIZATION.description: 'Enables the temporalization of the chapters within a call for proposals'
configuration_cliente_announcement.BONIFICATION.description: 'Bonus of the call, especially for the call of the tripartite foundation'
configuration_cliente_announcement.ACCESS_CONTENT.description: 'Enables access to the contents of the call once it has been finalized'
configuration_cliente_announcement.DIGITAL_SIGNATURE.description: 'Enables digital signature in the call for applications, especially in classroom courses'
configuration_cliente_announcement.COST.description: 'Allows you to enable customers to apply costs on the call for proposals'
configuration_cliente_announcement.NOTIFICATION_ACTIVATE_ANNOUNCEMENT.description: 'Notifications upon call activation (email, front notification)'
configuration_cliente_announcement.CONFIGURATION_IBEROSTAR.description: 'This configuration is specially designed for Iberostar customers, so as not to affect the flow of funds'
configuration_cliente_announcement.REPORT.description: 'Enable the generation of reports in the calls for proposals'
type_course_announcement_step_creation.seleccionar_curso.name: 'Select course'
type_course_announcement_step_creation.seleccionar_curso.description: 'Select the course for which the call is to be created'
type_course_announcement_step_creation.convocatoria.name: 'Call for applications'
type_course_announcement_step_creation.convocatoria.description: 'Information on the call for proposals'
type_course_announcement_step_creation.bonificacion.name: Bonus
type_course_announcement_step_creation.bonificacion.description: 'Information on the call for proposals'
type_course_announcement_step_creation.alumnado.name: Students
type_course_announcement_step_creation.alumnado.description: 'Students are added to the course'
type_course_announcement_step_creation.grupos.name: Groups
type_course_announcement_step_creation.grupos.description: 'Group information is detailed and tutor is also added'
type_course_announcement_step_creation.comunicacion.name: Communication
type_course_announcement_step_creation.comunicacion.description: 'This step will depend on whether the customer has communication enabled or not'
type_course_announcement_step_creation.encuesta.name: Survey
type_course_announcement_step_creation.encuesta.description: 'This step will depend on whether the customer has the survey activated or not'
type_course_announcement_step_creation.diploma.name: Diploma
type_course_announcement_step_creation.diploma.description: 'This step will depend on whether the customer has activated the diploma or not'
type_course_announcement_step_creation.alertas.name: Alerts
type_course_announcement_step_creation.alertas.description: 'This may depend on the customer''s configuration'
type_diploma.easylearning.name: Default
type_diploma.easylearning.description: 'It is the diploma of the client company'
type_diploma.fundae.name: Fundae
type_diploma.fundae.description: 'It is the Fundae diploma'
type_diploma.hobetuz.name: Hobetuz
type_diploma.hobetuz.description: 'It is the Hobetuz diploma'
type_money.euro.name: Euro
type_money.euro.country: Spain
type_money.dolar_estadounidense.name: 'U.S. dollar'
type_money.dolar_estadounidense.country: 'United States'
section_default_front.mi_formacion.name: 'My Training'
section_default_front.mi_formacion.description: 'Within the assigned training, you can find all the courses you have been assigned'
section_default_front.formacion_adicional.name: 'Additional training'
section_default_front.formacion_adicional.description: 'In this section you will find all the open campus courses'
section_default_front.formacion_asignada.name: 'Assigned Training'
section_default_front.formacion_asignada.description: 'In this section you can find all the courses assigned to you'
setting.multi_idioma.name: Multi-language
setting.multi_idioma.description: 'Offering a multilingual interface'
setting.default_lenguage.name: 'Default language'
setting.default_lenguage.description: 'default language of the application'
setting.languages.name: Language
setting.languages.description: 'Languages available in the application'
setting.registro_libre.name: 'Free user registration'
setting.registro_libre.description: 'Free user registration'
setting.opinion_plataforma.name: 'Platform opinions'
setting.opinion_plataforma.description: 'Platform opinions'
setting.validacion_automatica.name: 'Automatic user registration validation'
setting.validacion_automatica.description: 'Automatic user registration validation'
setting.filtros_plataforma.name: 'Filters on the platform'
setting.filtros_plataforma.description: 'It is to activate or deactivate the filters in the platform'
setting.itinearios_plataforma.name: 'Itineraries on the platform'
setting.itinearios_plataforma.description: 'It is to activate or deactivate the itineraries in the platform'
setting.seccion_cursos.name: 'Course sections [FRONT]'
setting.seccion_cursos.description: 'Course sections [FRONT],'
setting.set_points_course.name: 'Establish points for the course'
setting.set_points_course.description: 'This is used to assign points to courses when creating or editing them, especially for the e-learning course'
setting.default_points_course.name: 'Default points'
setting.default_points_course.description: 'This value will be taken by default when awarding the score at the end of a course. It works by dividing 50% between the theoretical chapters and 50% between the evaluative chapters.'
setting.documentation_course.name: 'General information'
setting.documentation_course.description: 'Enabled activates the "General information" text field when creating a course'
setting.open_course.name: 'Open campus'
setting.open_course.description: 'Enabled, the possibility of activating/deactivating the open campus appears.'
setting.client_id.name: 'vimeo customer id'
setting.client_id.description: 'It is the Vimeo Gestionet client identifier.'
setting.client_secret.name: 'Secret vimeo client'
setting.client_secret.description: 'Secret vimeo client Gestionet'
setting.access_token.name: 'Access token'
setting.access_token.description: 'Gestionet access token'
setting.user_id.name: 'client id'
setting.user_id.description: 'Id of the user registered in vimeo Gestionet'
setting.project_id.name: 'Video chapter folder'
setting.project_id.description: 'It is the identifier where the resources of the video chapters are hosted'
setting.project_id_resource_course.name: 'Materials resource kit (call for proposals)'
setting.project_id_resource_course.description: 'It is the identifier of the folder where the videos related to the course materials and call are stored'
setting.project_id_task_course.name: 'Task resource folder'
setting.project_id_task_course.description: 'It is the identifier of the folder where the videos related to the course tasks and call are stored'
setting.project_id_video_Quiz.name: 'Videoquiz resource folder'
setting.project_id_video_Quiz.description: 'It is the identifier of the folder where the videos related to the videoquiz game are stored'
setting.project_id_Roleplay.name: 'Roleplay resource kit'
setting.project_id_Roleplay.description: 'Identified for video type resources in the roleplay'
setting.upload_sudomain.name: 'Upload to subdomain'
setting.upload_sudomain.description: 'This variable is used to upload videos and SCORM files, allowing you to overcome Cloudflare''s 100 Mb restrictions.'
setting.from_email.name: 'Sender email'
setting.from_email.description: 'It is the origin of the e-mails sent from the platform'
setting.from_name.name: 'Sender name'
setting.from_name.description: 'O nome do remetente exibido nos e-mails'
setting.from_cif.name: CIF
setting.from_cif.description: 'Company Tax ID'
setting.email_support.name: 'Email support'
setting.email_support.description: 'These emails are used to send support notifications'
setting.email_support_register.name: 'Receiving e-mail administrator'
setting.email_support_register.description: 'This email is used to receive registration requests on the platform'
setting.news.name: News
setting.news.description: 'Enabled activates the "News" module on the platform'
setting.foro.name: Forum
setting.foro.description: 'Enabled activates the "Forum" module on the platform'
setting.desafios.name: Duels
setting.desafios.description: 'Enabled activates the "Duels" module on the platform'
setting.secciones.name: Sections
setting.secciones.description: 'Enabled sections are displayed on the front'
setting.encuestas.name: Surveys
setting.encuestas.description: 'Enabled activates the "Surveys" module on the platform'
setting.active_cron_exports.name: 'Report compilation waiting'
setting.active_cron_exports.description: 'Enabled, the report is generated and placed in a waiting queue, and disabled, the download is direct.'
setting.gender_excel.name: '"Gender" field in xls'
setting.gender_excel.description: 'Enabled activates the "Genre" column in download xls reports'
setting.code.name: '"Code" field in xls'
setting.code.description: 'Enabled activates the "Code" column in download xls reports'
setting.finished_chapters.name: 'Completed chapters'
setting.finished_chapters.description: 'Enabled activates "Completed Chapters" in xls download reports'
setting.zoom_cliente_id.name: 'Zoom Customer ID'
setting.zoom_cliente_id.description: 'Zoom client ID--required to use the Zoom API'
setting.zoom_cliente_secret.name: 'Client Secret de Zoom'
setting.zoom_cliente_secret.description: 'Zoom client key--required to use the zoom API'
setting.zoom_account_id.name: 'Zoom account ID'
setting.zoom_account_id.description: 'Zoom customer account number--required to use the Zoom API'
setting.zoom_email.name: 'Zoom Email'
setting.zoom_email.description: 'Zoom client mail--required to use the Zoom API'
setting.clickmeeting_api_key.name: 'ClickMeeting API Key'
setting.clickmeeting_api_key.description: 'ClickMeeting client ID-- required to use the ClickMeeting API'
setting.clikmeeting_dirbase.name: 'ClickMeeting base directory'
setting.clikmeeting_dirbase.description: 'ClickMeeting server address'
setting.clikmeeting_events_paralel.name: 'ClickMeeting side events'
setting.clikmeeting_events_paralel.description: 'Number of contracted side events'
setting.plugnmeet_serverurl.name: 'plugNmeet server Url'
setting.plugnmeet_serverurl.description: 'plugNmeet server address'
setting.plugnmeet_api_key.name: 'plugNmeet API key'
setting.plugnmeet_api_key.description: 'plugNmeet client ID'
setting.plugnmeet_secret.name: 'plugNmeet secret key'
setting.plugnmeet_secret.description: 'plugNmeet client key'
setting.plugnmeet_analyticsurl.name: 'plugNmeet URL analytics'
setting.plugnmeet_analyticsurl.description: 'plugNmeet server address for analytics'
setting.zoom_urlreports.name: 'Zoom Report Url'
setting.zoom_urlreports.description: 'Address where the zoom reports are stored'
setting.plugnmeet_urlreports.name: 'plugNmeet reporting url'
setting.plugnmeet_urlreports.description: 'Address where plugNmeet reports are stored'
setting.clickmeeting_urlreports.name: 'ClickMeeting reporting URL'
setting.clickmeeting_urlreports.description: 'Address where ClickMeeting reports are stored'
setting.library_enabled.name: 'Newspaper archive'
setting.library_enabled.description: 'Enabled activates the "Newspaper Library" module on the platform'
setting.library_audio_local.name: 'Local  audio'
setting.library_audio_local.description: 'Local  audio'
setting.library_audio_path.name: 'Audio path'
setting.library_audio_path.description: 'Audio path'
setting.library_file_path.name: 'File path'
setting.library_file_path.description: 'File path'
setting.library_data_page_size.name: 'Data page size'
setting.library_data_page_size.description: 'Data page size'
setting.library_comments.name: Comments
setting.library_comments.description: Comments
setting.challenge_loops.name: 'Number of questions per duel'
setting.challenge_loops.description: 'Default number of questions that are part of a duel.'
setting.points_for_win.name: 'Victory points'
setting.points_for_win.description: 'Points awarded for winning a duel'
setting.points_for_lose.name: 'Points for defeat'
setting.points_for_lose.description: 'Points subtracted for losing a duel'
setting.points_fortie.name: 'Points for ties'
setting.points_fortie.description: 'Points awarded for tying a duel with successes'
setting.points_corrects.name: 'Points for a zero draw'
setting.points_corrects.description: 'Points awarded for tying a duel with zero hits'
setting.points_for_left.name: 'Points for abandonment'
setting.points_for_left.description: 'Points awarded for leaving the duel'
setting.total_duels.name: 'Maximum number of duels'
setting.total_duels.description: 'Maximum number of duels available for each participating person.'
setting.seconds_per_question.name: 'Seconds available per question'
setting.seconds_per_question.description: 'Time expressed in seconds, available to answer each of the questions.'
setting.user_dni.name: 'User ID'
setting.user_dni.description: 'This appears when creating or editing a user'
setting.edit_code.name: 'User code'
setting.edit_code.description: 'Enabled on user creation/modification the "Code" field appears'
setting.stats_acumulative.name: 'Cumulative statistics'
setting.stats_acumulative.description: 'This is in case you want the statistics to be cumulative'
setting.maximo_fechas.name: 'Maximum date range'
setting.maximo_fechas.description: 'Maximum number of days allowed for data consultation'
setting.maximo_horas.name: 'Maximum requests per hour'
setting.maximo_horas.description: 'Maximum requests allowed per hour'
setting.maximo_dia.name: 'Maximum requests per day'
setting.maximo_dia.description: 'Maximum requests allowed per day'
setting.fundae.name: Fundae
setting.fundae.description: 'If this is enabled when a call is published the users have to fill in all the necessary fields of the users_extra_fundae table'
setting.margen_entrada.name: 'Default input margin'
setting.margen_entrada.description: 'Default input margin, used in the QR code'
setting.margen_salida.name: 'Default output margin'
setting.margen_salida.description: 'Default output margin, used in the QR code'
setting.registrar_qr.name: 'Register with QR Session'
setting.registrar_qr.description: 'If enabled, sessions will be logged with QR.'
setting.maximo_alumnos.name: 'Maximum number of students per group'
setting.maximo_alumnos.description: 'Maximum number of students per group'
setting.min_score.name: 'Minimum passing score'
setting.min_score.description: 'Minimum passing score'
setting.types_action.name: 'Types of action'
setting.types_action.description: 'Types of action'
setting.materiales_convocatoria.name: 'Enable the creation of materials in a call for papers'
setting.materiales_convocatoria.description: 'Enable the creation of materials in a call for papers'
setting.tareas_convocatoria.name: 'Enable the creation of tasks in a call for proposals'
setting.tareas_convocatoria.description: 'Enable the creation of tasks in a call for proposals'
setting.minimo_minutos.name: 'Minimum downtime in minutes'
setting.minimo_minutos.description: 'Minimum downtime in minutes, applies to users who are on the platform.'
setting.timezones.name: 'Time zones allowed in the call for proposals'
setting.timezones.description: 'Time zone that can be configured in the call for applications'
catalog.1.name: 'Chapter types'
catalog.1.description: 'Configuration of the types of chapters, which will be available on the platform'
catalog.2.name: 'Types of courses'
catalog.2.description: 'Configuration of the types of courses, which will be available on the platform'
catalog.3.name: 'Approval criteria'
catalog.3.description: 'Configuration of the approval criteria, which will be available on the platform'
catalog.4.name: 'Tutor alerts'
catalog.4.description: 'Configuration of alerts for tutors, which will be available on the platform'
catalog.5.name: 'Types of diplomas'
catalog.5.description: 'Configuration of the types of diplomas, which will be available on the platform'
catalog.6.name: 'Configuration of the client in convocation'
catalog.6.description: 'Configuration of the steps to be shown in the call for proposals'
catalog.7.name: 'Currency types'
catalog.7.description: 'Configuration of the types of currencies, which will be available on the platform'
catalog.8.name: 'Group of configurations'
catalog.8.description: 'Group of configurations, which will be available on the platform'
catalog.9.name: Configurations
catalog.9.description: 'Configurations per group, which will be available on the platform'
catalog.10.name: Company
catalog.10.description: 'Companies of users, which will be available on the platform'
catalog.11.name: 'Professional Category'
catalog.11.description: 'Professional Categories of the users, which will be available on the platform'
catalog.12.name: 'User Work Center'
catalog.12.description: 'User Work Centers, which will be available on the platform'
catalog.13.name: 'User Work Department'
catalog.13.description: 'User Work Departments, which will be available on the platform'
catalog.14.name: 'User Study Level'
catalog.14.description: 'User Study Levels, which will be available on the platform'
catalog.15.name: 'Steps for the different types of courses'
catalog.15.description: 'Configuration of the steps for the different types of courses, which will be available on the platform'
catalog.16.name: 'Types of virtual classrooms'
catalog.16.description: 'Types of virtual classrooms for the different types of courses, which will be available in the platform'
catalog.17.name: 'Types of identification'
catalog.17.description: 'Types of identification available on the platform'
nps_question.text.name: Text
nps_question.text.descripction: 'Give us your feedback'
setting.help.user.name: 'Include help pdf in user menu'
setting.help.user.description: 'This help was created especially for iberostar.'
catalog.18.name: 'Modalities of on-site calls'
catalog.18.description: 'This is a special need for Iberostar'
setting.userPolicies_plataforma.name: 'Privacy Policy'
setting.userPolicies_plataforma.description: 'This variable is used to enable a modal on the front-end when the user does not accept the privacy policy.'
setting.course.tab.person.name: 'Course statistics per person'
setting.course.tab.stats.name: 'General course statistics'
setting.course.tab.opinions.name: "Opinions by course\n"
setting.documentation.name: Tutorials
setting.documentation.description: 'Enabled activates the "Tutorials" module on the platform where you can upload information of interest and associate it with administration profiles.'
setting.user_company.name: Companies
setting.user_company.description: 'Enabled activates the "Companies" module on the platform allowing you to create this filter and be assigned to a tutor in the call.'
setting.pages.name: Footer
setting.pages.description: 'Activate the footer on campus'
setting.lite_formation.name: 'General Statistics Training Group'
setting.lite_formation.description: 'General Statistics Training Group'
setting.lite_formation.formationHours.name: 'Training hours'
setting.lite_formation.formationHours.description: 'Total training hours and average training hours per person'
setting.lite_formation.peopleWithCourses.name: 'People with course'
setting.lite_formation.peopleWithCourses.description: 'People currently training and people who have completed at least one course'
setting.lite_formation.courseStartedAndFinished.name: 'Courses started, in process and completed'
setting.lite_formation.courseStartedAndFinished.description: 'Number of courses started, in progress and completed'
setting.lite_formation.requiredCourses.name: 'Required courses'
setting.lite_formation.requiredCourses.description: 'Mandatory courses assigned to a call or itinerary'
setting.lite_formation.general.name: General
setting.lite_formation.general.description: General
setting.lite_formation.openedCourses.name: 'Open courses'
setting.lite_formation.openedCourses.description: 'Voluntary courses'
setting.lite_formation.educativeStatus.name: 'Educational level'
setting.lite_formation.educativeStatus.description: 'Training status by point levels'
setting.lite_formation.gamifiedPills.name: 'Gamified pills'
setting.lite_formation.gamifiedPills.description: 'Number of gamified chapters, failures and successes in gamified tests'
setting.lite_formation.gamifiedTest.name: 'Test pills'
setting.lite_formation.gamifiedTest.description: 'Gamified tests used and successes and failures by test type'
setting.lite_formation.peoplePerformance.name: 'People performance'
setting.lite_formation.peoplePerformance.description: 'People performance'
setting.lite_formation.coursesByStars.name: 'Courses by score'
setting.lite_formation.coursesByStars.description: 'Star rating of courses'
setting.lite_formation.structureAndHotel.name: 'Departments and hotels'
setting.lite_formation.structureAndHotel.description: 'Percentage by group'
setting.lite_formation.schoolFinishedAndProgress.name: 'School Completed and in progress'
setting.lite_formation.schoolFinishedAndProgress.description: 'School with the most participation, courses in process and completed'
setting.lite_formation.coursesBySchool.name: 'Courses by school'
setting.lite_formation.coursesBySchool.description: 'Number of courses per category'
setting.lite_formation.coursesByDepartment.name: 'Courses by department'
setting.lite_formation.coursesByDepartment.description: 'Creation of courses by department'
setting.lite_formation.usersMoreActivesByCourses.name: 'Most active users by courses'
setting.lite_formation.usersMoreActivesByCourses.description: 'Most and least active people from completed courses'
setting.lite_evolution.name: 'Evolution Group in General Statistics'
setting.lite_evolution.description: 'Evolution Group in General Statistics'
setting.lite_evolution.trainedPerson.name: 'Trained people'
setting.lite_evolution.trainedPerson.description: 'People who have completed at least one course'
setting.lite_evolution.startedCourses.name: 'Courses started'
setting.lite_evolution.startedCourses.description: 'Courses started'
setting.lite_evolution.proccessCourses.name: 'Course in progress'
setting.lite_evolution.proccessCourses.description: 'Course in progress'
setting.lite_evolution.finishedCourses.name: 'Completed courses'
setting.lite_evolution.finishedCourses.description: 'Completed courses'
setting.lite_evolution.segmentedHours.name: 'Hour segmentation'
setting.lite_evolution.userNewInPlatformThanFinishedOneCourse.name: 'New users who have completed a course'
setting.lite_evolution.userNewInPlatformThanFinishedOneCourse.description: 'People new to the platform who have completed at least one course'
setting.lite_demography.name: 'Demographics Group in General Statistics'
setting.lite_demography.description: 'Demographics Group in General Statistics'
setting.lite_demography.usersBySexAndAge.name: 'Users by gender and age'
setting.lite_demography.usersBySexAndAge.description: 'Users by sex and age'
setting.lite_demography.ageDistribution.name: 'Distribution by age'
setting.lite_demography.ageDistribution.description: 'Distribution by age'
setting.lite_demography.deviceDistribution.name: 'Distribution by device'
setting.lite_demography.deviceDistribution.description: 'Distribution by devices'
setting.lite_demography.usersByCountries.name: 'Distribution by country'
setting.lite_demography.usersByCountries.description: 'Distribution by country'
setting.lite_activity.name: 'General Statistics Activity Group'
setting.lite_activity.description: 'General Statistics Activity Group'
setting.lite_activity.activityInfo.name: 'Activity information'
setting.lite_activity.activityInfo.description: 'People active on the portal, registered people, people who have accessed at least once in the last 30 days, deactivated people and people who have never entered the platform'
setting.lite_activity.accessDays.name: 'Access for days'
setting.lite_activity.accessDays.description: 'Access days'
setting.lite_activity.platformAccessByHours.name: 'Access by platform and hours'
setting.lite_activity.platformAccessByHours.description: 'Access times to the platform by day and hour (heat map)'
setting.lite_activity.courseStartTime.name: 'Distribution by course start times'
setting.lite_activity.courseStartTime.description: 'Course start times'
setting.lite_activity.courseEndTime.name: 'Distribution by course completion hours'
setting.lite_activity.courseEndTime.description: 'Course completion hours (heat map)'
setting.lite_activity.coursesStartedVsFinished.name: 'Courses started versus courses completed'
setting.lite_activity.coursesStartedVsFinished.description: 'Courses started vs courses completed'
setting.lite_activity.usersMoreActivesByActivity.name: 'Most active users'
setting.lite_activity.usersMoreActivesByActivity.description: 'More and less active people and their time of use on the platform'
setting.lite_itinerary.name: 'Itinerary Group in General Statistics'
setting.lite_itinerary.description: 'Itinerary Group in General Statistics'
setting.lite_itinerary.itinerariesStartedAndFinished.name: 'Started and finished itineraries'
setting.lite_itinerary.itinerariesStartedAndFinished.description: 'Started and finished itineraries'
setting.lite_itinerary.itinerariesCompletedByCountries.name: 'Completed itineraries by country'
setting.lite_itinerary.itinerariesCompletedByCountries.description: 'Completed itineraries by country'
setting.survey.hide_empty_comment.name: 'Hide opinions with empty comment'
setting.survey.hide_empty_comment.description: 'Enabled hides opinions without comment'
setting.survey.show_only_ratings.name: 'Show rating only'
setting.survey.show_only_ratings.description: 'Enabled, it only shows the star rating without comments on campus, regardless of whether there is a written comment or not.'
app.survey.post_nps.enabled.name: Self-publishing
app.survey.post_nps.enabled.description: 'Enabled comment is published automatically, disabled requires administrator validation'
setting.lite_evolution.segmentedHours.description: Hours
setting.course.tab.person.description: 'Enabled, the "People" section is activated at the detail level of a course.'
setting.course.showDeactivatedCourses.name: 'Viewing deactivated courses (Campus)'
setting.course.showDeactivatedCourses.description: 'Enabled, deactivated courses (in gray) are displayed on campus'
catalog.19.name: 'Translations administrator'
catalog.19.description: 'Administrator translations'
setting.lenguage.platform: 'Administrator Translations'
setting.module.announcement.name: 'Call for applications'
setting.module.announcement.description: 'Enabled activates the "Calls" module on the platform'
course.diploma.index: 'Personalized diplomas'
setting.zip.day_available_until.name: 'Days available'
setting.zip.day_available_until.description: 'Number of days available before zip is automatically deleted.'
catalog.20.name: 'Extra fields call'
course.diploma.filters: 'Activate additional filters in diploma report'
setting.lenguage.platform.description: 'Languages available in the administrator panel'
translations_admin.title1: 'Assigned training'
translations_admin.title2: 'Additional training'
translations_admin.title3: 'Courses assigned'
translations_admin.title4: 'Volunteer courses'
setting.course.tab.stats.description: 'Enabled, the "Statistics" section is activated at the detail level of a course.'
setting.course.tab.options.description: 'Enabled, the "Opinions" section is activated at the level of detail of a course.'
course.diploma.index.description: 'Enabled, the "Diplomas" section is activated when creating/modifying a course'
setting.use.filter_in_ranking.name: 'Use filters in the user ranking'
setting.use.filter_in_ranking.description: 'Allows you to select from the menu the filters of the categories with which a user wishes to be compared. If this option is disabled, the user will be compared by default with all the filters available on the platform'
setting.use.include_only_first_category_name: 'Show only the first category of the filter in the ranking'
setting.use.include_only_first_category_description: 'If active, only the user''s first linkage is displayed. Otherwise, all associated categories are shown. For example, if the category is "country", the user could be linked to both Spain and Nicaragua.'
setting.email_support_error.name: 'Support mail for errors'
setting.email_support_error.description: 'Mailboxes to which platform incidents will be sent'
setting.export.task.slot_quantity.name: 'Number of task slots per user'
setting.export.task.slot_quantity.description: 'Number of slots available for processing export tasks per user.'
setting.export.task.long_running_type_tasks.name: 'Types of long-term tasks'
setting.export.task.long_running_type_tasks.description: 'List of types of tasks that are considered long duration for export.'
setting.export.zip_task.slot_quantity.name: 'Number of slots for zip tasks per user'
setting.export.zip_task.slot_quantity.description: 'Number of slots available for processing zip compression tasks per user.'
setting.export.zip_task.long_running_type_tasks.name: 'Types of long-term zip tasks'
setting.export.zip_task.long_running_type_tasks.description: 'List of types of zip tasks that are considered long-term.'
setting.export.task.user_pending_max_count_task.name: 'Maximum number of pending tasks per user'
setting.export.task.user_pending_max_count_task.description: 'Maximum number of pending tasks that a user can have in queue.'
setting.export.task.timeout.name: 'Time limit for tasks'
setting.export.task.timeout.description: 'Maximum time in seconds before an export task is considered expired.'
setting.export.zip_task.timeout.name: 'Time limit for zip tasks'
setting.export.zip_task.timeout.description: 'Maximum time in seconds before a zip compression task is considered expired.'
setting.export.task.timeout_seconds.name: 'Timeout time for tasks in TIMEOUT status'
setting.export.task.timeout_seconds.description: 'Maximum time in seconds after which a task in TIMEOUT status is no longer considered to be running.'
type_diploma.novomatic.name: Novomatic
type_diploma.novomatic.description: 'It is the Novomatic diploma'
app.announcement.managers.sharing.name: 'Enable the creation of tasks in a call for proposals'
app.announcement.managers.sharing.description: 'Enable the creation of tasks in a call for proposals'
