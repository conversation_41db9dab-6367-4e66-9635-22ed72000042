import { MenuItemModel } from '@/contexts/layout/models/MenuItem.model.js'
import { BASE_LOCATION } from '@/core/constants/general.constant.js'

export const LAYOUT_MENU_ITEMS = [
  new MenuItemModel({
    id: 1,
    title: 'Gestión de cursos',
    icon: 'university',
    link: '',
    subItems: [
      new MenuItemModel({ id: 11, title: 'Categorías', link: 'categories', icon: 'boxes' }),
      new MenuItemModel({
        id: 12,
        title: 'Cursos',
        link: `${BASE_LOCATION}home?ea-controller=course`,
        icon: 'university',
      }),
      new MenuItemModel({ id: 13, title: 'Opiniones', link: 'opinions', icon: 'sms' }),
      new MenuItemModel({ id: 14, title: 'Itinerarios', link: 'itineraries', icon: 'clipboard-list' }),
      new MenuItemModel({ id: 15, title: 'Encuestas', link: 'surveys', icon: 'poll' }),
      new MenuItemModel({ id: 16, title: 'Convocatorias', link: 'announcements', icon: 'calendar-alt' }),
    ],
  }),
  new MenuItemModel({
    id: 2,
    title: 'Gestión de ayuda',
    icon: 'question-circle',
    link: '',
    subItems: [
      new MenuItemModel({ id: 21, title: 'Contenido ayuda', link: 'help-content', icon: 'question-circle' }),
      new MenuItemModel({ id: 22, title: 'Categoría ayuda', link: 'help-category', icon: 'question-circle' }),
    ],
  }),
  new MenuItemModel({
    id: 3,
    title: 'Gestión de usuarios',
    icon: 'users',
    link: '',
    subItems: [
      new MenuItemModel({ id: 31, title: 'Usuarios', link: 'users', icon: 'users' }),
      new MenuItemModel({ id: 32, title: 'Filtros', link: 'users-filters', icon: 'filter' }),
    ],
  }),
  new MenuItemModel({
    id: 4,
    title: 'Estadísticas',
    icon: 'signal',
    link: '',
    subItems: [
      new MenuItemModel({ id: 41, title: 'Estadísticas', link: 'stats', icon: 'signal' }),
      new MenuItemModel({ id: 42, title: 'Herramienta Excel', link: 'excel-tools', icon: 'file-excel' }),
      new MenuItemModel({ id: 43, title: 'Informes y diplomas', link: 'reports', icon: 'file' }),
    ],
  }),
  new MenuItemModel({ id: 5, title: 'Library', icon: 'photo-video', link: 'library' }),
  new MenuItemModel({
    id: 6,
    title: 'SuperAdmin',
    icon: 'shield-alt',
    link: '',
    subItems: [
      new MenuItemModel({ id: 51, title: 'General', link: 'super-admin-main', icon: 'cog' }),
      new MenuItemModel({ id: 52, title: 'Catálogos', link: 'super-admin-catalogs', icon: 'bars' }),
      new MenuItemModel({ id: 53, title: 'Secciones', link: 'super-admin-sections', icon: 'signal' }),
      new MenuItemModel({ id: 54, title: 'Developer', link: 'super-admin-dev', icon: 'laptop' }),
      new MenuItemModel({ id: 55, title: 'Filtros', link: 'super-admin-filters', icon: 'filter' }),
      new MenuItemModel({ id: 56, title: 'SAML', link: 'super-admin-saml', icon: 'shield-alt' }),
      new MenuItemModel({ id: 57, title: 'Integraciones', link: 'super-admin-integrations', icon: 'shield-alt' }),
      new MenuItemModel({ id: 58, title: 'Import users', link: 'super-admin-import-users', icon: 'users' }),
      new MenuItemModel({ id: 59, title: 'Cron', link: 'super-admin-cron', icon: 'clock' }),
      new MenuItemModel({ id: 60, title: 'PHP info', link: 'super-admin-php', icon: ['fab', 'php'] }),
      new MenuItemModel({ id: 61, title: 'LTI', link: 'super-admin-lti', icon: 'pencil-square' }),
    ],
  }),
  new MenuItemModel({ id: 7, title: 'Ir al campus', icon: 'user', link: `${window.location.origin}/campus/` }),
]
