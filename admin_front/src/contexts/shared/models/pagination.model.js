export class Pagination {
  constructor({ page = 1, pageSize = 10, total_items = 0, total_pages = 0 } = {}) {
    this.currentPage = page || 1
    this.pageSize = pageSize || 10
    this.totalItems = total_items || 0
    this.totalPages = total_pages || Math.ceil(this.totalItems / this.pageSize)
  }

  getNextPage() {
    return Math.min(this.currentPage + 1, this.totalPages)
  }

  getPrevPage() {
    return Math.max(this.currentPage - 1, 1)
  }

  getPayloadData() {
    return { page: this.currentPage, pageSize: this.pageSize }
  }
}
