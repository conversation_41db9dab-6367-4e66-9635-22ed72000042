import { globSync } from 'glob'
import inquirer from 'inquirer'

function promptFactory(message, choices) {
  return inquirer.prompt([
    {
      type: 'list',
      name: 'result',
      choices,
      message,
    },
  ])
}

export function clientSelector() {
  const clientList = ['default', ...globSync('./src/clients/*').map((client) => client.replace('src/clients/', ''))]
  if (clientList.length < 2) return { result: 'default' }

  return promptFactory('Selecciona el cliente', clientList)
}

export async function environmentSelector() {
  const envList = ['dev', 'staging']
  return promptFactory('Selecciona el entorno', envList)
}

export async function mockServerSelector() {
  const envList = ['No', 'Si']
  return promptFactory('Desea inicializar el servidor offline', envList)
}
