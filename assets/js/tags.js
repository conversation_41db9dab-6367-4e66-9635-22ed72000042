
import $ from 'jquery';
import select2 from 'select2';                       // globally assign select2 fn to $ element
import 'select2/dist/css/select2.css';


$(document).ready(function () {
    var lastResults = [];

    (function () {
        var tagInput = $("input[name$='Course[tagsText]']");
        console.log("valor del input", tagInput.val())
        function tags($input) {
              $input.attr("type", "hidden").select2({
                tags: true,
                tokenSeparators: [","],
                placeholder: 'Please enter tags',               
                createSearchChoice: function (term, data) {
                    if ($(data).filter(function () {
                        return this.text.localeCompare(term) === 0;
                    }).length === 0) {
                        return {
                            id: term,
                            text: term
                        };
                    }
                },
                multiple: true,
                ajax: {
                    url: $input.data('ajax'),
                    dataType: "json",
                    data: function (params) {
                        var query = {
                            search: params.term,
                            type: 'public'
                        }
                        console.log("Query", query);
                        return query;
                    },

                    processResults: function (data) {
                        console.log("Resultado", data);
                        lastResults = data;
                        return {
                            results: data
                        };
                    },

                },

                initSelection: function (element, callback) {
                    var data = [];
                    function splitVal(string, separator) {
                        var val, i, l;
                        if (string === null || string.length < 1) {
                            return [];
                        }
                        val = string.split(separator);
                        for (i = 0, l = val.length; i < l; i = i + 1) {
                            val[i] = $.trim(val[i]);
                        }
                        return val;
                    }
                    console.log("init selection");
                    $(splitVal(element.val(), ",")).each(function () {
                        data.push({
                            id: this,
                            text: this
                        });
                    });

                    callback(data);

                  //  $("input[name$='[tagsText]']").append(data).tigger('change');
                }
            });
        }
        if (tagInput.length > 0) {
            tags(tagInput);           
        }
        $("#Course_tagsText").trigger('change');

    }()); 
});
