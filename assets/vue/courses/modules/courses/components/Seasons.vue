<template>
  <div>
    <div class="d-flex align-items-center justify-content-center" v-if="isLoading">
      <Spinner />
    </div>
    <div v-else>
      <div
        v-if="$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.SEASONS)"
        class="d-flex justify-content-end mb-4">
        <button
          class="btn btn-primary"
          @click="openModal"
        >
          {{ $t('COURSE.ADD_SEASONS') }}
        </button>
      </div>
      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th>{{ $t("NAME") }}</th>
              <th>{{ $t("TYPE") }}</th>
              <th v-if="$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.SEASONS)"></th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="season in seasons" :key="season.id">
              <td>{{ season.name }}</td>
              <td>{{ nameSeason(season.type) }}</td>
              <td
                v-if="$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.SEASONS)"
                class="text-end">
                <button class="btn btn-primary btn-sm" @click="editSeason(season)">
                  <i class="fa fa-pencil"></i>
                </button>
                <button class="btn btn-danger btn-sm" @click="deleteSeason(season.id)">
                  <i class="fa fa-trash fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr v-if="seasons.length === 0">
              <td colspan="12">
                <BaseNotResult />
              </td>
            </tr>
          </tbody>
        </table>
        <p class="mt-4"><b>{{ seasons.length }}</b> {{ $t('RESULTS') }}</p>
      </div>
      <BaseModal
        v-if="showModal"
        :identifier="'modal-chapter-new'"
        :title="isEditing ? $t('COURSE.EDIT_SEASON') : $t('COURSE.ADD_SEASONS')"
        @close="closeModal"
      >
        <div class="d-flex align-items-center justify-content-center" v-if="isSubmitting">
          <Spinner />
        </div>
        
        <form v-else @submit.prevent="handleSubmit">
          <div class="mb-3">
            <label for="season-name" class="form-label">{{ $t('NAME') }}</label>
            <input
              id="season-name"
              type="text"
              class="form-control"
              v-model="form.name"
              required
            />
          </div>
          <div class="mb-3">
            <label for="season-type" class="form-label">{{ $t('TYPE') }}</label>
            <select
              id="season-type"
              class="form-select"
              v-model="form.type"
              required
            >
              <option value="sequential">{{ $t('COURSE.SEASON.TYPE.SEQUENTIAL') }}</option>
              <option value="free">{{ $t('COURSE.SEASON.TYPE.FREE') }}</option>
              <option value="exam">{{ $t('COURSE.SEASON.TYPE.EXAM') }}</option>
            </select>
          </div>
          <div class="d-flex justify-content-end">
            <button type="submit" class="btn btn-primary" :disabled="isLoading">
              <span v-if="isLoading" class="spinner-border spinner-border-sm me-2"></span>
              {{ isEditing ? $t('COMMON.UPDATE') : $t('COMMON.CREATE') }}
            </button>
          </div>
        </form>
      </BaseModal>
    </div>
  </div>
</template>

<script>
import { get } from "vuex-pathify";
import $ from "jquery";
import Spinner from "../../../../admin/components/base/Spinner.vue";
import BaseNotResult from "../../../../base/BaseNotResult.vue";
import BaseModal from "../../../../base/BaseModal.vue";
import { COURSE_PERMISSIONS } from '../../../../common/utils/auth/permissions/course.permissions'

export default {
  name: "Seasons",
  components: { BaseNotResult, Spinner, BaseModal },
  data() {
    return {
      isLoading: true,
      isEditing: false,
      showModal: false,
      isSubmitting: false,
      form: {
        name: "",
        type: "sequential",
      },
    };
  },
  computed: {
    COURSE_PERMISSIONS() {
      return COURSE_PERMISSIONS
    },
    ...get("coursesModule", {
      seasons: "getSeasons"
    }),
  },
  async created() {
    this.isLoading = true;
    await this.fetchSeasons();
    this.isLoading = false;
  },
  methods: {
    async fetchSeasons() {
      await this.$store.dispatch("coursesModule/fetchSeasons", this.$route.params.id);
    },
    nameSeason(name) {
      switch (name) {
        case "sequential":
          return this.$t("COURSE.SEASON.TYPE.SEQUENTIAL");
        case "exam":
          return this.$t("COURSE.SEASON.TYPE.EXAM");
        case "free":
          return this.$t("COURSE.SEASON.TYPE.FREE");
        default:
          return name;
      }
    },
    openModal() {
      this.resetForm();
      this.showModal = true;
      this.$nextTick(() => {
        $("#modal-chapter-new").modal("show");
      });
      this.isEditing = false;
    },
    closeModal() {
      this.showModal = false;
      $("#modal-chapter-new").modal("hide");
      this.resetForm();
    },
    resetForm() {
      this.form = {
        name: "",
        type: "sequential",
      };
    },
    editSeason(season) {
      this.showModal = true;
      this.form = { ...season };
      this.isEditing = true;
      this.$nextTick(() => {
        $("#modal-chapter-new").modal("show");
      });    
    },
    async handleSubmit() {
      try {
        this.isSubmitting = true;
        const formData = {
          name: this.form.name,
          type: this.form.type
        };

        if (this.isEditing) {
          await this.$store.dispatch("coursesModule/updateSeason", {
            id: this.form.id,
            form: formData,
          });
        } else {
          await this.$store.dispatch("coursesModule/createSeason", {
            id: this.$route.params.id,
            form: formData,
          });
        }
        await this.fetchSeasons();
        this.$toast.success(this.$t("SUCCESS"));
        this.closeModal();
      } catch (error) {
        this.$toast.error(this.$t("FAILED"));
      } finally {
        this.isSubmitting = false;
      }
    },
    async deleteSeason(id) {
      try {
        await this.$alertify.confirmWithTitle(
          this.$t("DELETE"),
          this.$t("COMMON_AREAS.QUESTION_DELETE"),
          async () => {
            let response = await this.$store.dispatch("coursesModule/deleteSeason", id);
            if(response.error) {
              this.$toast.error(response.message);
              return;
            }
            await this.fetchSeasons();
            this.$toast.success(this.$t("DELETE_SUCCESS"));
          },
          () => {}
        );
      } catch (e) {
        this.$toast.error(this.$t("DELETE_FAILED"));
      }
    },
  },
};
</script>
