import { KEY_TAB_NAME } from "./constants";

export function removeKeyTabName() {
  localStorage.removeItem(KEY_TAB_NAME);
}

export function getTabData() {
  try {
    const raw = localStorage.getItem(KEY_TAB_NAME);
    if (!raw) return null;
    return JSON.parse(raw);
  } catch (e) {
    return null;
  }
}

export function setTabData(courseId, tab) {
  const data = { id: courseId, tab };
  localStorage.setItem(KEY_TAB_NAME, JSON.stringify(data));
}
