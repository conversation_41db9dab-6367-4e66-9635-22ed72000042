<script>
import {get} from "vuex-pathify";
import ForumContent from "../../../common/components/forum/ForumContent.vue";
import ForumThreads from "../../../common/components/forum/ForumThreads.vue";
import ChatGroup from "../../../common/components/chat/ChatGroup.vue";
import GlobalChat from "../../../common/components/chat/GlobalChat.vue";
import Spinner from "../../../admin/components/base/Spinner.vue";

export default {
  name: "AnnouncementChat",
  components: {Spinner, GlobalChat, ChatGroup, ForumThreads, ForumContent},
  data() {
    return {
      selectedGroupId: -1,
    };
  },
  computed: {
    loadingCalledUsers: get('announcementModule/loadingCalledUsers'),
    groupStudents: get('announcementModule/calledUsers'),
    announcement: get('announcementModule/announcement'),
    groups: get('announcementModule/announcement@groupBasicInfo'),
    channels: get('announcementModule/announcement@channels'),
    typeCourse() {
      // [1] Teleformación [2] Presencial [3] Mixto [4] Aula Virtual
      return (this.announcement?.course) ? this.announcement.course.typeID : 1;
    },
    isTeleformationOrMixte() {
      return this.typeCourse === 1 || this.typeCourse === 3
    },
    showChat() {
      return this.isTeleformationOrMixte && (this.announcement?.comunications?.CHAT)
    },


    channelsInGroup() {
      if (!this.showChat) return null;
      const group = this.channels.find(g => g.group === this.selectedGroup?.groupInfo?.id);
      return group?.channels ?? {};
    },

    mainChatChannel() {
      if (!this.showChat) return null;
      return this.channelsInGroup.chat;
    },

    publicChannel() {
      if (!this.showChat) return null;
      return {name: this.$t('CHAT.CHANNEL.PUBLIC'), ...this.channelsInGroup.public};
    },

    groupsInChannel() {
      if (!this.showChat) return null;
      return [this.channelsInGroup.groups];
    },
    selectedGroup() {
      return this.groupStudents.find(g => g.groupInfo.id === this.selectedGroupId);
    }
  },
  watch: {
    groups: {
      immediate: true,
      handler: function () {
        if ((this.selectedGroupId == null || this.selectedGroupId < 1) && this.groups.length > 0)
          this.selectedGroupId = this.groups[0].id;
      }
    }
  }
}
</script>

<template>
  <div class="AnnouncementChat">
    <div class="col-12 d-flex align-items-center justify-content-center" v-if="loadingCalledUsers">
      <spinner />
    </div>
    <global-chat
        v-else
        :chat-channel-parent="mainChatChannel"
        :public-channel="publicChannel"
        :group-channel-parent="publicChannel"
        :groups="groups"
        :selected-group-id="selectedGroupId"
        :users="selectedGroup?.users ?? []"
        :entity-id="mainChatChannel?.entityId"
        :entity-type="mainChatChannel?.entityType"
        @select-group="(id) => { selectedGroupId = id }"
    />
  </div>
</template>

<style scoped lang="scss">

</style>
