<template>
  <div class="AnnouncementStep2">
    <div class="col-12 d-flex flex-row flex-wrap">
      <div class="col-xs-12 col-md-9">
        <add-remove :source-items="tutors"
                    :realtime="false"
                    :title="$t('ANNOUNCEMENT.TUTORS') + ''"
                    :enable-all="false"
                    v-model="data.selectedTutors"
                    :loading-source="false"
                    :loading-selected="false"
        />
      </div>
      <div class="col-xs-12 col-md-3">
        <div class="col-12 form-group required">
          <label for="startAt">{{ $t('START_AT') }}</label>
          <input id="startAt" name="startAt" type="datetime-local" class="form-control" v-model="data.startAt">
        </div>
        <div class="col-12 form-group required">
          <label for="startAt">{{ $t('FINISH_AT') }}</label>
          <input id="finishAt" name="finishAt" type="datetime-local" class="form-control" v-model="data.finishAt">
        </div>
      </div>
    </div>
    <div class="is-subsidized d-flex flex-row" v-if="subsidizerActive">
      <div class="col-md-6" >
        <button-with-description v-model="data.subsidized"
                                 icon="fa fa-coins"
                                 title="ANNOUNCEMENT.SUBSIDIZED.TITLE"
                                 description="ANNOUNCEMENT.SUBSIDIZED.DESCRIPTION" />
      </div>
      <div class="col-xs-12 col-md-6">
        <div class="form-group col-12" v-show="data.subsidized">
          <label for="subsidizerEntity">{{ $t('ANNOUNCEMENT.SUBSIDIZER_ENTITY') }}</label>
          <select name="subsidizerEntity" id="subsidizerEntity" class="custom-select" v-model="data.subsidizerEntity">
            <option value="">{{ $t("INPUT.PLACEHOLDER.COLLABORATION") }}</option>
            <option
              v-for="(entity, index) in subsidizerEntity"
              :value="entity"
              :key="entity"
            >
              {{ $t(`ANNOUNCEMENT.SUBSIDIZER_ENTITIES.${index}`) }}
            </option>
          </select>
        </div>
        <div class="form-group col-12" v-show="data.subsidized">
          <label for="subsidizer">{{ $t('ANNOUNCEMENT.SUBSIDIZER') }}</label>
          <select name="subsidizer" id="subsidizer" class="custom-select" v-model="data.selectedSubsidizer">
            <option value="">{{ $t('INPUT.PLACEHOLDER.TECHNICIAN') }}</option>
            <option v-for="user in subsidizer" :value="user.id">{{ user.name }}</option>
          </select>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {get} from "vuex-pathify";

import AddRemove from "../../../common/components/select/AddRemove.vue";
import ButtonWithDescription from "../../../common/components/ButtonWithDescription.vue";

export default {
  name: "AnnouncementStep2",
  components: { AddRemove, ButtonWithDescription },
  props: {
    announcement: null,
    subsidizerEntity: {
      type: Array|Object,
      default() {
        return []
      }
    },
    subsidizer: {
      type: Array|Object,
      default() {
        return []
      }
    },
    tutors: {
      type: Array|Object,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      data: {
        selectedTutors: this.announcement?.selectedTutors ?? [],
        subsidized: this.announcement?.subsidized ?? false,
        startAt: this.announcement?.startAt ?? '',
        finishAt: this.announcement?.finishAt ?? '',
        subsidizerEntity: this.announcement?.subsidizerEntity ?? '',
        selectedSubsidizer: this.announcement?.selectedSubsidizer ?? ''
      }
    };
  },
  computed: {
    subsidizerActive: get('configModule/config@subsidizerActive'),
  },
  watch: {
    data: {
      handler: function (old, newVal) {
        this.$emit('updated', this.data);
      },
      deep: true
    }
  }
}
</script>

 <style scoped lang="scss"> 
.is-subsidized{
  margin-top: 2rem;
}
</style>
