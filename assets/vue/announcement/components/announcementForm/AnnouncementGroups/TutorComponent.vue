<template>
  <div class="TutorComponent">
    <div class="TutorCard">
      <h1>{{ $t("ANNOUNCEMENT.TUTOR.TITLE") }}</h1>
      <button
          class="EditButton btn btn-sm btn-primary"
          type="button"
          v-if="!isNotified && tutor?.tutorId > 0"
          @click="tutorFormModal"
      >
        <i class="fa fa-pencil"></i>
      </button>
      <button :id="`user-group-${groupId}-tutor-modal-open`" v-if="!isNotified" style="display: none;" data-bs-toggle="modal"
              :data-bs-target="`#user-group-${groupId}-tutor-modal`"></button>

      <div class="Tutor" v-if="tutor?.tutorId > 0">
        <div
            class="avatar"
            :style="{
              'background-image': `url(/uploads/users/avatars/default.svg)`,
            }"
        ></div>
        <div>
          <span>{{ tutor?.name }}</span>
          <button
              type="button"
              class="btn btn-primary"
              data-bs-toggle="modal"
              :data-bs-target="`#user-group-${groupId}-tutor-profile-modal`"
          >
            <i class="fa fa-eye"></i>
            {{ $t("ANNOUNCEMENT.FORM.GROUP.SHOWPROFILE") }}
          </button>
        </div>
      </div>

      <button
          v-else
          style="margin-top: 3rem"
          type="button"
          class="btn btn-primary mb-auto"
          @click="tutorFormModal"
          data-bs-toggle="modal"
          :data-bs-target="`#user-group-${groupId}-tutor-modal`"
      >
        <i class="fa fa-plus"></i> {{ $t("ANNOUNCEMENT.FORM.GROUP.ADD_TUTOR") }}
      </button>
    </div>

    <tutor-form-modal
        :group-id="groupId"
        :tutors="tutors"
        :show-form="showForm"
        :type-identifications="typeIdentifications"
        :main-identification="mainIdentification"
        :companies="companies"
        v-model="innerTutor"
        @input="setTutor"
        @close="showForm = false"
    />

    <BaseModal
        class="TutorProfileModal--container"
        :identifier="`user-group-${groupId}-tutor-profile-modal`"
        :title="$t('ANNOUNCEMENT.MODALS.TUTOR_PROFILE')"
    >
      <template>
        <tutor-view-component :group-id="groupId" :tutor="tutor" />
      </template>
    </BaseModal>
  </div>
</template>

<script>
import {get} from "vuex-pathify";
import TutorFormModal from "../../tutor/TutorFormModal.vue"

import TutorViewComponent from "./TutorViewComponent.vue";

export default {
  name: "TutorComponent",
  components: {TutorViewComponent, TutorFormModal},
  props: {
    groupId: {
      type: Number|String,
      required: true
    },
    tutor: {
      type: Object|Array,
      default: null
    },
    tutors: {
      type: Object | Array,
      default: () => [],
    },
  },
  data() {
    return {
      showForm: false,
      innerTutor: null
    };
  },
  computed: {
    isNotified: get("announcementFormModule/isNotified"),
    companies: get("announcementFormModule/companies"),
    typeIdentifications: get("announcementFormModule/typeIdentifications"),
    mainIdentification: get("announcementFormModule/mainIdentification"),
  },
  methods: {
    tutorFormModal() {
      this.innerTutor = this.tutor;
      this.showForm = true;
      const btn = document.getElementById(`user-group-${this.groupId}-tutor-modal-open`);
      if (btn) btn.click();
    },
    setTutor(data) {
      this.showForm = false;
      this.$emit('input', data);
    }
  }
}
</script>


<style scoped lang="scss">
.TutorComponent {
  .TutorCard {
    display: flex;
    flex-flow: column;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--color-neutral-mid);
    padding: 1rem;
    border-radius: 5px;
    position: relative;

    .EditButton {
      position: absolute;
      top: 1rem;
      right: 1rem;
    }

    h1 {
      font-size: 22px;
      color: var(--color-neutral-darkest);
      width: 100%;
      text-align: left;
    }

    span {
      width: 100%;
      font-size: 20px;
      color: var(--color-neutral-darkest);
      font-weight: 500;
    }

    .avatar {
      @include avatar;
      width: 100px !important;
      height: 100px !important;
      background-color: unset;
    }

    .Tutor {
      display: grid;
      grid-template-columns: 105px 1fr;
      gap: 1rem;
      align-items: center;

      & > div:not(.avatar) {
        display: flex;
        flex-flow: row wrap;
        row-gap: 0.5rem;
      }
    }
  }
}
.TutorProfileModal--container {
  :deep(.modal-body) {
    padding: 0 !important;
  }

  :deep(.modal-dialog) {
    max-width: 720px;
  }
}
</style>
