import axios from 'axios';
/**
 * Share common state for froala editor
 */
export default {
  namespaced: true,
  state: {
    isSavingTask: true,
  },
  getters: {
    getIsSavingTask(state) {
      return state.isSavingTask;
    },
  },
  mutations: {
    SET_IS_SAVING_TASK(state, isSaving) {
      state.isSavingTask = isSaving;
    },
  },
  actions: {
    async addNewTaskToAnnouncement({ commit }, { id, formData }) {
      const url = `/admin/announcement/${id}/task`;
      const result = await axios.post(url, formData);
      return result.data;
    },

    async updateAnnouncementTask({ commit }, { announcementId, taskId, formData }) {
      const url = `/admin/announcement/${announcementId}/task/${taskId}`;
      const result = await axios.post(url, formData);
      return result.data;
    },

    updateReviewTask({ commit }, payload) {
      return axios.post('/admin/review-homework-teacher', payload).then((result) => result.data);
    },

    async getGroupsAnnouncement({ commit }, idAnnouncement) {
      const result = await axios.get(`/admin/announcement/groups/${idAnnouncement}`);
      return result.data?.data;
    },
  },
};
