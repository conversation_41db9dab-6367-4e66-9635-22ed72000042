const AnnouncementConfigurationType = {
    TEMPORALIZATION: 1,
    SUBSIDIZED: 2,
    CHAT: 3,
    NOTIFICATION: 4,
    SMS: 5,
    FORUM: 6,
    CERTIFICATES: 7,
    ALERTS: 8,
    SURVEY: 9,
    ACTIVE_COURSE_AT_END: 10,
    DIGITAL_SIGNATURE: 11,
    COST: 12,
    EMAIL_NOTIFICATION_ON_ANNOUNCEMENT: 13,
    NOTIFICATION_ON_ANNOUNCEMENT: 14,

    getIdFromVModel: (vModel) => {
        const separated = vModel.split('-');
        return parseInt(separated.pop());
    },
    getVModelFromId: (id) => {
        return `configuration-${id}`;
    }
};

export default AnnouncementConfigurationType;

