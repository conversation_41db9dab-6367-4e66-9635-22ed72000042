<template>
  <div class="CourseApp">
    <router-view></router-view>

    <div id="modal-saving" class="modal fade saving">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <loader :is-loaded="showGlobalLoader" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import 'bootstrap';

import Loader from "../admin/components/Loader.vue";
import $ from "jquery";

export default {
  name: "CourseApp",
  components: {Loader},
  $,
  computed: {
    showGlobalLoader() {
      return this.$store.getters['getShowGlobalLoader'];
    }
  },
  watch: {
    showGlobalLoader(newValue) {
      if (newValue) {
        $('#modal-saving').modal({
          backdrop: 'static',
          keyboard: false
        }, 'show');
      } else {
        $('#modal-saving').modal('hide');
      }
    }
  }
}
</script>

 <style scoped lang="scss"> 
.CourseApp {
  .saving {
    .modal-dialog {
      max-width: 100px;
      .modal-content {
        align-items: center;
        justify-content: center;
      }
    }
  }
}
</style>
