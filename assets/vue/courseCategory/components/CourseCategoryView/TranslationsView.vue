<script>
import Translation from "../../../common/components/Translation.vue";
import {get} from "vuex-pathify";

export default {
  name: "TranslationsView",
  components: {Translation},
  data() {
    return {
      locale: 'es'
    };
  },
  computed: {
    translations: get('courseCategoryModule/category@translations')
  }
}
</script>

<template>
<div class="TranslationsView">
  <translation v-model="locale" direction="vertical">
    <template v-slot:content>
      <div v-for="t in translations" :key="t.locale" v-if="locale === t.locale">
        <div class="form-group col-12">
          <label>Name</label>
          <input type="text" class="form-control" v-model="t.name" readonly>
        </div>
      </div>
    </template>
  </translation>
</div>
</template>

<style scoped lang="scss">
.TranslationsView {
  padding: 1rem 3rem 1rem 3rem;
}
</style>
