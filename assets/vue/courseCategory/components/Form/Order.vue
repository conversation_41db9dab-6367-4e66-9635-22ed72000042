<script>
import {sync, get } from "vuex-pathify";
import BaseSwitch from "../../../base/BaseSwitch.vue";
import Draggable from "vuedraggable";
import Spinner from "../../../base/BaseSpinner.vue";

export default {
  name: "Order",
  components: {Spinner, BaseSwitch, Draggable},
  computed: {
    courses: sync('courseCategoryModule/courses'),
    loadingCourses: get('courseCategoryModule/loadingCourses'),
    orderType: sync('courseCategoryModule/form@orderType'),
    orderProperties: sync('courseCategoryModule/form@orderProperties'),
  },
  watch: {
    orderType: {
      immediate: true,
      handler: function () {
        if (this.orderType === 'manual') {
          this.$store.dispatch('courseCategoryModule/getCoursesOrder');
        }
      }
    }
  },
  created() {
    const actions = [];
    actions.push({
      name: this.$t("SAVE"),
      event: "onSave",
      class: "btn btn-primary",
    });

    if (this.$isGranted("ROLE_ADMIN") && this.$route.params.id != -1)
      actions.push({
        name: this.$t("DELETE"),
        event: "onDelete",
        class: "btn btn-danger",
      });

    this.$store.dispatch("contentTitleModule/setActions", {
      route: this.$route.name,
      actions,
    });
  },
  methods: {
    changedCoursePosition(event) {
      // console.log(this.courses);
    }
  }
}
</script>

<template>
  <div class="Order">
    <div class="Order--auto">
      <div class="form-check w-100">
        <input type="radio" class="form-check-input" name="orderType" id="orderTypeAuto" value="auto" v-model="orderType">
        <label for="orderTypeAuto" class="form-check-label">{{ $t('COURSE_CATEGORY.SORT.AUTO') }}</label>
      </div>

      <div class="w-100 Content">
        <div class="d-flex flex-row flex-nowrap align-items-center mb-1">
          <BaseSwitch tag="form-auto-order-showNewAtStart"
                       :disabled="orderType !== 'auto'"
                       v-model="orderProperties.showNewAtStart"
          />
          <span class="ml-3">{{ $t('COURSE_CATEGORY.SORT.NEW_AT_START') }}</span>
        </div>
        <div class="col-12">
          <label>{{ $t('COURSE_CATEGORY.SORT.CRITERIA') }}</label>
          <div class="form-check w-100">
            <input type="radio" class="form-check-input" name="orderCriteria" id="orderCriteriaCreatedAt"
                   :disabled="orderType !== 'auto'"
                   value="createdAt" v-model="orderProperties.orderCriteria">
            <label for="orderCriteriaCreatedAt" class="form-check-label">{{ $t('COURSE_CATEGORY.SORT.CRITERIA_CREATED_AT') }}</label>
          </div>

          <div class="form-check w-100">
            <input type="radio" class="form-check-input" name="orderCriteria" id="orderCriteriaAlphabetic"
                   :disabled="orderType !== 'auto'"
                   value="alphabetic" v-model="orderProperties.orderCriteria">
            <label for="orderCriteriaAlphabetic" class="form-check-label">{{ $t('COURSE_CATEGORY.SORT.CRITERIA_ALPHABETIC') }}</label>
          </div>
        </div>
      </div>
    </div>
    <div class="Order--manual">
      <div class="form-check w-100">
        <input type="radio" class="form-check-input" name="orderType" id="orderTypeManual" value="manual" v-model="orderType">
        <label for="orderTypeManual" class="form-check-label">{{ $t('COURSE_CATEGORY.SORT.MANUAL') }}</label>
      </div>

      <div v-if="loadingCourses && orderType === 'manual'" class="w-100 d-flex flex-row align-items-center justify-content-center">
        <spinner />
      </div>

      <Draggable class="w-100 Order--manual--courses" :list="courses" draggable=".Course" @end="changedCoursePosition">
        <div class="Course drag-el" v-for="(c, index) in courses" :key="c.id">
          <div class="Image" :style="{'background-image': `url(${c.image})`}">
            <span class="number">{{ index + 1 }}</span>
          </div>
          <div class="Content">
            <span>{{ c.name }}</span>
            <div class="info mt-auto">
              <i :class="c.typeCourseIcon"></i>
              <button type="button" class="tChapters ml-2">{{ c.totalChapters }}</button> {{ $t('CHAPTERS.LABEL.PLURAL') }}
            </div>
          </div>
        </div>
      </Draggable>
    </div>
  </div>
</template>

<style scoped lang="scss">
.form-check-label {
  margin-left: 1rem;
}

.Order--auto, .Order--manual {
  & > .form-check {
    border-bottom: 1px solid var(--color-neutral-mid);
    label {
      color: var(--color-neutral-darkest);
      font-weight: bold;
    }
  }
}

.Order--auto {
  .Content {
    padding: .5rem 3rem .5rem 3rem;
  }
}

.Order--manual {
  &--courses {
    display: grid;
    column-gap: .5rem;
    row-gap: 1rem;

    @media #{min-medium-screen()} {
      grid-template-columns: repeat(3, 1fr);
    }

    .Course {
      display: grid;
      grid-template-columns: 120px 1fr;
      border: 1px solid var(--color-neutral-mid);
      border-radius: 5px;
      width: 100%;
      .Image {
        position: relative;
        width: 120px;
        height: 120px;
        //background-color: #0b2e13;
        background-size: cover;
        .number {
          width: 30px;
          height: 30px;
          position: absolute;
          background-color: #FFFFFF;
          color: var(--color-neutral-darkest);
          top: 5px;
          left: 5px;
          text-align: center;
          font-weight: bold;
        }
      }

      .Content {
        padding: .5rem;
        display: flex;
        flex-flow: column;
        width: 100%;
        align-items: flex-start;
        & > span {
          width: 100%;
          font-size: 17px;
          font-weight: bold;
          color: var(--color-neutral-darkest);
        }

        .info {
          color: var(--color-neutral-darkest);
          font-size: 16px;

          .tChapters {
            background-color: var(--color-neutral-mid);
            border-radius: 3px;
            width: 30px;
            height: 30px;
            border: none;
            font-weight: bold;
          }
        }
      }
    }
  }
}
</style>
