import {make} from "vuex-pathify";
import axios from "axios";

const state = {
    loading: true,
    items: [],
    pagination: {
        page: 1,
        totalItems: 0
    },
};
const getters = {
    ...make.getters(state),
};
const mutations = {
    ...make.mutations(state),
};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions: {
        async saveNews({ commit }, formData) {
            const headers = {
                'Content-Type': 'multipart/form-data'
            };
            const result = await axios.post('/admin/news', formData, { headers });
            return result.data;
        },

        async updateNews({ commit }, { id, formData}) {
            const headers = {
                'Content-Type': 'multipart/form-data'
            };
            const result = await axios.post(`/admin/news/${id}/update`, formData, { headers });
            return result.data;
        },

        async getNews({ commit, getters }) {
            commit('SET_LOADING', true);
            try {
                const { page } = getters['pagination'];
                const result = await axios.get(`/admin/news/${page}`);
                const { error, data } = result.data;
                if (!error) {
                    commit('SET_ITEMS', data.items);
                    commit('SET_PAGINATION', {
                        page: page,
                        totalItems: data['total-items']
                    });
                }
                return result.data;
            } finally {
                commit('SET_LOADING', false);
            }
        },

        async deleteNew({ commit, dispatch }, id) {
            const result = await axios.delete(`/admin/new/${id}`);
            const { error } = result.data;
            if (!error) dispatch('getNews');
            return result.data;
        },

        async getNew({ commit }, id) {
            const result = await axios.get(`/admin/new/${id}`);
            return result.data;
        }
    }
}
