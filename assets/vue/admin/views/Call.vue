<template>
  <div>
    <div class="content-panel">
      <div class="card mb-3">
        <div class="card-header" v-if="textTranslator">
          {{ textTranslator.user_title }}
        </div>

        <div class="card-body" v-if="textTranslator">
          <div class="row">
            <div class="col-md-12">
              <form v-on:submit.prevent="search()" class="form-inline">
                <label class="sr-only" for="search">Search</label>

                <div class="col-md-12 input-group mb-2 mr-sm-2">
                  <transition name="fade">
                    <input
                      v-model="searchQuery"
                      type="text"
                      class="form-control"
                      id="search"
                      :placeholder="textTranslator.search_user"
                    />
                  </transition>
                </div>

                <div class="col-md-6 mb-2" v-if="categories">
                  <Multiselect
                    v-model="categoryQuery"
                    :options="categories"
                    :multiple="true"
                    :placeholder="textTranslator.search_category"
                    track-by="name"
                    label="name"
                  ></Multiselect>
                </div>

                <div class="col-md-6 mb-2" v-if="departaments">
                  <Multiselect
                    v-model="departamentQuery"
                    :options="departaments"
                    :multiple="true"
                    :placeholder="textTranslator.search_departament"
                    track-by="name"
                    label="name"
                    :custom-label="customLabel"
                  ></Multiselect>
                </div>
                <div class="col-md-6 mb-2" v-if="center">
                  <Multiselect
                    v-model="centerQuery"
                    :options="center"
                    :multiple="true"
                    :placeholder="textTranslator.search_center"
                    track-by="name"
                    label="name"
                    :custom-label="customLabel"
                  ></Multiselect>
                </div>
                <div class="col-md-6 mb-2" v-if="countries">
                  <Multiselect
                    v-model="countryQuery"
                    :options="countries"
                    :multiple="true"
                    :placeholder="textTranslator.search_country"
                    track-by="name"
                    label="name"
                  ></Multiselect>
                </div>

                <div class="col-md-6 mb-2" v-if="divisions">
                  <Multiselect
                      v-model="divisionQuery"
                      :options="divisions"
                      :multiple="true"
                      :placeholder="textTranslator.search_division"
                      track-by="name"
                      label="name"
                  ></Multiselect>
                </div>

                <div class="col-md-12">
                  <div class="row">
                    <div class="col-md-6">
                      <button
                          type="submit"
                          class="btn btn-primary mb-2 btn-block btn-lg"
                      >
                        {{ textTranslator.search }}
                      </button>
                    </div>
                    <div class="col-md-6" v-if="searchResults">
                      <button
                          type="button"
                          class="btn btn-primary mb-2 btn-block btn-lg"
                          v-on:click.prevent="callEverybody()"
                      >
                        Convocar a todos
                      </button>
                    </div>
                  </div>

                </div>

              </form>
            </div>

            <transition name="fade">
              <div v-if="error" class="col-md-12">
                <div
                  class="alert alert-danger alert-dismissible fade show"
                  role="alert"
                >
                  {{ error }}
                </div>
              </div>
            </transition>
          </div>
        </div>

        <transition name="slide-fade">
          <div v-if="searchResults" class="card-body">
            <h6 class="card-subtitle mb-2 text-muted">
              {{ textTranslator.result_found }}: {{searchResults.length}}
            </h6>

            <div id="results">
              <table class="table datagrid">
                <thead class="thead-light">
                  <tr>
                    <th><span>User</span></th>

                    <th><span>E-mail</span></th>

                    <th class="text-right">
                      <a
                          href="#"
                          @click="clearResults()"
                        ><i class="fa fa-trash"></i> {{ textTranslator.clear_result }}
                      </a>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(result, i) in searchResults" :key="i">
                    <td>{{ result.firstName }} {{ result.lastName }}</td>

                    <td>{{ result.email }}</td>

                    <td class="text-right">
                      <button
                        type="button"
                        class="btn"
                        @click="call(result.id)"
                      >
                        <i class="fa fa-plus"></i>
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </transition>
      </div>
    </div>

    <div
      class="
        d-flex
        flex-row
        justify-content-between
        align-content-center
        w-100
        pb-3
      "
      v-if="textTranslator"
    >
      <div class="content-header-title">
        <h1 class="title">{{ textTranslator.user_called }} :</h1>
      </div>
    </div>

    <div class="content-panel" v-if="textTranslator">
      <div class="content-panel-body with-rounded-top without-padding">
        <loader :isLoaded="!showCalled"></loader>
        <table v-if="showCalled" class="table with-rounded-top datagrid">
          <thead class="thead-light">
            <tr>
              <th><span>User</span></th>

              <th><span>E-mail</span></th>

              <th></th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(call, i) in called" :key="i">
              <td>{{ call.user.firstName }} {{ call.user.lastName }}</td>

              <td><a href="">{{ call.user.email }}</a></td>

              <td class="text-right">
                <button
                  type="button"
                  class="btn"
                  :disabled="call.notified"
                  @click="notify(call.id)"
                >
                  <i class="fa fa-envelope"></i>
                </button>

                <button type="button" class="btn" @click="unCall(call.id)">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div
        v-if="called && called.length"
        class="content-panel-footer without-padding without-border"
      >
        <div class="list-pagination">
          <div class="list-pagination-counter">
            <strong>{{ called.length }}</strong>
            {{ textTranslator.user_called }}
          </div>
        </div>
      </div>
    </div>

    <div>
      <loader :isLoaded="!showCalled"></loader>
    </div>
  </div>
</template>
<script>
import { get } from "vuex-pathify";
import Loader from "../components/Loader";
import Multiselect from "vue-multiselect";

export default {
  name: "call",
  components: {
    Loader,
    Multiselect,
  },

  props: ["id"],

  data() {
    return {
      called: undefined,
      searchResults: undefined,
      searchQuery: undefined,
      categoryQuery: undefined,
      departamentQuery: undefined,
      centerQuery: undefined,
      countryQuery: undefined,
      divisionQuery: undefined,
      error: undefined,
      refreshing: false,
      categories: undefined,
      departaments: undefined,
      center: undefined,
      countries: undefined,
      division: undefined,
      textTranslator: undefined,
    };
  },

  async created() {
    await this.getCalled();
    await this.getFilters();
  },

  computed: {
    ...get("callModule", ["isLoading"]),

    showCalled() {
      return (!this.isLoading() && this.called) || this.refreshing;
    },
  },

  methods: {
    async getCalled(refreshing = false) {
      if (refreshing) this.refreshing = refreshing;
      this.called = await this.$store.dispatch(
        "callModule/fetchCalled",
        this.id
      );
    },

    async getFilters() {
      const filters = await this.$store.dispatch(
        "callModule/fetchFilters",
        this.id
      );
      this.categories = filters.categories;
      this.departaments = filters.departaments;
      this.center = filters.center;
      (this.countries = filters.countries),
      (this.divisions = filters.divisions),
        (this.textTranslator = filters.translator);
    },

    async search() {
      // this.clearResults();
      if (
        this.searchQuery ||
        this.categoryQuery ||
        this.departamentQuery ||
        this.centerQuery ||
        this.countryQuery ||
        this.divisionQuery
      ) {
        this.searchResults = await this.$store.dispatch(
          "callModule/fetchSearch",
          {
            announcement: this.id,
            searchQuery: this.searchQuery,
            categoryQuery: this.categoryQuery,
            departamentQuery: this.departamentQuery,
            centerQuery: this.centerQuery,
            countryQuery: this.countryQuery,
            divisionQuery: this.divisionQuery,
          }
        );
      }
    },

    async callEverybody() {
      if (
          this.searchQuery ||
          this.categoryQuery ||
          this.departamentQuery ||
          this.centerQuery ||
          this.countryQuery ||
          this.divisionQuery
      ) {
        let refresh = this.$store.dispatch(
            "callModule/fetchCallEverybody",
            {
              announcement: this.id,
              searchQuery: this.searchQuery,
              categoryQuery: this.categoryQuery,
              departamentQuery: this.departamentQuery,
              centerQuery: this.centerQuery,
              countryQuery: this.countryQuery,
              divisionQuery: this.divisionQuery,
            }
        );

        if(refresh) {
          this.getCalled(true);
          this.clearResults();
        };
      }
    },

    async call(user) {
      try {
        let result = await this.$store.dispatch("callModule/fetchCall", {
          announcement: this.id,
          user: user,
        });
        await this.getCalled(true);
      } catch (error) {
        if (error.response.status === 409) {
          this.showError(error.response.data.message);
        }
      }
    },

    async unCall(user) {
      try {
        let result = await this.$store.dispatch("callModule/fetchUnCall", {
          announcement: this.id,
          user: user,
        });

        await this.getCalled(true);
      } catch (error) {
        if (error.response.status === 409) {
          this.showError(error.response.data.message);
        }
      }
    },

    async notify(user) {
      try {
        let result = await this.$store.dispatch("callModule/fetchNotify", {
          announcement: this.id,
          user: user,
        });

        await this.getCalled(true);
      } catch (error) {
        if (error.response.status === 409) {
          this.showError(error.response.data.message);
        }
      }
    },

    showError(message) {
      this.error = message;
      setTimeout(() => (this.error = undefined), 3000);
    },

    clearResults() {
      this.searchResults = this.searchQuery = undefined;
    },

    customLabel({ code, name }) {
      return `${code} – ${name}`;
    },
  },
};
</script>
<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>
<style scoped lang="scss">
@import "../assets/config/_transitions.scss";

#results {
  max-height: 200px;
  overflow: auto;
}
</style>
