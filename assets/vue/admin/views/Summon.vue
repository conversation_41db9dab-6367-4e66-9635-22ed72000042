<template>
  <div>
    <div class="content-panel">
      <div class="card mb-3">
        <div class="card-header">Search users for call to the challenge:</div>

        <div class="card-body">
          <div class="row">
            <div class="col-md-8">
              <form v-on:submit.prevent="search()" class="form-inline">
                <label class="sr-only" for="search">Search</label>

                <div class="input-group mb-2 mr-sm-2">
                  <input v-model="searchQuery" type="text" class="form-control" id="search" placeholder="Search users...">
                </div>

                <button type="submit" class="btn btn-primary mb-2">Search</button>
              </form>
            </div>

            <transition name="fade">
              <div v-if="error" class="col-md-4">
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                  {{ error }}
                </div>
              </div>
            </transition>
          </div>
        </div>

        <transition name="slide-fade">
          <div v-if="searchResults" class="card-body">
            <h6 class="card-subtitle mb-2 text-muted">Search results:</h6>

            <div id="results">
              <table class="table datagrid">
                <thead class="thead-light">
                <tr>
                  <th><span>User</span></th>

                  <th><span>E-mail</span></th>

                  <th class="text-right"><a href="#" @click="clearResults()"><i class="fa fa-trash"></i> Clear results</a></th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(result, i) in searchResults" :key="i">
                  <td>{{result.firstName}} {{result.lastName}}</td>

                  <td>{{result.email}}</td>

                  <td class="text-right"><button type="button" class="btn" @click="call(result.id)"><i class="fa fa-plus"></i></button></td>
                </tr>
                </tbody>
              </table>
            </div>
          </div>
        </transition>
      </div>
    </div>

    <div class="d-flex flex-row justify-content-between align-content-center w-100 pb-3">
      <div class="content-header-title">
        <h1 class="title">Called Users:</h1>
      </div>
    </div>

    <div class="content-panel">
      <div class="content-panel-body with-rounded-top without-padding ">
        <loader :isLoaded="!showCalled" ></loader>
        <table v-if="showCalled" class="table with-rounded-top datagrid">
          <thead class="thead-light">
          <tr>
            <th><span>User</span></th>

            <th><span>E-mail</span></th>

            <th></th>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(call, i) in called" :key="i">
            <td>{{call.user.firstName}} {{call.user.lastName}}</td>

            <td>{{call.user.email}}</td>

            <td class="text-right">
              <button type="button" class="btn" :disabled="call.notified" @click="notify(call.id)"><i class="fa fa-envelope"></i></button>

              <button type="button" class="btn" @click="unCall(call.id)"><i class="fa fa-trash"></i></button>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
      <div v-if="called && called.length" class="content-panel-footer without-padding without-border">
        <div class="list-pagination">
          <div class="list-pagination-counter">
            <strong>{{ called.length }}</strong> called users
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { get } from 'vuex-pathify';
import Loader from "../components/Loader";

export default {
  name: "summon",
  components: {
    Loader
  },

  props: ['id'],

  data() {
    return {
      called: undefined,
      searchResults: undefined,
      searchQuery: undefined,
      categoryQuery: undefined,
      error: undefined,
      refreshing: false,
      categories: undefined,
    }
  },

  async created() {
    await this.getCalled();
  },

  computed: {
    ...get('summonModule', [
      'isLoading',
    ]),

    showCalled() {
      return ((!this.isLoading() && this.called) || this.refreshing )
    },
  },

  methods: {
    async getCalled(refreshing = false) {
      if(refreshing) this.refreshing = refreshing;
      this.called = await this.$store.dispatch('summonModule/fetchCalled', this.id);
    },

    async search() {
      if(this.searchQuery) {
        this.searchResults = await this.$store.dispatch('summonModule/fetchSearch', {
          'challenge': this.id,
          'searchQuery': this.searchQuery,
        });
      }
    },

    async call(user) {
      try{
        let result = await this.$store.dispatch('summonModule/fetchCall', {
          'challenge': this.id,
          'user': user,
        });

        await this.getCalled(true);

      }
      catch (error) {
        console.log('error', error.response);
        if(error.response.status === 409) {
          this.showError(error.response.data.message);
        }
      }
    },

    async unCall(user) {
      try{
        let result = await this.$store.dispatch('summonModule/fetchUnCall', {
          'challenge': this.id,
          'user': user,
        });

        await this.getCalled(true);

      }
      catch (error) {
        if(error.response.status === 409) {
          this.showError(error.response.data.message);
        }
      }
    },

    async notify(user) {
      try{
        let result = await this.$store.dispatch('summonModule/fetchNotify', {
          'challenge': this.id,
          'user': user,
        });

        await this.getCalled(true);

      }
      catch (error) {
        if(error.response.status === 409) {
          this.showError(error.response.data.message);
        }
      }
    },

    showError(message) {
      this.error = message;
      setTimeout(() => this.error = undefined, 3000);
    },

    clearResults() {
      this.searchResults = this.searchQuery = undefined
    },
  }
}
</script>
<style scoped lang="scss">
@import "../assets/config/_transitions.scss";

#results{
  max-height: 200px;
  overflow: auto;
}
</style>
