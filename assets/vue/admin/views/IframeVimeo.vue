<template>
  <div class="IframeVimeo">
    <vimeo-player
      ref="player"
      :video-url="urlvideo"
      @ready="onReady"
      @error="onError"
      :options="{
          responsive: true
        }"
    >
    </vimeo-player>

    <div class="messageVideo" v-if="inProccessLoad">
      <p v-if="translate">!{{translate.text_good}}!</p>
      <p v-if="translate">
       <Spinner/> {{translate.optimizing_video}}
      </p>
    </div>
  </div>
</template>

<script>
import Spinner from "../components/base/Spinner";

export default {
  name: "iframe-vimeo",

   components: {
    Spinner,
  },

  props: ["urlvideo"],

  data() {
    return {
      inProccessLoad: 0,
      translate:undefined
    };
  },

  computed: {
    player() {
      return this.$refs.player.player;
    },
  },

  async created()
  {
    const data = await this.$store.dispatch("uploadModule/translateTextComponent");
    this.translate = data.data;    
  },
  
  methods: {
    onReady() {     
      this.inProccessLoad = 0;
    },

    onError() {
      this.inProccessLoad = 1;     
      setInterval(() => {
        this.showStateVideo();
      }, 60000);
    },

    showStateVideo() {
      if (this.inProccessLoad == 1) {       ;
        location.reload();
      }
    },
  },
};
</script>

 <style scoped lang="scss"> 
.IframeVimeo {
  margin: 1rem;
  .messageVideo {
    margin: 2rem;
    background: #eff0f0;
    width: 70%;
    padding: 2rem;
    p:nth-child(1) {
      font-size: 4rem;
      margin-bottom: 0px;
      text-align: center;     
    }

    p:nth-child(2) {
      font-size: 1.5rem;
      text-align: center;
    }
  }
}
</style>