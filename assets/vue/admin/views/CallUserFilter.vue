<template>
  <div class="mt-4">
    <div class="accordion" id="accordionExample" v-if="textTranslator">
      <div class="accordion-item">
        <h2 class="accordion-header" id="headingOne">
          <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne"
                  aria-expanded="true" aria-controls="collapseOne">
            {{ textTranslator.user_title }}
          </button>
        </h2>
        <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne"
             data-bs-parent="#accordionExample">
          <div class="accordion-body">
            <div class="card-body" v-if="textTranslator">
              <div class="row">
                <div class="col-md-12">
                  <form v-on:submit.prevent="search()" class="form-inline">
                    <label class="sr-only" for="search">Search</label>

                    <div class="col-md-12 input-group mb-2 mr-sm-2">
                      <transition name="fade">
                        <input
                            v-model="searchQuery"
                            type="text"
                            class="form-control"
                            id="search"
                            :placeholder="textTranslator.search_user"
                        />
                      </transition>
                    </div>

                    <div class="col-md-6 mb-2" v-for="(item, index) in filterCategories" :key="index">
                      <Multiselect
                          v-model="filtersQuerys[item.name]"
                          :options="item.filters"
                          :multiple="true"
                          :placeholder="'Buscar por '+item.name"
                          track-by="name"
                          label="name"
                      ></Multiselect>
                    </div>

                    <div class="col-md-12">
                      <div class="row">
                        <div class="col-md-6">
                          <button
                              type="submit"
                              class="btn btn-primary mb-2 btn-block btn-md"
                          >
                            {{ textTranslator.search }}
                          </button>
                        </div>
                        <div class="col-md-6" v-if="searchResults">
                          <button
                              type="button"
                              class="btn btn-primary mb-2 btn-block btn-md"
                              v-on:click.prevent="callEverybody()"
                          >
                            {{ translationsVue.announcements_configureFields_annoucement_all }}
                          </button>
                        </div>
                      </div>

                    </div>

                  </form>
                </div>

                <transition name="fade">
                  <div v-if="error" class="col-md-12">
                    <div
                        class="alert alert-danger alert-dismissible fade show"
                        role="alert"
                    >
                      {{ error }}
                    </div>
                  </div>
                </transition>
              </div>
            </div>

            <transition name="slide-fade">
              <div v-if="searchResults" class="card-body">
                <h6 class="card-subtitle mb-2 text-muted">
                  {{ textTranslator.result_found }}: {{searchResults.length}}
                </h6>

                <div id="results">
                  <table class="table datagrid">
                    <thead class="thead-light">
                    <tr>
                      <th><span>{{ translationsVue.user_label_in_singular }}</span></th>

                      <th><span>{{ translationsVue.user_configureFields_email }}</span></th>

                      <th class="text-right">
                        <a
                            href="#"
                            @click="clearResults()"
                        ><i class="fa fa-trash"></i> {{ textTranslator.clear_result }}
                        </a>
                      </th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr v-for="(result, i) in searchResults" :key="i">
                      <td>{{ result.firstName }} {{ result.lastName }}</td>

                      <td>{{ result.email }}</td>

                      <td class="text-right">
                        <button
                            type="button"
                            class="btn"
                            @click="call(result.id)"
                        >
                          <i class="fa fa-plus"></i>
                        </button>
                      </td>
                    </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </transition>
          </div>
        </div>
      </div>
    </div>

    <div class="card p-2 mt-3">
      <div
          class="
        d-flex
        flex-row
        justify-content-between
        align-content-center
        w-100
        pb-3
      "
          v-if="textTranslator"
      >
        <div class="content-header-title">
          <h6>{{ textTranslator.user_called }}:
            <span class="badge bg-secondary text-white" v-if="subsidized">{{ called.length }}/{{ maxUser }}</span>
            <span class="badge bg-secondary text-white" v-else>{{ called.length }}</span>
          </h6>
        </div>
      </div>
      <div class="content-panel" v-if="textTranslator">
        <div class="content-panel-body with-rounded-top without-padding">
          <loader :isLoaded="!showCalled"></loader>
          <table v-if="showCalled" class="table with-rounded-top datagrid">
            <thead class="thead-light">
            <tr>
              <th class="text-center">--</th>
              <th><span>{{ translationsVue.user_label_in_singular }}</span></th>

              <th><span>{{ translationsVue.user_configureFields_email }}</span></th>

              <th></th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="(call, i) in pageOfUsers" :key="i">
              <td>
                <Messages :user="call.user"
                          :messages-student="call.messages"/>
              </td>
              <td><a :href="call.linkUser">{{ call.user.firstName }} {{ call.user.lastName }}</a>
              </td>

              <td>{{ call.user.email }}</td>

              <td class="text-right">
                <button
                    type="button"
                    class="btn"
                    :disabled="call.notified"
                    @click="notify(call.id)"
                >
                  <i class="fa fa-envelope"></i>
                </button>

                <button type="button" class="btn" @click="unCall(call.id)">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            </tbody>

          </table>
          <div class="row mt-2" v-if="called.length > 10">
            <div class="col-md-9">
              <jw-pagination :items="called" v-on:changePage="onUserChangePage"
              ></jw-pagination>
            </div>
            <div class="col-md-3">
              <strong>{{ called.length }}</strong>
              {{ textTranslator.user_called }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div>
      <loader :isLoaded="!showCalled"></loader>
    </div>
  </div>
</template>
<script>
import {get}        from "vuex-pathify";
import JwPagination from 'jw-vue-pagination';
import Loader       from "../components/Loader";
import Multiselect  from "vue-multiselect";
import Messages     from "../components/call/messages/Messages"


export default {
  name      : "call_user_filter",
  components: {
    Loader,
    Multiselect,
    JwPagination,
    Messages
  },

  props: ["id"],

  data() {
    return {
      called          : undefined,
      searchResults   : undefined,
      searchQuery     : undefined,
      error           : undefined,
      refreshing      : false,
      filterCategories: [],
      filtersQuerys   : {},
      textTranslator  : undefined,
      pageOfUsers     : [],
      translationsVue,
      subsidized,
      maxUser
    };
  },

  async created() {
    await this.getCalled();
    await this.getFilters();
  },

  computed: {
    ...get("callUserFilterModule", ["isLoading"]),

    showCalled() {
      return (!this.isLoading() && this.called) || this.refreshing;
    },
  },

  methods: {
    async getCalled(refreshing = false) {
      if (refreshing) this.refreshing = refreshing;
      this.called = await this.$store.dispatch(
          "callUserFilterModule/fetchCalled",
          this.id
      );
      this.pageOfUsers = this.called;
    },

    async getFilters() {
      const bd_data = await this.$store.dispatch(
          "callUserFilterModule/fetchFilters",
          this.id
      );
      this.filterCategories = bd_data.filters;
      this.textTranslator = bd_data.translator;

      this.clearFilters();
    },

    async search() {
      if (
          this.searchQuery ||
          !this.areFiltersEmpty()
      ) {
        this.clearResults();
        this.searchResults = await this.$store.dispatch(
            "callUserFilterModule/fetchSearch",
            {
              announcement: this.id,
              searchQuery : this.searchQuery,
              filters     : this.filtersQuerys,
            }
        );
      }
    },

    areFiltersEmpty() {
      let categories = 0;

      this.filterCategories.forEach((element) => {
        if (undefined !== this.filtersQuerys[element.name] && this.filtersQuerys[element.name] != 0) {
          categories++;
        }
      });

      return categories == 0;
    },

    async callEverybody() {
      if (
          this.searchQuery ||
          !this.areFiltersEmpty()
      ) {
        if (subsidized) {   // subsidized is a variable that is defined in the view
          if (this.searchResults < maxUser) {
            let refresh = this.$store.dispatch(
                "callUserFilterModule/fetchCallEverybody",
                {
                  announcement: this.id,
                  searchQuery : this.searchQuery,
                  filters     : this.filtersQuerys,
                }
            );
          } else {
            this.$toast.open({
              message : this.translationsVue.announcements_configureFields_info_max_users + '' + maxUser,
              type    : "warning",
              duration: 5000,
              position: "top-right",
            })
          }
        } else {
          let refresh = this.$store.dispatch(
              "callUserFilterModule/fetchCallEverybody",
              {
                announcement: this.id,
                searchQuery : this.searchQuery,
                filters     : this.filtersQuerys,
              }
          );

          if (refresh) {
            this.getCalled(true);
            this.clearResults();

          }
        }
      }
    },

    async call(user) {
      try {
        if (subsidized) {
          if (this.called.length < maxUser) {
            await this.$store.dispatch("callUserFilterModule/fetchCall", {
              announcement: this.id,
              user        : user,
            })
          } else {
            this.$toast.open({
              message : this.translationsVue.announcements_configureFields_info_max_users + '' + maxUser,
              type    : "warning",
              duration: 5000,
              position: "top-right",
            })
          }
        } else {
          let result = await this.$store.dispatch("callUserFilterModule/fetchCall", {
            announcement: this.id,
            user        : user,
          });
        }

        await this.getCalled(true);
      } catch (error) {
        if (error.response.status === 409) {
          this.showError(error.response.data.message);
        }
      }
    },

    async unCall(user) {
      try {
        let result = await this.$store.dispatch("callUserFilterModule/fetchUnCall", {
          announcement: this.id,
          user        : user,
        });

        await this.getCalled(true);
      } catch (error) {
        if (error.response.status === 409) {
          this.showError(error.response.data.message);
        }
      }
    },

    async notify(user) {
      try {
        let result = await this.$store.dispatch("callUserFilterModule/fetchNotify", {
          announcement: this.id,
          user        : user,
        });

        await this.getCalled(true);
      } catch (error) {
        if (error.response.status === 409) {
          this.showError(error.response.data.message);
        }
      }
    },

    showError(message) {
      this.error = message;
      setTimeout(() => (this.error = undefined), 3000);
    },

    clearResults() {
      this.searchResults = undefined;
    },

    clearFilters() {
      //Limpiar input buscador
      this.searchQuery = undefined;

      //Limpiar filtros seleccionados
      let filters_querys = {};
      this.filterCategories.forEach(element => {
        filters_querys[element.name] = undefined;
      });
      this.filtersQuerys = filters_querys;
    },

    customLabel({code, name}) {
      return `${code} – ${name}`;
    },

    onUserChangePage(pageOfUsers) {
      this.pageOfUsers = pageOfUsers;
    },
  },
};
</script>
<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>
<style scoped lang="scss">
@import "../assets/config/_transitions.scss";

#results {
  max-height: 200px;
  overflow: auto;
}

.accordion-body {
  background: #fff !important;
}
</style>
