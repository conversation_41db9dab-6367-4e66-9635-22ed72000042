<template>
  <div class="Fillgaps">
    <div class="col align-self-end text-right mb-2">
      <button
        type="button"
        class="btn btn-primary"
        data-bs-toggle="modal"
        :data-bs-target="`#modal-chapter-${typeChapter}`"
      >
        {{ translationsVue.add }}
      </button>
    </div>

    <!-- Modal -->
    <div
      class="modal fade"
      :id="`modal-chapter-${typeChapter}`"
      data-bs-backdrop="static"
      data-bs-keyboard="false"
      tabindex="-1"
      aria-labelledby="staticBackdropLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="staticBackdropLabel">
              {{ translationsVue.fillgaps_configureFields_title }}
            </h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
              :id="`close-modal-chapter-${typeChapter}`"
            ></button>
          </div>
          <div class="modal-body">
            <NewFillGap :routeChapter="routeChapter" />
          </div>
        </div>
      </div>
    </div>

    <div>
      <table class="table datagrid" v-if="fillgapsChapter">
        <thead>
          <tr>
            <th scope="col">
              {{ translationsVue.help_text_content_configureFields_text }}
            </th>
            <th scope="col">
              {{ translationsVue.question_configureFields_answers }}
            </th>
            <th scope="col">{{ translationsVue.user_configureFields_time }}</th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="fillGap in fillgapsChapter" :key="fillGap.id">
            <td>{{ fillGap?.extra?.textPlain | strippedContent }}</td>
            <td>{{ fillGap.answers.length }}</td>
            <td>{{ fillGap.time }}</td>
            <td class="text-right">
              <button
                type="button"
                class="btn-sm btn btn-danger btn-sm"
                data-bs-toggle="modal"
                :data-bs-target="`#deleteModal${fillGap.id}`"
              >
                <i class="fas fa-trash-alt"></i>
              </button>
              <button
                type="button"
                class="btn btn-primary btn-sm"
                data-bs-toggle="modal"
                :data-bs-target="`#modal-edit-question${fillGap.id}`"
              >
                <i class="fas fa-edit"></i>
              </button>
            </td>

            <!-- Modal -->
            <div
              class="modal fade"
              :id="`modal-edit-question${fillGap.id}`"
              data-bs-backdrop="static"
              data-bs-keyboard="false"
              tabindex="-1"
              aria-labelledby="staticBackdropLabel"
              aria-hidden="true"
            >
              <div class="modal-dialog modal-dialog-centered modal-xl">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="staticBackdropLabel">
                      {{ translationsVue.fillgaps_configureFields_title }}
                    </h5>
                    <button
                      type="button"
                      class="btn-close"
                      data-bs-dismiss="modal"
                      aria-label="Close"
                    ></button>
                  </div>
                  <div class="modal-body">
                    <NewFillGap
                      :routeChapter="routeChapter"
                      :fill-gap-data="fillGap"
                    />
                  </div>
                </div>
              </div>
            </div>

            <BaseModalDelete
              :identifier="`deleteModal${fillGap.id}`"
              :title="translationsVue.quiz_configureFields_question_delete"
              @delete-element="deleteFillgap(fillGap.id)"
            />
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
import { get } from "vuex-pathify";
import Loader from "../components/Loader";
import NewFillGap from "../components/fillgaps/NewFillGap";
import EditFillGap from "../components/fillgaps/EditFillGap";

export default {
  components: {
    Loader,
    NewFillGap,
    EditFillGap,
  },

  props: {
    blocks: {
      type: Array,
      default: () => [],
    },
    chapterId: {
      type: Number,
      default: 0,
    },
  },

  data() {
    return {
      called: true,
      time: 0,
      typeChapter,
      translationsVue,
    };
  },

  computed: {
    ...get("callModule", ["isLoading"]),

    ...get("fillgapsModule", ["getFillgapsChapter", "getRouteChapter"]),

    showCalled() {
      return !this.isLoading() && this.called;
    },

    fillgapsChapter() {
      const fillgaps = this.getFillgapsChapter();
      return fillgaps?.map((fillgap) => {
        return {
          id: fillgap.id,
          text: fillgap.text,
          extra: JSON.parse(fillgap.extra),
          answers: JSON.parse(fillgap.answers),
          time: this.getTime(fillgap.time),
          label: fillgap.label,
        };
      });
    },

    routeChapter() {
      return this.getRouteChapter();
    },
  },

  filters: {
    strippedContent: function (string) {
      let substrText = "";
      substrText = string.replace(/<\/?[^>]+>/gi, " ").slice(0, 100) + " ...";
      return substrText;
    },
  },

  async created() {
    await this.$store.dispatch(
      "fillgapsModule/getFillgapsChapter",
      this.chapterId
    );
  },

  methods: {
    getTime(time) {
      const hours = Math.floor(time / 3600);
      const minutes = Math.floor((time - hours * 3600) / 60);
      const seconds = Math.floor(time - hours * 3600 - minutes * 60);

      const minutesFormat = minutes < 10 ? "0" + minutes : minutes;
      const secondsFormat = seconds < 10 ? "0" + seconds : seconds;
      const hoursFormat = hours < 10 ? "0" + hours : hours;

      return hoursFormat + ":" + minutesFormat + ":" + secondsFormat;
    },

    async deleteFillgap(id) {
      const data = {
        id: id,
      };

      await this.$store.dispatch("fillgapsModule/deleteFillGap", data);
      await this.fetchFillGaps();
    },
  },

  async fetchFillGaps() {
    await this.$store.dispatch(
      "fillgapsModule/getFillgapsChapter",
      this.chapterId
    );
  },
};
</script>

 <style scoped lang="scss"> 
.Fillgaps {
  background: #fff;
  padding: 1rem;
}
.labelMensaje {
  width: 60%;
  padding: 0.5rem;
  align-content: center;
  color: red;
}
.labelWord {
  width: 35%;
  margin-right: 0.5rem;
}

.question {
  display: flex;
  flex-direction: column;
  margin-bottom: 0.5rem;
  padding: 0.5rem;
}
.select-block {
  padding: 0.5rem;
}
.campoInput {
  margin: 0.5rem;
  border: 1px solid #5ae8e8;
  border-radius: 25px;
  padding: 0.5rem;
}
.cursor {
  cursor: pointer;
}
</style>
