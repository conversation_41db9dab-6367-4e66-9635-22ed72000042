<template>
  <div v-if="noActiveGraphs" class="d-flex justify-content-center align-items-center flex-column-reverse no-graphs-message">
    <img src="/img/empty.svg" alt="empty" class="w-50"/>
    <strong class="mt-3 text-muted text-center fs-4">{{ $t("ADMIN.STATS.BLANK") }}</strong>
  </div>
  <div v-else>
    <accordion v-if="statsFormationSettings.length"
      v-model="visiblePanels[0]"
      title="ADMIN.STATS.TRAINING"
      icon="fa fa-graduation-cap"
    >
      <div class="r1fr1fr smAuto">
        <div class="r1fr1fr rauto" >
        <card v-if="statsFormationSettings.includes('peopleWithCourses')"
              :is-loading="!chartData.peopleWithCourses.isLoaded"
              :options="chartData.peopleWithCourses.options.totalInCourse"></card>
          <card v-if="statsFormationSettings.includes('peopleWithCourses')"
              :is-loading="!chartData.peopleWithCourses.isLoaded"
              :options="chartData.peopleWithCourses.options.totalOneCourse"></card>
          <card  v-if="statsFormationSettings.includes('formationHours')" 
              :is-loading="!chartData.formationHours.isLoaded"
              :options="chartData.formationHours.options.hours"></card>
          <card   v-if="statsFormationSettings.includes('formationHours')"
              :is-loading="!chartData.formationHours.isLoaded"
              :options="chartData.formationHours.options.average"></card>
          <card  v-if="statsFormationSettings.includes('requiredCourses')"
              :is-loading="!chartData.requiredCourses.isLoaded"
              :options="chartData.requiredCourses.options"></card>
          <card v-if="statsFormationSettings.includes('openedCourses')"
              :is-loading="!chartData.openedCourses.isLoaded"
              :options="chartData.openedCourses.options"></card>
        </div>
        <div class="rauto">
          <div class="r3auto mdAuto">
            <small-card v-if="statsFormationSettings.includes('courseStartedAndFinished')"
                :is-loading="!chartData.courseStartedAndFinished.isLoaded"
                :value="chartData.courseStartedAndFinished.options.started"
                title="STATISTICS.COURSE.START"
                color="#F7BE73"
                icon="fa fa-book"
            ></small-card>
            <small-card v-if="statsFormationSettings.includes('courseStartedAndFinished')"
                :is-loading="!chartData.courseStartedAndFinished.isLoaded"
                :value="chartData.courseStartedAndFinished.options.inProcess"
                title="STATISTICS.COURSE.PROCESS"
                color="#68bbb0"
                icon="fas fa-chalkboard-teacher"
            ></small-card>
            <small-card v-if="statsFormationSettings.includes('courseStartedAndFinished')"
                :is-loading="!chartData.courseStartedAndFinished.isLoaded"
                :value="chartData.courseStartedAndFinished.options.finished"
                title="STATISTICS.COURSE.ENDED"
                icon="fa fa-graduation-cap"
            ></small-card>
          </div>
          <div class="rauto">
            <chart v-if="statsFormationSettings.includes('educativeStatus')"
                :is-loading="!chartData.educativeStatus.isLoaded"
                :options="chartData.educativeStatus.options"
                title="STATISTICS.EDUCATIVE.STATUS"
                icon="fa fa-trophy"
            ></chart>
          </div>
        </div>
      </div>
      <div class="r3auto lgAuto">
        <chart  v-if="statsFormationSettings.includes('peoplePerformance')"
            :is-loading="!chartData.peoplePerformance.isLoaded"
            :options="chartData.peoplePerformance.options"
            title="STATISTICS.PEOPLE.PERFORMANCE"
            icon="fa fa-users"
        ></chart>
        <stars-graphics v-if="statsFormationSettings.includes('coursesByStars')"
            :is-loading="!chartData.coursesByStars.isLoaded"
            title="STATISTICS.COURSES.BY_STARS"
            icon="fa fa-users"
            :value="chartData.coursesByStars.options"
        ></stars-graphics>
        <chart v-if="statsFormationSettings.includes('structureAndHotel')"
            :is-loading="!chartData.structureAndHotel.isLoaded"
            :options="chartData.structureAndHotel.options"
            title="STATISTICS.STRUCTURE_HOTEL"
            icon="fa fa-users"
        ></chart>
      </div>
      <div class="auto">
        <chart v-if="statsFormationSettings.includes('schoolFinishedAndProgress')"
            :is-loading="!chartData.schoolFinishedAndProgress.isLoaded"
            :options="chartData.schoolFinishedAndProgress.options"
            title="STATISTICS.SCHOOL.FINISHED_PROGRESS"
        ></chart>
      </div>
      <div class="auto">
        <chart v-if="statsFormationSettings.includes('coursesByDepartment')"
            :is-loading="!chartData.coursesByDepartment.isLoaded"
            :options="chartData.coursesByDepartment.options"
            title="STATISTICS.COURSES.BY_DEPARTMENT"
            icon="fa fa-book"
        ></chart>
      </div>
      <div class="auto">
        <chart v-if="statsFormationSettings.includes('coursesBySchool')"
            :is-loading="!chartData.coursesBySchool.isLoaded"
            :options="chartData.coursesBySchool.options"
            title="STATISTICS.COURSES.BY_SCHOOL"
            icon="fa fa-book"
        ></chart>
      </div>
      <div class="r3auto mdAuto" v-if="statsFormationSettings.includes('gamifiedPills')">
        <card 
            :is-loading="!chartData.gamifiedPills.isLoaded"
            :options="chartData.gamifiedPills.options.general"></card>
        <card
            :is-loading="!chartData.gamifiedPills.isLoaded"
            :options="chartData.gamifiedPills.options.failures"></card>
        <card
            :is-loading="!chartData.gamifiedPills.isLoaded"
            :options="chartData.gamifiedPills.options.successes"></card>
      </div>
      <div class="r1fr1fr mdAuto" v-if="statsFormationSettings.includes('gamifiedTest')">
        <chart
            :is-loading="!chartData.gamifiedTest.isLoaded"
            :options="chartData.gamifiedTest.options.chart1"
            class="hidePoints"
            title="STATISTICS.GAMIFIED.TEST"
            icon="fa fa-gamepad"
        ></chart>
        <chart
            :is-loading="!chartData.gamifiedTest.isLoaded"
            :options="chartData.gamifiedTest.options.chart2"
            class="alignLeft"
            title="STATISTICS.GAMIFIED.OPTIONS"
            icon="fa fa-gamepad"
        ></chart>
      </div>
      <div class="auto"  v-if="statsFormationSettings.includes('coursesByStars')" >
        <rating-list v-if=false
            :is-loading="!chartData.general.isLoaded"
            :elementList="chartData.general.options.courses"
            tag="courses"
            title="STATISTICS.COURSES.RATING"
            icon="fa fa-book"
        ></rating-list>        
      </div>
      <div class="auto"  v-if="statsFormationSettings.includes('usersMoreActivesByCourses')">
        <rating-list 
            :is-loading="!chartData.general.isLoaded"
            :elementList="chartData.general.options.usersMoreActivesByCourses"
            tag="usersMoreActivesByCourses"
            title="STATISTICS.COURSE.USER_MORE_ACTIVES"
            list-icon=""
            icon="fa fa-book"
        ></rating-list>
      </div>
    </accordion>
    <accordion v-if="statsEvolutionSettings.length" v-model="visiblePanels[1]" title="ADMIN.STATS.EVOLUTIONARY" icon="fa fa-chart-line">
      <chart v-if="statsEvolutionSettings.includes('userNewInPlatformThanFinishedOneCourse')"
          :is-loading="!chartData.userNewInPlatformThanFinishedOneCourse.isLoaded"
          :options="chartData.userNewInPlatformThanFinishedOneCourse.options"
          title="STATISTICS.COURSE.USER_FINISHED_ONE"
      ></chart>

      <chart v-if="statsEvolutionSettings.includes('trainedPerson')"
          :is-loading="!chartData.trainedPerson.isLoaded"
          :options="chartData.trainedPerson.options"
          title="STATISTICS.USERS.TRAINED_PERSON"
      ></chart>

      <chart v-if="statsEvolutionSettings.includes('startedCourses')"
          :is-loading="!chartData.startedCourses.isLoaded"
          :options="chartData.startedCourses.options"
          title="STATISTICS.COURSE.START"
      ></chart>

      <chart v-if="statsEvolutionSettings.includes('proccessCourses')"
          :is-loading="!chartData.proccessCourses.isLoaded"
          :options="chartData.proccessCourses.options"
          title="STATISTICS.COURSE.PROCESS"
      ></chart>

      <chart v-if="statsEvolutionSettings.includes('finishedCourses')"
          :is-loading="!chartData.finishedCourses.isLoaded"
          :options="chartData.finishedCourses.options"
          title="STATISTICS.COURSE.ENDED"
      ></chart>

      <chart v-if="statsEvolutionSettings.includes('segmentedHours')"
          :is-loading="!chartData.segmentedHours.isLoaded"
          :options="chartData.segmentedHours.options" title="HOURS"></chart>
    </accordion>
    <accordion v-if="statsDemographySettings.length" v-model="visiblePanels[2]" title="ADMIN.STATS.DEMOGRAPHICS">
      <div class="r2fr1fr mdAuto">
        <chart v-if="statsDemographySettings.includes('usersBySexAndAge')"
            :is-loading="!chartData.usersBySexAndAge.isLoaded"
            :options="chartData.usersBySexAndAge.options"
            title="STATISTICS.USERS.SEX_AGE"
        ></chart>
        <chart v-if="statsDemographySettings.includes('ageDistribution')"
            :is-loading="!chartData.ageDistribution.isLoaded"
            :options="chartData.ageDistribution.options"
            title="STATISTICS.USERS.AGE_DISTRIBUTION"
        ></chart>
      </div>
      <div class="r1fr2fr mdAuto">
        <chart v-if="statsDemographySettings.includes('deviceDistribution')"
            :is-loading="!chartData.deviceDistribution.isLoaded"
            :options="chartData.deviceDistribution.options"
            title="STATISTICS.USERS.DEVICE_DISTRIBUTION"
            icon="fa fa-mobile"
        ></chart>
        <chart v-if="statsDemographySettings.includes('usersByCountries')"
            :is-loading="!chartData.usersByCountries.isLoaded"
            :options="chartData.usersByCountries.options"
            title="STATISTICS.USERS.COUNTRY_DISTRIBUTION"
            icon="fa fa-flag"
        ></chart>
      </div>
    </accordion>
    <accordion v-if="statsActivitySettings.length" v-model="visiblePanels[3]" title="ADMIN.STATS.ACTIVITY" icon="fa fa-bolt">
      <div class="r1fr1frauto lgAuto">
      <chart v-if="statsActivitySettings.includes('activityInfo')"
            :is-loading="!chartData.activityInfo.isLoaded"
            :options="chartData.activityInfo.options.activeUsers"
            title="STATISTICS.USERS.PORTAL_ACTIVITY"
        ></chart>
        <simple-card v-if="statsActivitySettings.includes('activityInfo')"
            :is-loading="!chartData.activityInfo.isLoaded"
            :options="chartData.activityInfo.options.totalAcc"
        ></simple-card>
        <div class="r1fr1fr lgAuto">
          <card v-if="statsActivitySettings.includes('activityInfo')"
              :is-loading="!chartData.activityInfo.isLoaded"
              :options="chartData.activityInfo.options.loggedOnceUsers"
          ></card>
          <card v-if="statsActivitySettings.includes('activityInfo')"
              :is-loading="!chartData.activityInfo.isLoaded"
              :options="chartData.activityInfo.options.loggedLastMonth"
          ></card>
          <card v-if="statsActivitySettings.includes('activityInfo')"
              :is-loading="!chartData.activityInfo.isLoaded"
              :options="chartData.activityInfo.options.inactiveUsers"></card>
          <card v-if="statsActivitySettings.includes('activityInfo')"
              :is-loading="!chartData.activityInfo.isLoaded"
              :options="chartData.activityInfo.options.neverAccess"></card>
        </div>
      </div>
      <div class="rauto" v-if="statsActivitySettings.includes('accessDays')">
        <master-detail-chart
            :is-loading="!chartData.accessDays.isLoaded"
            :options="chartData.accessDays.options"
            title="ACCESS_DAYS"
            icon="fa fa-calendar-o"
        ></master-detail-chart>
      </div>
      <div class="rauto" v-if="statsActivitySettings.includes('platformAccessByHours')">
        <chart
            :is-loading="!chartData.platformAccessByHours.isLoaded"
            :options="chartData.platformAccessByHours.options"
            title="STATISTICS.PLATAFORM_ACCESS.BY_HOURS"
            icon="fa fa-clock-o"
        ></chart>
      </div>
      <div class="rauto " v-if="statsActivitySettings.includes('courseStartTime')">
        <chart
            :is-loading="!chartData.courseStartTime.isLoaded"
            :options="chartData.courseStartTime.options"
            title="STATISTICS.COURSES.START_TIME"
            icon="fa fa-clock-o"
        ></chart>
      </div>
      <div class="rauto" v-if="statsActivitySettings.includes('courseEndTime')" >
        <chart
            :is-loading="!chartData.courseEndTime.isLoaded"
            :options="chartData.courseEndTime.options"
            title="STATISTICS.COURSES.END_TIME"
            icon="fa fa-clock-o"
        ></chart>
      </div>
      <div class="rauto" v-if="statsActivitySettings.includes('coursesStartedVsFinished')" >
        <chart
            :is-loading="!chartData.coursesStartedVsFinished.isLoaded"
            :options="chartData.coursesStartedVsFinished.options"
            :allow-sort="true"
            @show-modal="
              showModalOptions(
                'coursesStartedVsFinished',
                'Cursos empezados VS Cursos finalizados'
              )
            "
            title="STATISTICS.COURSES.START_END"
            icon="fa fa-book"
        ></chart>
      </div>
      <div class="auto" v-if="statsActivitySettings.includes('usersMoreActivesByActivity')" >
        <rating-list
            :is-loading="!chartData.usersMoreActivesByActivity.isLoaded"
            :elementList="chartData.usersMoreActivesByActivity.options"
            tag="usersMoreActivesByActivity"
            title="STATISTICS.USERS.TIME_USE"
            list-icon=""
            icon="fa fa-users"
        ></rating-list>
      </div>
    </accordion>
    <accordion  v-if="statsItinerarySettings.length" v-model="visiblePanels[4]" title="ITINERARY.LABEL.PLURAL" icon="fa fa-book">
      <div class="auto">
        <chart v-if="statsItinerarySettings.includes('itinerariesStartedAndFinished')"
            :is-loading="!chartData.itinerariesStartedAndFinished.isLoaded"
            :options="chartData.itinerariesStartedAndFinished.options"
            title="STATISTICS.ITINERARIES.START_END"
            :allow-sort="true"
            @show-modal="showModalOptions('itinerariesStartedAndFinished', 'Itinerarios empezados y completados')"
            icon="fa fa-book"
        ></chart>
      </div>
      <!--          <div class="rauto">-->
      <!--            <chart :options="chartData.itinerariesCompletedByCountries.options" title="Itinerarios completados por países"-->
      <!--                   icon="fa fa-flag"></chart>-->
      <!--          </div>-->
    </accordion>
  </div>
</template>

<script>
import Chart from "./Chart";
import MasterDetailChart from "./MasterDetailChart";
import Card from "./Card";
import SimpleCard from "./SimpleCard";
import SmallCard from "./SmallCard";
import StarsGraphics from "./StarsGraphics";
import RatingList from "./RatingList";
import Accordion from "./Accordion";

export default {
  name: "ContentTemplate",
  computed: {
    noActiveGraphs() {
      return (
        this.statsFormationSettings.length === 0 &&
        this.statsEvolutionSettings.length === 0 &&
        this.statsDemographySettings.length === 0 &&
        this.statsActivitySettings.length === 0 &&
        this.statsItinerarySettings.length === 0
      );
    },
  },
  components: {
    Chart,
    MasterDetailChart,
    Card,
    SimpleCard,
    SmallCard,
    StarsGraphics,
    RatingList,
    Accordion,
  },
  props: {
    visiblePanels: {
      type: Array,
      default: () => [true, true, true, true, true],
    },
    chartData: {
      type: Object,
      default: () => ({}),
    },
    statsFormationSettings: {
      type: Array,
      default: () => [],
    },
    statsEvolutionSettings: {
      type: Array,
      default: () => [],
    },
    statsDemographySettings: {
      type: Array,
      default: () => [],
    },
    statsActivitySettings: {
      type: Array,
      default: () => [],
    },
    statsItinerarySettings: {
      type: Array,
      default: () => [],
    },
    data() {
      return {
        courseStart: '{{ $t("COURSE.STATISTICS.START") }}'
      };
    },
  },
  methods: {
    showModalOptions(key, title) {
      this.$emit("show-modal", [key, title]);
    },
  },
};
</script>

 <style scoped lang="scss"> 
</style>
