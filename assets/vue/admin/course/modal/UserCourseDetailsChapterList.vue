<template>
  <div class="UserCourseDetailsChapterList">
    <div class="d-flex align-items-center justify-content-between px-3 pb-3 mb-3" v-if="itinerary || (allowExport && userData?.progress > 0)">
      <button class="btn btn-sm btn-primary mr-3" v-if="itinerary" @click="goBack" id="backButton">
        <i class="fa fa-caret-left"></i> {{ $t('SUBSCRIPTION.GO_BACK') }}
      </button>
      <button class="btn btn-sm btn-primary ml-auto mr-0 my-1" @click="$emit('download')" v-if="allowExport && userData?.progress > 0">
        <i class="fa mr-1" :class="loadingData ? 'fa-spinner' : 'fa-file-alt'"></i>
        {{ $t('ANNOUNCEMENT.MODALS.REPORT_XML') }}
      </button>
    </div>
    <div class="d-flex w-100 align-items-center justify-content-between px-3 pb-3">
      <div class="d-flex flex-column flex-wrap">
        <p class="mt-2 mb-0">
          <b>{{ $t('PROGRESS') }}:</b>
          <b class="text-primary">{{ userData.progress || 0 }}%</b>
        </p>
        <p class="mt-1 mb-0">
          <b>{{ $t('ANNOUNCEMENT.MODALS.PROGRESS_TASK4') }}:</b>
          <b class="text-primary">{{ userData.chapterStarted ? userData.chapterStarted : (userData.chaptersFinished ? userData.chaptersFinished : 0)  }}</b><b>/{{ userData.totalChapters || 0 }}</b>
        </p>
      </div>
    </div>
    <div class="pr-3 h-100 scrollable">
      <div class="table-responsive w-100">
        <table class="table table-condensed">
          <thead class="sticky-top bg-white">
          <tr>
            <th>{{ $t('CHAPTERS.LABEL.PLURAL') }}</th>
            <th>{{ $t('TYPE') }}</th>
            <th>{{ $t('ANNOUNCEMENT.MODALS.START_END') }}</th>
            <th>{{ $t('STATUS') }}</th>
            <th>{{ $t('TOTAL_TIME') }}</th>
            <th class="text-center">{{ $t('RESULTS') }}</th>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(chapter, index) in userData.chapterDetails" :key="'chapter_' + index">
            <td>
              <div class="d-flex gap-2">
                <img :src="chapter.image" :alt="chapter.name">
                {{ chapter.name }}
              </div>
            </td>
            <td class="text-nowrap">
              <img :src="getIconDir(chapter.icon)" alt="Chapter Type">
              <span class="text-capitalize">{{ chapter.type }}</span>
            </td>
            <td class="subtitle">
              <p class="my-0 text-nowrap">{{ chapter.startDateTimeArray.join(' ') }}</p>
              <p class="my-0 text-nowrap">{{ chapter.endDateTimeArray.join(' ') }}</p>
            </td>
            <td>
              <span :class="chapter.statusData.className">{{ $t(chapter.statusData.textKey) }}</span>
            </td>
            <td class="text-nowrap">{{ chapter.time }}</td>
            <td class="text-center">
              <b v-if="chapter.attempts.length" class="text-primary cursor-pointer" @click="openChapter(index)">{{ $t('VIEW') }}</b>
              <b v-else>-</b>
            </td>
          </tr>
          <tr v-if="!userData.chapterDetails.length">
            <td class="text-center" colspan="10">{{ $t('EMPTY.TITLE') }}</td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
    <p class="mt-2 mb-0 text-right pr-3">
      {{$t('TOTAL_TIME')}}: <b>{{ totalTime }}</b>
    </p>
  </div>
</template>

<script>
import BaseModal from '../../../base/BaseModal.vue'
import UserStatsModel, { secondsToTime } from '../models/UserStatsModel'

export default {
  name: "UserCourseDetailsChapterList",
  components: { BaseModal },
  props: {
    userData: { type: UserStatsModel, default: () => (new UserStatsModel({})) },
    loadingData: { type: Boolean, default: false },
    itinerary: { type: Boolean, default: false },
    allowExport: { type: Boolean, default: true }
  },
  computed: {
    assetsHome() { return assetsDir || ''},
    totalTime() {
      return secondsToTime(this.userData.chapterDetails.reduce((acc, current) => acc + current.timeDiff, 0))
    }
  },
  methods: {
    openChapter(index) {
      this.$emit('select', this.userData.chapterDetails[index])
    },
    getIconDir(icon = '') { return `${ this.assetsHome }${icon}` },
    goBack() {
      const button = document.getElementById('backButton');
      button.setAttribute('data-bs-toggle', 'modal');
      button.setAttribute('data-bs-target', `#staticBackdrop${this.userData.id}`);
      button.click();
    },
  },
}
</script>

<style lang="scss">
.UserCourseDetailsChapterList {
  display: grid;
  // grid-template-rows: minmax(75px, 100px) auto 2rem;
  img {
      height: 2rem; width: 2rem; object-fit: cover; object-position: center;
  }
}
</style>