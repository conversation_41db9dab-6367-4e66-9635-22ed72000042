import axios from 'axios';
import { make } from 'vuex-pathify';

const getDefaultState = () => ({
    called: undefined,
    notified: undefined,
    results: undefined,
    filters: undefined,
    loading: false, // todo add funcionality
    searching: false, // todo add funcionality
    calling: false, // todo add funcionality
    notifying: false,
    taskByUser: undefined,
    taskAnnouncement: undefined,
    messages: undefined   

});

const state = () => getDefaultState();

export const getters = {
    getCalled: (state) => () => state.called,
    isLoading: (state) => () => state.loading,
    isSearching: (state) => () => state.searching,
    isCalling: (state) => () => state.calling,
    getTaskByUser: (state) => () => state.taskByUser,
    getTaskAnnouncement: (state) => () => state.taskAnnouncement,
    getMessages: (state) => () => state.messages
};

export const mutations = {
    ...make.mutations(state),
};

export const actions = {
    async fetchFilters({ commit }, announcement) {
        const url = `/admin/announcements/${announcement}/filters`;
        let filters;

        try {
            commit('SET_SEARCHING', true);
            const {data} = await axios.get(url);
            filters = data.data.filters;
            commit('SET_FILTERS', filters);
        } finally {
            commit('SET_SEARCHING', false);
        }

        return filters;
    },


    async fetchCalled({ commit }, announcement) {
        const url = `/admin/announcements/${announcement}/called`;
        let called;

        try {
            commit('SET_LOADING', true);
            const {data} = await axios.get(url);
            called = data.data.called;
            commit('SET_CALLED', called);
        } finally {
            commit('SET_LOADING', false);
        }

        return called;
    },

    async fetchSearch({ commit }, search) {
        const url = `/admin/announcements/${search.announcement}/search`;
        let results;

        try {
            commit('SET_SEARCHING', true);
            const {data} = await axios.post(
                url,
                {
                    q: search.searchQuery,
                    category: search.categoryQuery,
                    departament: search.departamentQuery,
                    center: search.centerQuery,
                    country: search.countryQuery,
                    division: search.divisionQuery,
                }
            );
            results = data.data.results;
            commit('SET_RESULTS', results);
        } finally {
            commit('SET_SEARCHING', false);
        }

        return results;
    },

    async fetchCallEverybody({ commit }, search) {
        const url = `/admin/announcements/${search.announcement}/call`;

        try {
            commit('SET_SEARCHING', true);
            const {data} = await axios.post(
                url,
                {
                    q: search.searchQuery,
                    category: search.categoryQuery,
                    departament: search.departamentQuery,
                    center: search.centerQuery,
                    country: search.countryQuery,
                    division: search.divisionQuery,
                }
            );
        } finally {
            commit('SET_SEARCHING', false);
        }

        return data.error;
    },

    async fetchCall({ commit }, call) {
        const url = `/admin/announcements/${call.announcement}/call/${call.user}`;
        let result;

        try {
            commit('SET_CALLING', true);
            const {data} = await axios.get(url);
            result = data;
            commit('SET_CALLED', result);
        } finally {
            commit('SET_CALLING', false);
        }

        return result;
    },

    async fetchUnCall({ commit }, call) {
        const url = `/admin/announcements/${call.announcement}/uncall/${call.user}`;
        let result;

        try {
            commit('SET_CALLING', true);
            const {data} = await axios.get(url);
            result = data;
            commit('SET_CALLED', result);
        } finally {
            commit('SET_CALLING', false);
        }

        return result;
    },

    async fetchNotify({ commit }, call) {
        const url = `/admin/announcements/${call.announcement}/notify/${call.user}`;
        let result;

        try {
            commit('SET_NOTIFYING', true);
            const {data} = await axios.get(url);
            result = data;
            commit('SET_NOTIFIED', result);
        } finally {
            commit('SET_NOTIFYING', false);
        }

        return result;
    },


  async fetchTaskByUser({ commit }, requestData) {
    try {
      commit("SET_LOADING", true);

      const { data } = await axios.get(`/admin/task-by-user/${requestData.idUser}/announcement/${requestData.announcement}`);
   
      commit("SET_TASK_BY_USER", data?.data);
      //window.Vue.$toast.success(data?.message);
    } catch (e) {
      console.log(e);
      //window.Vue.$toast.success(data?.message);
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async fetchMessagesByUser({ commit }, requestData) {
    try {
      commit("SET_LOADING", true);

      const { data } = await axios.get(`/admin/messages-student-tutor/user/${requestData.idUser}/announcement/${requestData.announcement}`);

      commit("SET_MESSAGES", data?.data);

    } catch (e) {
      console.log(e);
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async sendMessageToStudent({ commit }, requestData) {
    try {
      commit("SET_LOADING", true);

      const { data } = await axios.post("/messages/sent/tutor", requestData);

      commit("SET_MESSAGES", data.data);
     // window.Vue.$toast.success(data?.message);
    } catch (e) {
     // window.Vue.$toast.success(data?.message);
    } finally {
      commit("SET_LOADING", false);
    }
  },
};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions,
};
