import axios from 'axios';
import {make} from 'vuex-pathify';

const getDefaultState = () => ({});

const state = () => getDefaultState();

export const getters = {};

export const mutations = {
    ...make.mutations(state),
};

export const actions = {
    setBlock(context, data) {
        const url = "/admin/chapter/trueorfalse/set-block"; 
        const options = {
            headers: { 'Content-Type': 'multipart/form-data' },
        };

        const formData = new FormData();
        formData.append('chapterId', data.chapterId);
        formData.append('trueorfalse_id', data.trueorfalse_id);
        formData.append('text', data.text);
        formData.append('time', data.time);
        formData.append('select', data.select);
        formData.append('trueorfalse_id', data.trueorfalse_id);
        formData.append('categorized', true);
        formData.append('route', data.route);

        const { result } = axios.post(url, formData, options);
        return result;       
    },

    editBlock(context, data) {console.log(data)
        const url = "/admin/chapter/trueorfalse/edit-line";
        const options = {
            headers: { 'Content-Type': 'multipart/form-data' },
        };

        const formData = new FormData();
        formData.append('id', data.id);
        formData.append('text', data.text);
        formData.append('time', data.time);
        formData.append('select', data.select);
        formData.append('trueorfalse_id', data.trueorfalse_id);
        formData.append('route', data.route);

        const { result } = axios.post(url, formData, options);
        
        return result; 
    },

    async reloadBlock(context, data) {console.log('data', data);
        const url = "/admin/chapter/trueorfalse/reload-block";
        return  await axios.post(url, data); 
    },

    deleteLine(context, data) {
        const url = "/admin/chapter/trueorfalse/delete-line";
        return axios.post(url, data);
    },
};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions,
};
