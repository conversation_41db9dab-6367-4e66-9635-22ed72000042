import axios from 'axios';
import { make } from 'vuex-pathify';

const getDefaultState = () => ({
    uploadPuzzle: undefined,

});

const state = () => getDefaultState();

export const getters = {
    getUpload: (state) => () => state.uploadPuzzle,
}

export const mutations = {
    ...make.mutations(state),
};


export const actions = {

    async uploadImagePuzzle(context, { file, chapter }) {
        const url = '/upload-image-puzzle';
        const options = {
            headers: { 'Content-Type': 'multipart/form-data' },
        };

        const nameFile = Date.now();

        const formData = new FormData();
        formData.append('file', file, nameFile + '.png');
        formData.append('chapter', chapter);

        const { result } = await axios.post(url, formData, options);

       
        return result;
    },

    async detailPuzzle(context, chapter) {
        const url = `/admin/detail-puzzle/${chapter}`;

        const results = await axios.get(url);

        return results;
    },

    async updatePuzzle(context, {id, chapter, file, nameImage}) {
        const url = '/admin/update-image-puzzle';
        const options = {
          headers: { 'Content-Type': 'multipart/form-data' },
        };
        
        const nameFile = Date.now();
        const formData = new FormData();
        formData.append('id', id);  
        formData.append('chapter', chapter);
        formData.append('file', file, nameFile + '.png');
        formData.append('nameImage', nameImage);

    
        const { data } = await axios.post(url, formData, options);

        return data;
      }, 

      async getImagesSlider(context, chapter) {
        const url = `/admin/images-slider/${chapter}`;

        const results = await axios.get(url);

        return results;
    },

};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions,
};
