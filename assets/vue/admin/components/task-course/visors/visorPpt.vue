<template>
  <div class="visor-ppt">
    <embed
      :src="`${routeBase}${name}`"
      width="100%"
      height="100%"
      type="application/vnd.ms-powerpoint"
    />
  </div>
</template>

<script>
export default {
  props: {
    name: {
      type: String,
      default: "",
    },
    routeBase: {
      type: String,
      required: true,
    },
  },
};
</script>

 <style scoped lang="scss"> 
.visor-ppt {
  width: 100%;
  height: 90vh;
}
</style>
