<template>
  <div class="setting">
    <label :for="setting.key"
      >{{ setting.name }}
      <br><small>[{{ setting.code }}] </small>
    </label>

    <a
      class="badge badge-primary"
      data-toggle="tooltip"
      data-placement="top"
      :title="setting.description ? setting.description : setting.code"
    >
      <i class="fas fa-info"></i>
    </a>

    <div>
      <component
        :is="settingType"
        :key="setting.id"
        :setting="setting"
        class="setting"
      >
      </component>
    </div>
  </div>
</template>

<script>
import SettingSingleSelect from './SettingSingleSelect.vue';
import SettingMultiSelect from './SettingMultiSelect.vue';
import SettingMultiSelectWithNew from './SettingMultiSelectWithNew.vue';
import SettingString from './SettingString.vue';
import SettingText from "./SettingText.vue";
import SettingBool from "./SettingBool.vue";

const SETTING_TYPES = {
  text: "SettingText",
  bool: "SettingBool",
  select: "SettingSingleSelect",
  multiSelect: "SettingMultiSelect",
  multiSelectWithNew: "SettingMultiSelectWithNew",
};
export default {
  components: {
    SettingString,
    SettingText,
    SettingBool,
    SettingSingleSelect,
    SettingMultiSelect,
    SettingMultiSelectWithNew,
  },
  props: {
    setting: {
      type: Object,
      required: true,
    },
  },

  computed: {
    settingType() {
      return typeof SETTING_TYPES[this.setting.type] !== "undefined"
        ? SETTING_TYPES[this.setting.type]
        : SETTING_TYPES.text;
    },
  },
};
</script>

<style lang="scss" scoped >
a {
  cursor: pointer;
  margin-left: 10px;
  font-size: 10px;
  color: white;
  background-color: #007bff;
  border-radius: 50%;
  padding: 5px;
  text-align: center;
  width: 30px;
  height: 30px;
  line-height: 20px;
  display: inline-block;
  vertical-align: middle;
  text-decoration: none;
  transition: all 0.3s;
  &:hover {
    background-color: #0056b3;
  }
}
</style>

