<template>
  <div class="setting-bool">
    <select
        v-model="setting.value"
        class="form-select"
    >
      <option value="false">No</option>
      <option value="true">Sí</option>
    </select>

  </div>
</template>


<script>
export default {
  props: {
    setting: {
      type: Object,
      required: true,
    },
  },
  computed: {
    settingId() {
      return this.setting.key;
    },
  },
}
</script>
