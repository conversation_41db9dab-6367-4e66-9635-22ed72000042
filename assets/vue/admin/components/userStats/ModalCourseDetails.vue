<template>
  <div class="ModalCourseDetails">
    <UserCourseDetails
      identifier="courseDetails"
      :user-data="userData"
      :course-id="courseId"
    />
  </div>
</template>

<script>
import BaseModal         from "../../../base/BaseModal";
import TrainingTable     from "./TrainingTable";
import CourseDetailsInfo from "./CourseDetailsInfo";
import ExcelGenerator    from "../../stats/ExcelGenerator";
import UserStatsModel from '../../course/models/UserStatsModel'
import UserCourseDetails from '../../course/modal/UserCourseDetails.vue'

export default {
  name      : "ModalCourseDetails",
  components: { UserCourseDetails, ExcelGenerator, CourseDetailsInfo, TrainingTable, BaseModal},
  props: {
    course: {
      type   : Object,
      default: () => ({})
    },
  },
  data() {
    return {
      currentPage: 0,
      selectedItem: {},
      userData: undefined
    }
  },
  computed: {
    courseKey(){
      const dateObj = new Date(this.course?.dateStart)
      return `course${this.courseId}_${(isNaN(dateObj.getTime()) ? '-' : dateObj.getTime())}`
    },
    userId() { return userId || 0 },
    userEmail() { return userEmail || '' },
    userFullName() { return userFullName || '' },
    courseId() { return this.course.id || 0 },
    courseData() {
      return this.course?.id ? {
        id: this.courseId,
        image: this.course.thumbnail,
        name: this.course.title,
        type: this.course.informationType,
      } : {}
    }
  },
  watch: {
    courseKey() {
      course_data.id = this.courseData.id;
      course_data.name = this.courseData.name;
      course_data.image = this.courseData.image;
      course_data.type = this.courseData.type;
      this.userData = new UserStatsModel({
        id: this.userId,
        email: this.userEmail,
        firstname: this.userFullName,
        chapterStarted: this.course.chapterStarted,
        chaptersFinished: this.course.chaptersFinished,
        totalChapters: this.course.totalChapters,
        chapterDetails: this.course.chapterDetails,
        type: this.course.informationType,
        category: this.course.category,
        start: this.course.dateStart,
        end: this.course.dateEnd,
        survey: this.course.survey,
        imgCss: this.dimensionsImg(this.courseData.image)
      })
    }
  },
  methods: {
    dimensionsImg(url){
      const foto = new Image(); 
      foto.src = url; 
      foto.onload = function() {     
        if(foto.width>=foto.height) return 'detailsImgWidth';
        else return 'detailsImgHeight';
      };     
    }
  }
}
</script>

 <style scoped lang="scss"> 
.ModalCourseDetails {
  .course-item-image {
    width: 2rem;
    height: 2rem;
    object-position: center;
    object-fit: cover;
  }

  .bg-lightgray {
    background-color: var(--color-neutral-mid);
  }

  .courseDetailPanel {
    display: grid;
    grid-template-rows: auto 1fr;
  }
  .informationIcon {
    width: auto;
    height: 1.5rem;
    aspect-ratio: 1;
    margin-top: -0.25rem;
  }
  i.fa-icon {
    width: 1.2rem;
    height: 1.2rem;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
  }
  .teleformation {
    background-image: url("../../../../images/announcement/icon_teleformation.svg");
  }
  .presential {
    background-image: url("../../../../images/announcement/icon_presential.svg");
  }
  .mixed {
    background-image: url("../../../../images/announcement/icon_mixed.svg");
  }
  .virtual {
    background-image: url("../../../../images/announcement/icon_virtual.svg");
  }
}
</style>
