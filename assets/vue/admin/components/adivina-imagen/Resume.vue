<template>
  <tr>
      <td>  {{ block.title }} </td>
      <td> {{ block.image}} </td>
      <td> {{ block.words }} </td>
      <td>  {{ block.clue }} </td>
      <td>  {{ block.time }} </td>
      <td>
        <div class="cursor" @click="modifyLine"> 
          <i class="fa fa-pen"></i>
          Modificar
        </div>

        <div class="cursor" @click="deleteLine">
          <i class="fa fa-trash"></i>
          Eliminar
        </div>
      </td>
  </tr>
</template>

<script>

export default{
  props: {
    block: {
      type: Object,
      default: () => ({}),
    },
  },

  methods:{
    modifyLine(){
      const data = {
        id: this.block.id,
        image: this.block.image,
        words: this.block.words,
        clue: this.block.clue,
        time: this.block.time,
        title: this.block.title,
        pathImage: 'uploads/games/adivinaImagen/',
      }

      this.$emit('modifyLine', data);
    },

    deleteLine(){
      this.$emit('deleteLine', this.block.id);
    }
  }
}

</script>

 <style scoped lang="scss"> 
  .Resume{
    display: flex;
    justify-content: space-around;
    align-items: center;
  }

  .cursor{
    cursor: pointer;
  }
</style>
