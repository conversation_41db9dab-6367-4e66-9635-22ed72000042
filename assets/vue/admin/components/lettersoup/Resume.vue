<template>
  <tr>
    <td>{{ block.question }}</td>
    <td>{{ lengthWords(block.word) }}</td>
    <td>{{ getTime(block.time) }}</td>
    <td class="text-right">
      <button
        class="btn btn-danger btn-sm"
        @click="deleteLine"
        data-bs-toggle="modal"
        data-bs-target="#deleteModal"
      >
        <i class="fas fa-trash-alt"></i>
      </button>
      <button
        class="btn btn-primary btn-sm"
        @click="modifyLine"
        data-bs-toggle="modal"
        data-bs-target="#editQuestion"
      >
        <i class="fas fa-edit"></i>
      </button>
    </td>
  </tr>
</template>

<script>
export default {
  props: {
    block: {
      type: Object,
      default: () => ({}),
    },
  },

  methods: {
    modifyLine() {
      const data = {
        id: this.block.id,
        question: this.block.question,
        time: this.block.time,
        word: this.block.word,
      };

      this.$emit("modifyLine", data);
    },

    deleteLine() {
      this.$emit("deleteLine", this.block.id);
    },

    getTime(time) {
      const hours = Math.floor(time / 3600);
      const minutes = Math.floor((time - hours * 3600) / 60);
      const seconds = Math.floor(time - hours * 3600 - minutes * 60);

      const minutesFormat = minutes < 10 ? "0" + minutes : minutes;
      const secondsFormat = seconds < 10 ? "0" + seconds : seconds;
      const hoursFormat = hours < 10 ? "0" + hours : hours;

      return hoursFormat + ":" + minutesFormat + ":" + secondsFormat;
    },

    lengthWords(word) {
      const data = word.split(",");
      return data.length;
    },
  },
};
</script>

 <style scoped lang="scss"> 
.Resume {
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.cursor {
  cursor: pointer;
}
</style>
