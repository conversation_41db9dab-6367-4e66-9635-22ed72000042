<template>
  <div>
    <div class="CarouselImage" v-if="imagesSlider.data">
      <div
        v-for="item in imagesSlider.data"
        id="slider"
        :key="item.position"
        class="slider"
        @click="pause()"
      >
        <div>
          <img
            :src="`/assets/slider/slider1.svg`"
            class="iconSlider"
            @click="lastImage()"
          />
        </div>

        <img
          v-show="keyImage == item.position"
          id="image"
          class="iframe"
          :src="`/uploads/slider/images/${item.name}`"
          :style="{
            width: zoom + '%',
          }"
        />

        <div>
          <img
            :src="`/assets/slider/slider2.svg`"
            class="iconSlider2"
            @click="nextImage()"
          />
        </div>
      </div>

      <div class="controls">
        <img
          :src="`/assets/slider/izda.svg`"
          class="iconsSvg"
          @click="lastImage()"
        />

        <p v-if="imagesSlider.data">
          {{ keyImage }} of {{ imagesSlider.data.length }}
        </p>

        <img
          :src="`/assets/slider/derecha.svg`"
          class="iconsSvg"
          @click="nextImage()"
        />

        <img
          v-show="play == 0"
          :src="`/assets/slider/video.svg`"
          class="iconsSvg"
          @click="autoPlay()"
        />

        <img
          v-show="play != 0"
          :src="`/assets/slider/pausa.svg`"
          class="iconsSvg"
          @click="pause()"
        />

        <img
          :src="`/assets/slider/zoom+.svg`"
          class="iconsSvg"
          @click="zoomIncrement()"
        />

        <img
          :src="`/assets/slider/zoom-.svg`"
          class="iconsSvg"
          @click="zoomDecrease()"
        />
      </div>
    </div>

    <div class="Spinner">
      <spinner />
    </div>
  </div>
</template>

<script>
import { get } from "vuex-pathify";
import Spinner from "../base/Spinner";

export default {
  name: "carouselmage",

  components: {
    Spinner,
  },

  props: ["chapter"],

  data() {
    return {
      keyImage: 1,
      play: 0,
      zoom: 45,
      image: document.getElementById("image"),
      imagesSlider: [],
      infoChapter: undefined,
      mouseDownState: false,
      x: 0,
      y: 0,
      currentFrame: 1,
      minDistance: 20,
      frame: "",
    };
  },

  async created() {
    const data = await this.$store.dispatch(
      "puzzleModule/getImagesSlider",
      this.chapter
    );
    this.imagesSlider = data.data;

    this.infoChapter = await this.$store.dispatch(
      "chapterModule/getState",
      this.chapter
    );
    await this.$store
      .dispatch("chapterModule/getState", this.chapter)
      .then(async (data) => {
        await this.$store.dispatch("timeModule/initStart", this.userChapter());

        const chapterStatus = {
          chapter: this.chapter,
          finished: false,
        };
        if (!this.infoChapter.finished) {
          await this.$store.dispatch(
            "chapterModule/updateState",
            chapterStatus
          );
        }
      });
    document.onwheel = this.zoomImage;
    window.load = this.init();
  },

  computed: {
    ...get("chapterModule", ["userChapter"]),
  },

  methods: {
    async nextImage() {
      clearInterval(this.play);
      this.play = 0;
      const keyImage = this.keyImage + 1;
      this.keyImage = keyImage;

      if (keyImage > this.imagesSlider.data.length) {
        this.keyImage = 1;
      } else if (keyImage === this.imagesSlider.data.length) {
        this.finishChapter();
      }

      return keyImage;
    },

    async finishChapter() {
      const chapterStatus = {
        chapter: this.chapter,
        finished: true,
      };

      await this.$store.dispatch("chapterModule/updateState", chapterStatus);
      this.infoChapter = await this.$store.dispatch(
        "chapterModule/getState",
        this.chapter
      );
    },

    lastImage() {
      clearInterval(this.play);
      this.play = 0;
      const keyImage = this.keyImage - 1;
      this.keyImage = keyImage;

      if (keyImage === 0) {
        this.keyImage = this.imagesSlider.data.length;
        this.finishChapter();
      }

      return keyImage;
    },

    executePlay() {
      const keyImage = this.keyImage + 1;
      this.keyImage = keyImage;

      if (keyImage > this.imagesSlider.data.length) {
        this.keyImage = 1;
      } else if (keyImage === this.imagesSlider.data.length) {
        this.finishChapter();
      }

      return keyImage;
    },

    autoPlay() {
      this.play = setInterval(this.executePlay, 35);
      return this.play;
    },

    pause() {
      clearInterval(this.play);
      this.play = 0;
    },

    zoomIncrement() {
      this.zoom += 5;
    },

    zoomDecrease() {
      if (this.zoom >= 40) {
        this.zoom -= 5;
      }
    },

    zoomImage(event) {
      let scale = 1;

      if (event.deltaY < 0) {
        scale *= event.deltaY * -2;
      } else {
        scale /= event.deltaY * 2;
      }

      scale = Math.min(Math.max(0, scale), 1);

      if (scale === 1) {
        if (this.zoom <= 95) {
          this.zoom += 5;
        }
      } else {
        if (this.zoom > 40) {
          this.zoom -= 5;
        }
      }
    },

    init() {
      this.frame = document.querySelector(".frame");

      window.addEventListener("mousedown", this.mouseDown);
      window.addEventListener("mouseup", this.mouseUp);
      window.addEventListener("mousemove", this.mouseMove);

      this.renderFrame();
    },

    mouseUp() {
      this.mouseDownState = false;
    },

    mouseDown(e) {
      this.x = e.offsetX;
      this.y = e.offsetY;

      this.mouseDownState = true;
    },

    mouseMove(e) {
      if (this.mouseDownState) {
        const xDistance = this.x - e.offsetX;
        if (this.isStep(xDistance)) {
          const direction = this.getDirection(xDistance);
          this.nextFrame(direction);
          this.renderFrame();

          this.x = e.offsetX;
          this.y = e.offsetY;
        }
      }
    },

    isStep(distance) {
      return Math.abs(distance) > this.minDistance;
    },

    getDirection(distance) {
      return distance > 0 ? 1 : -1;
    },

    nextFrame(direction) {
      this.currentFrame += direction;

      if (this.currentFrame >= this.imagesSlider.data.length)
        this.currentFrame = 0;
      if (this.currentFrame < 0)
        this.currentFrame = this.imagesSlider.data.length;

      if (
        this.currentFrame > 0 &&
        this.currentFrame <= this.imagesSlider.data.length
      ) {
        this.keyImage = this.currentFrame;
      }
    },

    renderFrame() {
      this.frame = `${this.currentFrame}`;
    },
  },
};
</script>

 <style scoped lang="scss"> 
.CarouselImage {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-content: center;
  width: 100%;
  height: 100%;

  .iconsSvg {
    width: 2rem;
    margin-left: 1rem;
    cursor: pointer;
  }

  .slider {
    display: flex;
    justify-content: center;
    position: absolute;
    align-items: center;
    width: 100%;
    height: 100%;
    .iconSlider {
      position: fixed;
      width: 2rem;
      left: 20;
      z-index: 1;
      cursor: pointer;
    }
    .iconSlider2 {
      position: fixed;
      width: 2rem;
      right: 20;
      z-index: 1;
      cursor: pointer;
    }
    img {
      background: #fff;
    }
  }

  .controls {
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background: var(--color-secondary);
    box-shadow: 0 4px 4px #0000002e;
    color: #565656;
    .button {
      margin-left: 1rem;
      align-content: flex-start;
      justify-content: flex-start;
    }
    p {
      color: #ffff;
      width: 5rem;
      text-align: center;
      margin-left: 1rem;
      margin-bottom: 0;
    }
  }
}

.spinner {
  margin-top: 3rem;
}
</style>
