<template>
    <div class="results">
        <h4 class="mb-1">Results ({{ correctAnswers }}/{{ questions.length }})</h4>
        <div class="questions">
            <div class="question" v-for="(item, index) in questions" :key="index">
                <h5>{{ item.question.question }}</h5>
                <div class="answers">
                    <div class="answer"
                         v-for="(answer, option) in item.question.answers"
                         :class="answer.id === item.id ? 'selected' : ''"
                    >
                        <div class="answer-option"
                             :class="answer.correct ? 'correct' : answer.id === item.id ? 'wrong' : ''"
                        >{{ options[option] }}
                        </div>
                        <div class="answer-text">{{ answer.answer }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>

export default {
    name: 'RouletteResults',
    props: ['questions'],

    data() {
        return {
            options: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'],
        }
    },

    computed: {
        correctAnswers: function () {
            let correct = 0;
            for (let i = 0; i < this.questions.length; i++) {
                if (this.questions[i].correct) {
                    correct++;
                }
            }

            return correct;
        },
    }
};
</script>

 <style scoped lang="scss"> 
</style>
