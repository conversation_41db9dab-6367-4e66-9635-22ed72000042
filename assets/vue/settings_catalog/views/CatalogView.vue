<template>
  <div>
    <home
      :title="catalog?.name || ''"
      :description="catalog?.description || ''"
      src-thumbnail="/assets/imgs/survey_app.svg"
    >
      <template v-slot:content-main>
        <router-view />
      </template>
    </home>
  </div>
</template>

<script>
import { get } from "vuex-pathify";

import Home from "../../base/Home.vue";

//import { catalogs } from "../utils/catalogs";


export default {
  data() {
    return {
      catalogsBD: [],
    };
  },

  components: {
    home: Home,
  },

  computed: {
    ...get("catalogModule", ["getCatalogsFromBd"]),
    catalog() {

      const catalogs = this.getCatalogsFromBd();

      return catalogs.find(
        (catalog) => catalog.id === this.$route.params.catalog
      );
    },
  },

  async created() {
    this.$store.dispatch("contentTitleModule/addRoute", {
      routeName: this.$route.name,
      params: {
        linkName: this.catalog?.name,
        params: this.$route.params,
      },
    });
  },

  async mounted() {
    await this.$store.dispatch(
      "catalogModule/loadCatalogsFromBd",
      "/admin/catalog/all"
    );
  },
};
</script>

 <style scoped lang="scss">
:deep(.Home--content) {
  padding: 0;
}
:deep(.Home--content--actions) {
  display: none;
}
</style>
