import Home from "../views/HomeView.vue";
import Catalog from "../views/CatalogView.vue";
import AnnouncementCriteria from "../components/announcementCriteria/AnnouncementCriteria.vue";
import AnnouncementCriteriaForm from "../components/announcementCriteria/Form.vue";
import AnnouncementModality from "../components/announcementModality/AnnouncementModality.vue";
import AnnouncementModalityForm from "../components/announcementModality/Form.vue";
import TypeCourse from "../components/typeCourse/TypeCourse.vue";
import TypeCourseForm from "../components/typeCourse/Form.vue";
import TranslationsAdmin from "../components/translationsAdmin/TranslationsAdmin.vue";
import TranslationsAdminForm from "../components/translationsAdmin/Form.vue";
import ChapterTypes from "../components/chapterType/ChapterTypes.vue";
import ChapterTypesForm from "../components/chapterType/Form.vue";
import CertificateTypes from "../components/certificateTypes/CertificateTypes.vue";
import CertificateTypesForm from "../components/certificateTypes/Form.vue";
import CertificateTypesPreview from "../components/certificateTypes/CertificateTypesPreview.vue";
import AlertTypeTutor from "../components/alertTypeTutor/AlertTypeTutor.vue";
import AlertTypeTutorForm from "../components/alertTypeTutor/Form.vue";
import ConfigurationClientAnnouncement
    from "../components/configurationClientAnnouncement/ConfigurationClientAnnouncement.vue";
import ConfigurationClientAnnouncementForm from "../components/configurationClientAnnouncement/Form.vue";
import TypeMoney from "../components/typeMoney/TypeMoney.vue";
import TypeMoneyForm from "../components/typeMoney/Form.vue";
import ExtraData from "../components/extraData/ExtraData.vue";
import ExtraDataForm from "../components/extraData/Form.vue";
import SettingGroup from "../components/settingGroup/SettingGroup.vue";
import SettingGroupForm from "../components/settingGroup/Form.vue";
import Setting from "../components/setting/Setting.vue";
import SettingForm from "../components/setting/Form.vue";
import Company from "../components/company/Company.vue";
import CompanyForm from "../components/company/Form.vue";
import ProfessionalCategory from "../components/professionalCategory/ProfessionalCategory.vue";
import ProfessionalCategoryForm from "../components/professionalCategory/Form.vue";
import UserWorkCenter from "../components/userWorkCenter/UserWorkCenter.vue";
import UserWorkCenterForm from "../components/userWorkCenter/Form.vue";
import UserWorkDepartment from "../components/userWorkDepartment/UserWorkDepartment.vue";
import UserWorkDepartmentForm from "../components/userWorkDepartment/Form.vue";
import UserStudyLevel from "../components/userStudyLevel/UserStudyLevel.vue";
import UserStudyLevelForm from "../components/userStudyLevel/Form.vue"; 
import TypeCourseAnnouncementStepCreation from "../components/TypeCourseAnnouncementStepCreation/TypeCourseAnnouncementStepCreation.vue";
import TypeCourseAnnouncementStepCreationForm from "../components/TypeCourseAnnouncementStepCreation/Form.vue";
import ClassroomvirtualType from "../components/classroomvirtualType/ClassroomvirtualType.vue";
import ClassroomvirtualTypeForm from "../components/classroomvirtualType/Form.vue";
import TypeIdentification from "../components/typeIdentification/TypeIdentification.vue";
import TypeIdentificationForm from "../components/typeIdentification/Form.vue";
import UserExtraFields from "../components/userExtraFields/UserExtraFields.vue";
import UserExtraFieldsForm from "../components/userExtraFields/Form.vue";

export default [
    {
        path: '/admin/apps/catalogs',
        component: Home,
        name: 'Home'
    },
    {
        path: '/admin/apps/catalogs/:catalog',
        component: Catalog,
        name: 'Catalog',
        children: [
            {
                path: 'announcement-criteria',
                name: 'AnnouncementCriteria',
                component: AnnouncementCriteria
            },
            {
                path: 'announcement-criteria/:id/update',
                name: 'AnnouncementCriteriaUpdate',
                component: AnnouncementCriteriaForm
            },
            {
                path: 'announcement-criteria/create',
                name: 'AnnouncementCriteriaCreate',
                component: AnnouncementCriteriaForm
            },
            {
                path: 'type-course',
                name: 'TypeCourse',
                component: TypeCourse
            },
            {
                path: 'type-course/:id/update',
                name: 'TypeCourseUpdate',
                component: TypeCourseForm
            },
            {
                path: 'type-course/create',
                name: 'TypeCourseCreate',
                component: TypeCourseForm
            },
            {
                path: 'translations-admin',
                name: 'TranslationsAdmin',
                component: TranslationsAdmin
            },
            {
                path: 'translations-admin/update',
                name: 'TranslationsAdminUpdate',
                component: TranslationsAdminForm
            },
            {
                path: 'translations-admin/create',
                name: 'TranslationsAdminCreate',
                component: TranslationsAdminForm
            },
            {
                path: 'chapter-type',
                name: 'ChapterTypes',
                component: ChapterTypes
            },
            {
                path: 'chapter-type/:id/update',
                name: 'ChapterTypesUpdate',
                component: ChapterTypesForm
            },
            {
                path: 'certificate-types',
                name: 'DiplomasType',
                component: CertificateTypes
            },
            {
                path: 'certificate-types/:id/update',
                name: 'DiplomasTypeUpdate',
                component: CertificateTypesForm
            },
            {
                path: 'certificate-types/create',
                name: 'DiplomasTypeCreate',
                component: CertificateTypesForm
            },
            {
                path: 'certificate-types/preview',
                name: 'DiplomasTypePreview',
                component: CertificateTypesPreview
            },
            {
                path: 'certificate-types/generateDiplomaPreview/:id',
                name: 'DiplomasTypePreviewUrl',
                component: CertificateTypesPreview
            },
            {
                path: 'certificate-types/generateDiplomaPreview/:id',
                name: 'DiplomasTypePreviewUrl',
                component: CertificateTypesPreview
            },
            {
                path: 'alert-type-tutor',
                name: 'AlertTypeTutor',
                component: AlertTypeTutor
            },
            {
                path: 'alert-type-tutor/:id/update',
                name: 'AlertTypeTutorUpdate',
                component: AlertTypeTutorForm
            },
            {
                path: 'alert-type-tutor/create',
                name: 'AlertTypeTutorCreate',
                component: AlertTypeTutorForm
            },
            {
                path: 'configuration-client-announcement',
                name: 'ConfigurationClientAnnouncement',
                component: ConfigurationClientAnnouncement
            },
            {
                path: 'configuration-client-announcement/:id/update',
                name: 'ConfigurationClientAnnouncementUpdate',
                component: ConfigurationClientAnnouncementForm
            },
            {
                path: 'type-money',
                name: 'TypeMoney',
                component: TypeMoney
            },
            {
                path: 'type-money/:id/update',
                name: 'TypeMoneyUpdate',
                component: TypeMoneyForm
            },
            {
                path: 'type-money/create',
                name: 'TypeMoneyCreate',
                component: TypeMoneyForm
            },
            {
                path: 'extra-data',
                name: 'ExtraData',
                component: ExtraData
            },
            {
                path: 'extra-data/:id/update',
                name: 'ExtraDataUpdate',
                component: ExtraDataForm
            },
            {
                path: 'extra-data/create',
                name: 'ExtraDataCreate',
                component: ExtraDataForm
            },
            {
                path: 'setting-group',
                name: 'SettingGroup',
                component: SettingGroup
            },
            {
                path: 'setting-group/:id/update',
                name: 'SettingGroupUpdate',
                component: SettingGroupForm
            },
            {
                path: 'setting-group/create',
                name: 'SettingGroupCreate',
                component: SettingGroupForm
            },
            {
                path: 'setting-group/:id/delete',
                name: 'SettingGroupDelete',
                component: SettingGroup
            },
            {
                path: 'setting',
                name: 'Setting',
                component: Setting
            },
            {
                path: 'setting/:id/update',
                name: 'SettingUpdate',
                component: SettingForm
            },
            {
                path: 'setting/create',
                name: 'SettingCreate',
                component: SettingForm
            },
            {
                path: 'setting/:id/delete',
                name: 'SettingDelete',
                component: Setting
            },
            {
                path: 'company',
                name: 'Company',
                component: Company
            },
            {
                path: 'company/:id/update',
                name: 'CompanyUpdate',
                component: CompanyForm
            },
            {
                path: 'company/create',
                name: 'CompanyCreate',
                component: CompanyForm
            },
            {
                path: 'professionalCategory',
                name: 'ProfessionalCategory',
                component: ProfessionalCategory
            },
            {
                path: 'professionalCategory/:id/update',
                name: 'ProfessionalCategoryUpdate',
                component: ProfessionalCategoryForm
            },
            {
                path: 'professionalCategory/create',
                name: 'ProfessionalCategoryCreate',
                component: ProfessionalCategoryForm
            },
            {
                path: 'userWorkCenter',
                name: 'UserWorkCenter',
                component: UserWorkCenter
            },
            {
                path: 'userWorkCenter/:id/update',
                name: 'UserWorkCenterUpdate',
                component: UserWorkCenterForm
            },
            {
                path: 'userWorkCenter/create',
                name: 'UserWorkCenterCreate',
                component: UserWorkCenterForm
            },
            {
                path: 'userWorkDepartment',
                name: 'UserWorkDepartment',
                component: UserWorkDepartment
            },
            {
                path: 'userWorkDepartment/:id/update',
                name: 'UserWorkDepartmentUpdate',
                component: UserWorkDepartmentForm
            },
            {
                path: 'userWorkDepartment/create',
                name: 'UserWorkDepartmentCreate',
                component: UserWorkDepartmentForm
            },
            {
                path: 'userStudyLevel',
                name: 'UserStudyLevel',
                component: UserStudyLevel
            },
            {
                path: 'userStudyLevel/:id/update',
                name: 'UserStudyLevelUpdate',
                component: UserStudyLevelForm
            },
            {
                path: 'userStudyLevel/create',
                name: 'UserStudyLevelCreate',
                component: UserStudyLevelForm
            },
            {
                path: 'TypeCourse-AnnouncementStepCreation',
                name: 'TypeCourseAnnouncementStepCreation',
                component: TypeCourseAnnouncementStepCreation
            },
            {
                path: 'TypeCourse-AnnouncementStepCreation/:id/update',
                name: 'TypeCourseAnnouncementStepCreationUpdate',
                component: TypeCourseAnnouncementStepCreationForm
            },
            {
                path: 'TypeCourse-AnnouncementStepCreation/create',
                name: 'TypeCourseAnnouncementStepCreationCreate',
                component: TypeCourseAnnouncementStepCreationForm
            },
            {
                path: 'classroomvirtual-Type',
                name: 'ClassroomvirtualType',
                component: ClassroomvirtualType
            },
            {
                path: 'classroomvirtual-Type/:id/update',
                name: 'ClassroomvirtualTypeUpdate',
                component: ClassroomvirtualTypeForm
            },
            {
                path: 'classroomvirtual-Type/create',
                name: 'ClassroomvirtualTypeCreate',
                component: ClassroomvirtualTypeForm
            },
            {
                path: 'type-identification',
                name: 'TypeIdentification',
                component: TypeIdentification
            },
            {
                path: 'type-identification/:id/update',
                name: 'TypeIdentificationUpdate',
                component: TypeIdentificationForm
            },
            {
                path: 'type-identification/create',
                name: 'TypeIdentificationCreate',
                component: TypeIdentificationForm
            },
            {
                path: 'announcement-modality',
                name: 'AnnouncementModality',
                component: AnnouncementModality
            },
            {
                path: 'announcement-modality/:id/update',
                name: 'AnnouncementModalityUpdate',
                component: AnnouncementModalityForm
            },
            {
                path: 'announcement-modality/create',
                name: 'AnnouncementModalityCreate',
                component: AnnouncementModalityForm
            },
            {
                path: 'user-extra-fields',
                name: 'UserExtraFields',
                component: UserExtraFields
            },
            {
                path: 'user-extra-fields/:id/update',
                name: 'UserExtraFieldsUpdate',
                component: UserExtraFieldsForm
            },
            {
                path: 'user-extra-fields/create',
                name: 'UserExtraFieldsCreate',
                component: UserExtraFieldsForm
            },
        ]
    }

];
