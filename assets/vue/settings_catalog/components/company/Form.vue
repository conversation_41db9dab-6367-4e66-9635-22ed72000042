<template>
  <div class="d-flex w-100 align-items-center justify-content-center" v-if="loading">
    <spinner/>
  </div>
  <div class="FormView" v-else>
    <div class="form-group col-12">
      <label>{{ $t('NAME') }}*</label>
      <input type="text" class="form-control" v-model="userCompany.name">
    </div>

    <div class="form-group col-12">
      <label>{{ $t('COMPANY.PROFYLE') }}</label>
      <input type="text" class="form-control" v-model="userCompany.profile">
    </div>

    <div class="form-group col-12">
      <label>{{ $t('COMPANY.CIF') }}</label>
      <input type="text" class="form-control" v-model="userCompany.cif">
    </div>

    <div class="form-group col-12">
      <label>{{ $t('COMPANY.CODE') }}</label>
      <input type="text" class="form-control" v-model="userCompany.code">
    </div>


    <div class="form-group col-12">
      <label>{{ $t('COMPANY.STATE') }}</label>
      <BaseSwitch v-model="userCompany.state" />
    </div>
    <div class="form-group col-12">
      <label>{{ $t('DESCRIPTION') }}</label>      
      <textarea
          type="text"
          class="form-control"
          name="description"
          v-model="userCompany.description"
          rows="5"
        />
    </div>

    <div>
      <button class="btn btn-primary btn-center" @click="submit">
        <i class="fas fa-save"></i> {{ $t('SAVE') }}
      </button>
      <button class="btn btn-warning btn-center" @click="returnToList">
        <i>&times;</i> {{ $t('CANCEL') }}
      </button>
    </div>
  </div>
  
</template>

<script>
import BaseForm from "../BaseForm.vue";
import {get} from "vuex-pathify";
import BaseSwitch from "../../../base/BaseSwitch.vue";
import Spinner from "../../../admin/components/base/Spinner.vue";

export default {
  name: "Form",
  components: {BaseSwitch, BaseForm, Spinner},
  data() {
    return {
      locale: 'es',
      userCompany: {
        id: -1,
        name: '',
        description: '',
        state: true,
        cif: '',
        profile: '',  
      },
    };
  },
  computed: {
    loading: get('catalogModule/loading'), 
    catalogs: get('catalogModule/catalogs'),
    locales: get('localeModule/locales'),
  },
  created() {
    // if (this.catalogs.length === 0) {
    //   this.returnToList();
    //   return;
    // }

    let userCompany = {
        id: -1,
        name: '',
        description: '',
        state: true,
        cif: '',
        profile: '',  
      };

    if (this.$route.name === 'CompanyUpdate') {
      userCompany = this.catalogs.find(c => c.id === this.$route.params.id);

      if (userCompany === undefined) {
        this.returnToList();
        return;
      }
    }

    this.userCompany = userCompany;
  },
  methods: {
    returnToList() {
      this.$router.push({name: 'Company', params: this.$route.params});
    },
    submit() {
      if(this.userCompany.name===""){
        this.$toast.error(this.$t('COMPANY.ERROR1.DESCRIPTION') + '');
        return;
      }else{
        const update = this.$route.name === 'CompanyUpdate';
        const endpoint = update ? '/admin/company/update' : '/admin/company/create';
        const save = () => {
          return this.$store.dispatch('catalogModule/save', { endpoint: endpoint, data: this.userCompany });
        }

        save().then(r => {
          const { error, data } = r;
          if (error) this.$toast.error(data);
          else {
            this.$toast.success(this.$t('CATALOG.SAVED') + '');
            this.returnToList();
          }
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">

</style>
