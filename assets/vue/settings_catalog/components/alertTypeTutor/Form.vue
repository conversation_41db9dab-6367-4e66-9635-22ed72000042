<script>
import Multiselect from "vue-multiselect";
import 'vue-multiselect/dist/vue-multiselect.min.css'
import {get} from "vuex-pathify";
import axios from "axios";
import BaseSwitch from "../../../base/BaseSwitch.vue";
import BaseForm from "../BaseForm.vue";

export default {
  name: "Form",
  components: {BaseSwitch, BaseForm, Multiselect},
  data() {
    return {
      locale: 'es',
      alertType: {
        id: -1,
        name: '',
        description: '',
        active: false,
        translations: [],
        typeCourses: []
      },
      typeCourses: []
    };
  },
  computed: {
    catalogs: get('catalogModule/catalogs'),
    locales: get('localeModule/locales'),
  },
  created() {
    if (this.catalogs.length === 0) {
      this.returnToList();
      return;
    }

    let alertType = {
      id: -1,
      name: '',
      description: '',
      active: false,
      translations: [],
      typeCourses: []
    };

    if (this.$route.name === 'AlertTypeTutorUpdate') {
      alertType = this.catalogs.find(c => c.id === this.$route.params.id);
      if (alertType === undefined) {
        this.returnToList();
        return;
      }
    }

    let translations = [];
    const keys = Object.keys(this.locales);
    keys.forEach((k) => {
      const translated = alertType.translations.find(t => t.locale === k);
      translations.push({
        locale: k,
        name: translated?.name ?? '',
        description: translated?.description ?? ''
      })
    });

    alertType.translations = translations;
    this.alertType = alertType;
    axios.get('/admin/type-course/all').then(r => {
      const { data } = r.data;
      this.typeCourses = data.map(t => ({id: t.id, name: t.name}));
    })
  },
  methods: {
    returnToList() {
      this.$router.push({name: 'AlertTypeTutor', params: this.$route.params});
    },

    submit() {
      const update = this.$route.name === 'AlertTypeTutorUpdate';
      const endpoint = `/admin/alert-type-tutor/${update ? 'update' : 'create'}`;
      const save = () => {
        return this.$store.dispatch('catalogModule/save', { endpoint: endpoint, data: this.alertType });
      }

      save().then(r => {
        const { error, data } = r;
        if (error) this.$toast.error(data);
        else {
          this.$toast.success(this.$t('CATALOG.SAVED') + '');
          this.returnToList();
        }
      })
    }
  }
}
</script>

<template>
  <base-form v-model="locale" @cancel="returnToList" @submit="submit">
    <template v-slot:form>
      <div class="form-group col-12">
        <label>{{ $t('NAME') }}</label>
        <input type="text" class="form-control" v-model="alertType.name">
      </div>

      <div class="form-group col-12">
        <label>{{ $t('DESCRIPTION') }}</label>
        <textarea class="form-control" v-model="alertType.description" rows="5"></textarea>
      </div>

      <div class="form-group col-12 d-flex align-items-center justify-content-start">
        <BaseSwitch :tag="`switcher-alert-type-form-active-${alertType.id}`"
                     v-model="alertType.active"/>
        <label class="ml-1">{{ $t('ACTIVE') }}</label>
      </div>
      <div class="w-100 mb-5">
        <label for="">{{ $t("COURSE.TYPE_COURSE") }} [{{ (alertType.typeCourses.length === 0 || alertType.typeCourses.length === typeCourses.length) ? $t('ALL') : alertType.typeCourses.length  }}]</label>
        <Multiselect
            v-model="alertType.typeCourses"
            :options="typeCourses"
            :multiple="true"
            track-by="name"
            label="name"
            :placeholder="$t('MULTISELECT.PLACEHOLDER')"
            :selectLabel="$t('MULTISELECT.SELECT_LABEL')"
            :deselectLabel="$t('MULTISELECT.DESELECT_LABEL')"
        ></Multiselect>
      </div>
    </template>
    <template v-slot:translations>
      <div v-for="t in alertType.translations" :key="t.locale" v-if="t.locale === locale">
        <div class="form-group col-12">
          <label>{{ $t('NAME') }}</label>
          <input type="text" class="form-control" v-model="t.name">
        </div>

        <div class="form-group col-12">
          <label>{{ $t('DESCRIPTION') }}</label>
          <textarea class="form-control" v-model="t.description" rows="5"></textarea>
        </div>
      </div>
    </template>
  </base-form>
</template>

<style scoped lang="scss">

</style>
