<script>
import BaseForm from "../BaseForm.vue";
import {get} from "vuex-pathify";
import BaseSwitch from "../../../base/BaseSwitch.vue";
import Multiselect from "vue-multiselect";

export default {
  name: "Form",
  components: {BaseSwitch, BaseForm, Multiselect}, 
  data() {
    return {
      locale: 'es',
      typeMoney: {
        id: -1,
        name: '',
        symbol: '',
        country:'',
        fractionalUnit:'',
        codeIso:'',
        state: false,
        translations: []
      },
    };
  },
  computed: {
    catalogs: get('catalogModule/catalogs'),
    locales: get('localeModule/locales'),
  },
  created() {
    // if (this.catalogs.length === 0) {
    //   this.returnToList();
    //   return;
    // }

    let typeMoney = {
      id: -1,
      name: '',
      symbol: '',
      country:'',
      fractionalUnit:'',
      codeIso:'',
      state: false,
      translations: []
    };
    
    if (this.$route.name === 'TypeMoneyUpdate') {
      typeMoney = this.catalogs.find(c => c.id === this.$route.params.id);
      if (typeMoney === undefined) {
        this.returnToList();
        return;
      }
    }

    let translations = [];

    const keys = Object.keys(this.locales);
    keys.forEach((k) => {
      const translated = typeMoney.translations.find(e => e.locale === k);
      translations.push({
        locale: k,
        name: translated?.name ?? '',
        country: translated?.country ?? ''
      })
    });

    console.log(translations)
    typeMoney.translations = translations;

    this.typeMoney = typeMoney;
  },
  methods: {
    returnToList() {
      this.$router.push({name: 'TypeMoney', params: this.$route.params});
    },

    submit() {
      const update = this.$route.name === 'TypeMoneyUpdate';
      const endpoint = update ? '/admin/type-money/update' : '/admin/type-money/create';
      const save = () => {
        return this.$store.dispatch('catalogModule/save', { endpoint: endpoint, data: this.typeMoney });
      }

      save().then(r => {
        const { error, data } = r;
        if (error) this.$toast.error(data);
        else {
          this.$toast.success(this.$t('CATALOG.SAVED') + '');
          this.returnToList();
        }
      })
    }
  }
}
</script>

<template>
  <base-form v-model="locale" @cancel="returnToList()" @submit="submit()">
    <template v-slot:form>
      <div class="form-group col-12">
        <label>{{ $t('NAME') }}</label>
        <input type="text" class="form-control" v-model="typeMoney.name">
      </div>

      <div class="form-group col-12">
        <label>{{ $t('CATALOG.TYPE_MONEY.SYMBOL') }}</label>
        <input type="text" class="form-control" v-model="typeMoney.symbol">
      </div>

      <div class="form-group col-12">
        <label>{{ $t('CATALOG.TYPE_MONEY.COUNTRY') }}</label>
        <input type="text" class="form-control" v-model="typeMoney.country">
      </div>

      <div class="form-group col-12">
        <label>{{ $t('CATALOG.TYPE_MONEY.FRACTIONAL_UNIT') }}</label>
        <input type="text" class="form-control" v-model="typeMoney.fractionalUnit">
      </div>

      <div class="form-group col-12">
        <label>{{ $t('CATALOG.TYPE_MONEY.CODE_ISO') }}</label>
        <input type="text" class="form-control" v-model="typeMoney.codeIso">
      </div>

      <div class="form-group col-12 d-flex align-items-center justify-content-start">
        <BaseSwitch :tag="`switcher-type-money-form-active-${typeMoney.id}`"
                     v-model="typeMoney.state"/>
        <label class="ml-1">{{ $t('ACTIVE') }}</label>
      </div>
    </template>
    <template v-slot:translations>
      <div v-for="t in typeMoney.translations" :key="t.locale" v-if="t.locale === locale">
        <div class="form-group col-12">
          <label>{{ $t('NAME') }}</label>
          <input type="text" class="form-control" v-model="t.name">
        </div>

        <div class="form-group col-12">
          <label>{{ $t('COUNTRY') }}</label>
          <input type="text" class="form-control" v-model="t.country">
        </div>
      </div>
    </template>
  </base-form>
</template>

<style scoped lang="scss">

</style>
