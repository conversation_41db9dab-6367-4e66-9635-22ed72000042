<script>
import {get} from "vuex-pathify";
import Spinner from "../../../admin/components/base/Spinner.vue";
import BaseSwitch from "../../../base/BaseSwitch.vue";

export default {
  name: "AnnouncementCriteria",
  components: {BaseSwitch, Spinner},
  data() {
    return {

    };
  },
  computed: {
    loading: get('announcementCriteriaModule/loading'),
    catalogs: get('announcementCriteriaModule/catalogs'),
  },
  created() {
    this.$store.dispatch('announcementCriteriaModule/getCatalogs');
  },
  methods: {
    changeActiveStatus(index) {
      this.$store.dispatch('announcementCriteriaModule/changeActiveStatus', { id: this.catalogs[index].id, active: this.catalogs[index].active});
    }
  }
}
</script>

<template>
  <div>
    <div class="col-12 d-flex flex-row align-items-center justify-content-center" v-if="loading">
      <spinner />
    </div>
    <table class="table table-condensed mt-3" v-else>
      <thead>
      <tr>
        <th></th>
        <th>{{ $t('NAME') }}</th>
        <th>{{ $t('ACTIVE') }}</th>
        <th></th>
      </tr>
      </thead>
      <tbody>
      <tr v-for="(c, index) in catalogs" :key="c.id">
        <td></td>
        <td>{{ c.name }}</td>
        <td>
          <BaseSwitch :tag="`switcher-announcement-criteria-${c.id}`" v-model="c.active" @change="changeActiveStatus(index)" />
        </td>
        <td>
          <div class="dropdown">
            <button class="btn btn-default" type="button" :id="`dropdown-menu-${c.id}`" data-bs-toggle="dropdown" aria-expanded="false">
              <i class="fa fa-ellipsis-h"></i>
            </button>
            <ul class="dropdown-menu" :aria-labelledby="`dropdown-menu-${c.id}`">
              <li><router-link class="dropdown-item" :to="{ name: 'AnnouncementCriteriaUpdate', params: {...$route.params, id: c.id} }">{{ $t('EDIT') }}</router-link></li>
            </ul>
          </div>
        </td>
      </tr>
      </tbody>
    </table>
  </div>
</template>

<style scoped lang="scss">

</style>
