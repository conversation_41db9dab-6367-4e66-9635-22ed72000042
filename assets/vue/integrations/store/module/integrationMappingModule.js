import {make} from "vuex-pathify";
import axios from "axios";

const state = {
    loading: false,
    saving: false,
    groups: [],
    current: {
        index: -1,
        group: null,
        mappings: []
    }
};

export const getters = {
    ...make.getters(state)
};

export const mutations = {
    ...make.mutations(state),
    updateGroup(state, { group, index}) {
        state.groups[index] = group;
        state.current.group = group;
        state.current.mappings = group.mappings;
    },

    setMapping(state, {mappings}) {
        state.current.mappings = mappings;
        state.current.group.mappings = mappings;
    }
};

export const actions = {
    ...make.actions(state),
    setCurrentGroupMapping({ commit }, mappings) {
        commit('setMapping', {mappings});
    },
    getGroups({ commit }) {
        commit('SET_LOADING', true);
        axios.get('/admin/api/v1/integrations/mapping/groups').then(r => {
            const { data } = r.data;
            if (data.length > 0) {
                commit('SET_CURRENT', {
                    index: 0,
                    group: data[0],
                    mappings: data[0]?.mappings ?? []
                })
            }
            commit('SET_GROUPS', data);
        }).finally(() => {
            commit('SET_LOADING', false);
        });
    },

    saveMappingGroup({ getters, commit }) {
        const { current } = getters;
        commit('SET_SAVING', true);
        return axios.post('/admin/api/v1/integrations/mapping/group', current.group).then(r => {
            console.log(r);
            const { data } = r.data;
            current.group = data;
            if (current.index >= 0) {
                commit('updateGroup', { group: data, index: current.index});
            }
            return Promise.resolve();
        }).catch(e => {
            return Promise.reject(e);
        }).finally(() => {
            commit('SET_SAVING', false);
        });
    },

    addMapping({ commit }, mapping) {

    }
};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions
}
