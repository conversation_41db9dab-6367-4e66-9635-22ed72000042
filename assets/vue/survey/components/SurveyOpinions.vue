<script>
import Pagination from "../../admin/components/Pagination.vue";
import axios from "axios";
import Opinions from "../../common/components/opinions/Opinions.vue";
import Spinner from "../../base/BaseSpinner.vue";

export default {
  name: "SurveyOpinions",
  components: {Spinner, Opinions, Pagination},
  data() {
    return {
      opinions: [],
      totalItems: 0,
      pageSize: 20,
      page: 1,
      loading: true
    };
  },
  created() {
    this.$store.dispatch('contentTitleModule/addRoute', {
      routeName: this.$route.name,
      params: {
        linkName: this.$t('OPINIONS.HOME.TITLE'),
        params: {}
      }
    });
    this.getOpinions();
  },
  methods: {
    onCurrentPage(page) {
      this.page = page;
      this.getOpinions();
    },

    getOpinions() {
      const url = new URL(window.location.origin + '/admin/nps/opinions');
      url.searchParams.set('page', this.page);
      url.searchParams.set('pageSize', this.pageSize);
      url.searchParams.set('surveyId', this.$route.params.id);

      this.loading = true;
      axios.get(url.toString()).then(r => {
        const { data } = r.data;
        this.opinions = data.items;
        this.totalItems = data['total-items'];
      }).finally(() => {
        this.loading = false;
      });
    }
  }
}
</script>

<template>
  <div v-if="loading" class="d-flex align-items-center justify-content-center">
    <spinner />
  </div>
  <div class="Opinions" v-else>
    <Opinions :opinions="opinions" :enable-visibility="false"/>
    <pagination v-if="opinions.length > 0"
                :total-items="totalItems"
                :page-size="pageSize"
                :number-of-chips="6"
                :prop-current-page="page"
                @current-page="onCurrentPage"
    />
  </div>
</template>

<style scoped lang="scss">

</style>
