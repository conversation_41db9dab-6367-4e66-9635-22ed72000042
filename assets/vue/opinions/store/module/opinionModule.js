import axios from "axios";
import {make} from "vuex-pathify";

const state = {
    opinions: [],
    totalItems: [],
    loading: true,
    page: 1,
    pageSize: 20,
    courses: [],
    hideEmptyOpinions: false
};

const getters = {
    ...make.getters(state)
};

const mutations = {
    ...make.mutations(state)
}

/**
 * Share common state for froala editor
 */
export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions: {
        async getOpinions({ commit }, {
            page,
            pageSize,
            sort = null,
            direction = null,
            courseId = null,
            start = null,
            end = null,
            query = null
        }) {
            try {
                commit('SET_LOADING', true);
                const url = new URL(window.location.origin + '/admin/nps/opinions');
                url.searchParams.set('page', page);
                url.searchParams.set('pageSize', pageSize);
                if (sort) {
                    url.searchParams.set('sort', sort);
                    url.searchParams.set('direction', direction ?? 'ASC');
                }
                if (courseId && courseId.length > 0) url.searchParams.set('courseId', courseId);
                if (start && start.length > 0) url.searchParams.set('start', start);
                if (end && end.length > 0) url.searchParams.set('end', end);
                if (query && query.length > 0) url.searchParams.set('query', query);

                const result = await axios.get(url.toString());
                const { data } = result.data;

                data.items.map(item => {
                    item.visibleOptions = !(!item.text.length && data.hideEmptyOpinions)
                    return item;
                })
                commit('SET_OPINIONS', data.items);
                commit('SET_HIDE_EMPTY_OPINIONS', data.hideEmptyOpinions);
                commit('SET_TOTAL_ITEMS', data['total-items']);
            } finally {
                commit('SET_LOADING', false);
            }
        },

        async getAvailableCourses({ commit }) {
            const result = await axios.get('/admin/nps/available-courses');
            const { data } = result.data;
            commit('SET_COURSES', data);
        },

        async setOpinionVisibility({ commit }, { id, toPost = false}) {
            const result = await axios.patch(`/admin/nps/opinion/${id}/to-post`, { toPost });
            return result.data;
        },

        async setOpinionHighlight({ commit }, { id, toPost = false}) {
            const result = await axios.patch(`/admin/nps/opinion/${id}/highlight`, { toPost });
            return result.data;
        }
    }
}
