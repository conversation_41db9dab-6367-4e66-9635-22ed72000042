<template>
  <home title="OPINIONS.HOME.TITLE"
        description="OPINIONS.HOME.DESCRIPTION"
        src-thumbnail="/assets/imgs/opinions_home.svg"
        :allow-filters="true"
        @apply-filters="onApplyFilters"
        @clear-filters="onClearFilters"
  >
    <template v-slot:content-actions>
      <button class="btn btn-primary" @click="downloadReport()"><i class="fa fa-download"></i> {{ $t('DOWNLOAD_EXCEL') }}</button>
    </template>
    <template v-slot:content-filters>
      <div class="row w-100 pb-2">
        <div class="col-12 mb-3">
          <label for="course-selector">{{ $t('FILTER.SELECT_COURSE') }}</label>
          <Multiselect
            :options="options"
            track-by="id"
            label="name"
            :multiple="true"
            :close-on-select="false"
            :show-labels="false"
            :placeholder="$t('SELECT')"
            v-model="courseId"
          />
        </div>
        <div class="col-xs-12 col-3">
          <label for="start" class="form-label">{{ $t('FILTER.START_AT') }}</label>
          <input type="date" class="form-control" id="start" name="start" v-model="start">
        </div>
        <div class="col-xs-12 col-3">
          <label for="end" class="form-label">{{ $t('FILTER.FINISH_AT') }}</label>
          <input type="date" class="form-control" id="end" name="end" v-model="end">
        </div>
      </div>
    </template>
    <template v-slot:content-main>
      <div class="d-flex align-items-center justify-content-center" v-if="loading">
        <spinner />
      </div>
      <div v-else>
        <div class="col-xs-12 p-2 mb-3">
          <div class="col-xs-12 col-4 OrderSelector">
            <div>
              <label for="course-selector">{{ $t('ORDER_BY') }}</label>
              <select class="form-select" id="course-selector" aria-label="Default select example" v-model="sort.sort">
                <option v-for="(name, field) in orderFields" :value="field">{{ $t('ORDER.' + name) }}</option>
              </select>
            </div>
            <button type="button"
                    class="btn btn-sm btn-primary ml-2"
                    @click="sort.direction = sort.direction === 'ASC' ? 'DESC' : 'ASC'">
              <i class="fa" :class="sort.direction === 'ASC' ? 'fa-arrow-circle-down' : 'fa-arrow-circle-up'"></i>
            </button>
          </div>
        </div>

        <opinions
          :opinions="opinions"
          @on-opinion-visibility-change="changeOpinionVisibility"
          @on-opinion-highlight-change="changeOpinionHighlight"
          @open-details="setCurrentOpinion"
        />
        
        <div class="col-12" v-if="opinions.length === 0">
          <BaseNotResult />
        </div>
        
        <pagination v-if="opinions.length > 0"
                    :total-items="totalItems"
                    :page-size="pageSize"
                    :number-of-chips="6"
                    :prop-current-page="page"
                    @current-page="onCurrentPage"
        />
        <OpinionDetails :opinion="currentOpinion"/>
      </div>
    </template>
  </home>
</template>

<script>
import { get } from "vuex-pathify";
import Multiselect from 'vue-multiselect'
import '../../../css/vueMultiSelect.css';
import Home from "../../base/Home.vue";
import Opinions from "../../common/components/opinions/Opinions.vue";
import Pagination from "../../admin/components/Pagination.vue";
import Spinner from "../../admin/components/base/Spinner.vue";
import OpinionDetails from '../../admin/components/Opinions/OpinionDetails.vue'
import TaskQueueMixin from '../../mixins/TaskQueueMixin';

export default {
  name: "HomeView",
  components: { OpinionDetails, Home, Opinions, Pagination, Spinner, Multiselect },
  mixins: [
    TaskQueueMixin
  ],
  data() {
    return {
      pageSize: 20,
      page: 1,
      searchQuery: null,
      sort: {
        sort: 'createdAt',
        direction: 'DESC'
      },
      filters: {
        courseId: [],
        start: null,
        end: null
      },
      courseId: [],
      start: null,
      end: null,
      currentOpinion: {},
      queryCourse: null,
    };
  },
  computed: {
    orderFields: get('configModule/config@orderFields'),
    opinions: get('opinionModule/opinions'),
    totalItems: get('opinionModule/totalItems'),
    loading: get('opinionModule/loading'),
    courses: get('opinionModule/courses'),
    useGlobalEventBus: get('contentTitleModule/getUseGlobalEventBus'),
    options() {
      return (this.courses || []).map((course) => ({ id: course.id, name: `${ course.code } ${ course.name }` }));
    }
  },
  watch: {
    searchQuery(newValue) {
      if (newValue && newValue.length > 0) this.getOpinions();
    },
    sort: {
      handler: function (val, oldVal) {
        this.getOpinions();
      },
      deep: true
    }
  },
  created() {
    this.$store.dispatch('contentTitleModule/addRoute', {
      routeName: this.$route.name,
      params: {
        linkName: this.$t('OPINIONS.HOME.TITLE'),
        params: {}
      }
    });
    if (this.courses.length === 0) this.$store.dispatch('opinionModule/getAvailableCourses');
    this.getOpinions();
  },
  mounted() {
    if (this.useGlobalEventBus) {
      this.$eventBus.$on('search', e => {
        this.searchQuery = e;
      });
    }
  },
  beforeDestroy() {
    if (this.useGlobalEventBus) {
      this.$eventBus.$off('search');
    }
  },
  methods: {
    async downloadReport() {
      await this.enqueueTask({
        url: '/admin/opinions/report',
        method: 'GET',
        messages: {
          success: this.$t('OPINIONS.EXPORT.SUCCESS'),
          error: this.$t('OPINIONS.EXPORT.FAILED')
        }
      });
    },
    onApplyFilters() {
      this.filters.courseId = (this.courseId || []).map((course) => course.id);
      this.filters.start = this.start;
      this.filters.end = this.end;
      
      this.getOpinions();
    },
    onClearFilters() {
      this.courseId = [];
      this.start = null;
      this.end = null;
      this.filters = {
        courseId: [],
        start: null,
        end: null
      };
      this.getOpinions();
    },
    onCurrentPage(page) {
      this.page = page;
      this.getOpinions();
    },
    getOpinions() {
      this.$store.dispatch('opinionModule/getOpinions', {
        page: this.page,
        pageSize: this.pageSize,
        sort: this.sort.sort,
        direction: this.sort.direction,
        query: this.searchQuery,
        ...this.filters
      });
    },
    
    changeOpinionVisibility(opinion) {
      this.$store.dispatch('opinionModule/setOpinionVisibility', {
        id: opinion.id,
        toPost: opinion.visible
      }).then(res => {
        const { error } = res;
        const message = opinion.visible ?  'OPINION.TO_POST.SUCCESS' : 'OPINION.TO_POST.UNPUBLISED'
        if (error) this.$toast.error(this.$t('OPINION.TO_POST.FAILED') + '')
        else this.$toast.success(this.$t(message) + '')
      })
    },
    
    changeOpinionHighlight(opinion) {
      this.$store.dispatch('opinionModule/setOpinionHighlight', {
        id: opinion.id,
        toPost: opinion.highlight
      }).then(res => {
        const { error } = res;
        if (error) this.$toast.error(this.$t('OPINION.HIGHLIGHT.FAILED') + '')
        else this.$toast.success(this.$t('OPINION.HIGHLIGHT.SUCCESS') + '')
      })
    },
    
    setCurrentOpinion(opinion = {}) {
      this.currentOpinion = { ...opinion }
    }
  },
}
</script>

 <style scoped lang="scss"> 
.Home {
  :deep(.Home--header--banner) {
    height: 190px !important;
  }
  
  :deep(.Home--header) {
    background-color: #F6F7F8;
  }
  
  :deep(.Home--content) {
    background-color: #ffffff;
  }
  
  :deep(.Home--content--main), :deep(.Home--content--actions) {
    padding: 0 2rem 1rem !important;
  }
  
  .OrderSelector {
    display: grid;
    grid-template-columns: 1fr 50px;
    align-items: end;
    
    button {
      height: 35px;
    }
  }
  
  #course-selector{
    width: 200px;
  }
  
  :deep {
    .multiselect__placeholder {
      margin-bottom: 0 !important;
      color: var(--color-neutral-dark) !important;
    }
    
    .multiselect__tags {
      border: 1px solid var(--color-neutral-mid) !important;
    }
    
    .multiselect__tag {
      background-color: var(--color-primary) !important;
    }
  }
}
</style>
