<script>
import {get, sync} from 'vuex-pathify';
import Spinner from "../../base/BaseSpinner.vue";
import dateTimeFormatterMixin from "../../common/mixins/dateTimeFormatterMixin";
import Pagination from "../../admin/components/Pagination.vue";
import Diplomas from "./modals/DiplomaModal.vue";

export default {
  name: "HomeView",
  components: {Pagination, Spinner, Diplomas},
  mixins: [dateTimeFormatterMixin],
  data() {
    return {};
  },
  computed: {
    page: sync('zipModule/pagination@page'),
    totalItems: get('zipModule/pagination@totalItems'),
    loading: get('zipModule/loading'),
    files: get('zipModule/files'),
  },
  created() {
    this.$store.dispatch('contentTitleModule/addRoute', {
      routeName: this.$route.name,
      params: {
        linkName: this.$t('REPORTS_DIPLOMAS'),
        params: {}
      }
    });
    this.$store.dispatch('zipModule/loadFiles');
    setTimeout(() => {
      this.$store.dispatch('zipModule/loadFiles');
    }, 1000 * 60);
  },
  methods: {
    statusClass(status) {
      switch (status) {
        case 2: return 'bg-success';
        case -1: return 'bg-danger';
        default: return 'bg-info'
      }
    },
    updatePage(page) {
      this.page = page;
      this.$store.dispatch('zipModule/loadFiles');
    },
    getFormatFecha(fecha){
      if(fecha !== null && fecha !== undefined){
        return this.getDateTimeFormatted(fecha);
      }
      return '-'
    }
  }
}
</script>

<template>
  <div>
    <div v-if="loading" class="d-flex w-100 align-items-center justify-content-center">
      <spinner />
    </div>
    <div v-else>
      <div class="text-right" style="margin-top: 10px; margin-bottom: 10px; margin-right: 5px;">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#diplomaModal">
          <i class="fa fa-file"></i> {{ $t('DIPLOMAS_GENERATE') }}
        </button>
      </div>
      <table class="table table-condensed">
        <thead>
        <tr>
          <th class="col-5">{{ $t('NAME') }}</th>
          <th>{{ $t('CREATED_AT') }}</th>
          <th>{{ $t('STATUS') }}</th>
          <th>{{ $t('STATUS_INFORMATION.COMPLETED') }}</th>
          <th>{{ $t('AVALIABLE_UNTIL') }}</th>
        </tr>
        </thead>
        <tbody>
        <tr v-for="file in files" :key="file.id">
          <td>
            <a v-if="file.status === 2" target="_blank" :href="`files/${file.filename}`">{{ file.originalName }}</a>
            <span v-else>{{ file.originalName }}</span>
          </td>
          <td>{{ getFormatFecha(file.createdAt) }}</td>
          <td>
            <span class="badge text-white" :class="statusClass(file.status)">{{ $t(file.statusText) }}</span>
          </td>
          <td>{{ getFormatFecha(file.finishedAt) }}</td>
          <td>{{ getFormatFecha(file.available_at, file) }}</td>
        </tr>

        </tbody>
      </table>
      <div class="col-12">
        <pagination
            :total-items="totalItems"
            :prop-current-page="page"
            @current-page="updatePage"
        />
      </div>
    </div>
    <Diplomas :title="$t('DIPLOMAS_GENERATE')"/>
  </div>
</template>

<style scoped lang="scss">

</style>
