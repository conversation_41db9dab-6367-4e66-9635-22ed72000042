import {make} from "vuex-pathify";
import axios from "axios";
import ZipFileTask from "../../types/ZipFileTask";

const state = {
    loading: false,
    files: [],
    pagination: {
        page: 1,
        pageSize: 10,
        totalItems: 0
    }
};

export const mutations = {
    ...make.mutations(state)
};

export const getters = {
    ...make.getters(state)
};

export const actions = {
    loadFiles({ commit, getters }) {
        commit('SET_LOADING', true);
        const { pagination } = getters;
        const url = new URL(window.location.origin + '/admin/api/v1/zip-files');
        url.searchParams.set('page', pagination.page ?? 1);
        url.searchParams.set('page_size', pagination.pageSize ?? 10);
        axios.get(url.toString()).then(r => {
            const { data, totalItems } = r.data.data;
            const elements = [];

            const now = Date.now();
            const ZOMBIE_HOURS_THRESHOLD = 24;

            data.forEach(item => {
                elements.push(new ZipFileTask(checkZombieTask(item, now, ZOMBIE_HOURS_THRESHOLD)));
            });

            commit('SET_FILES', elements);
            pagination.totalItems = totalItems;
            commit('SET_PAGINATION', pagination);
        }).finally(() => {
            commit('SET_LOADING', false);
        });
    }
};

function checkZombieTask(item, now, hoursThreshold) {
    if (item.startedAt && item.status === -2) {
        const startedAtTime = new Date(item.startedAt).getTime();
        const diffHours = (now - startedAtTime) / (1000 * 60 * 60);
        if (diffHours > hoursThreshold) {
            item.status = -1;
        }
    }
    return item;
}

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions
};
