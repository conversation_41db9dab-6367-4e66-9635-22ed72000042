<script>
import { dispatch, get } from 'vuex-pathify'
import Multiselect from "vue-multiselect";
import CategoryFilter from '../../common/components/filter/CategoryFilter.vue'
import BaseSwitch from '../../base/BaseSwitch.vue'

export default {
  name: "UsersFormView",
  components: {
    BaseSwitch,
    CategoryFilter,
    Multiselect
  },
  data() {
    return {
      loading: true,
      saving: false,
      user: {},
    }
  },
  computed: {
    useGlobalEventBus: get("contentTitleModule/getUseGlobalEventBus"),
    getUserDetails: get('UsersFormModule/getUserDetails'),
    preData: get('UsersFormModule/preData'),
    timezones() { return this.preData.timezones },
    locales() { return this.preData.locales },
    companies() { return this.preData.companies },
    roles() { return this.preData.roles },
    userId() { return this.$route?.params?.id || 0; },
  },
  async mounted() {
    await this.setTitle()
    await dispatch('UsersFormModule/loadPreData')
    this.setActionButton()
    await this.loadData()
  },
  methods: {
    async loadData() {
      await dispatch('UsersFormModule/loadUserDetails', this.userId)
      this.user = this.getUserDetails()
      this.loading = false
    },
    setActionButton() {
      this.$store.dispatch('contentTitleModule/setActions', {
        route: this.$route.name,
        actions: [{ name: this.$t('LIBRARY.SAVE'), event: 'USER_SAVE', class: 'btn btn-primary' }],
      })
      if (this.useGlobalEventBus) {
        this.$eventBus.$on('USER_SAVE', () => { this.saveData() });
      }
    },
    async setTitle() {
      await this.$store.dispatch("contentTitleModule/addRoute", {
        routeName: this.$route.name,
        params: {
          linkName: this.$t(this.userId ? 'USER.UPDATE_USER' : 'USER.CREATE_USER'),
          params: {},
        },
      });
    },
    async saveData() {
      if (this.saving) return null;
      this.saving = true;
      const errors = this.user.getErrorList()
      if (errors.length) {
        errors.forEach(errorTag => this.$t(errorTag))
      } else await dispatch('UsersFormModule/saveUserDetails', this.user.getPayload())
      this.saving = false;
    }
  }
}
</script>

<template>
<div class="UsersFormView px-4">
  <div class="row">
    <p class="col-12 title"><i class="fa fa-file-alt"/> {{ $t('ANNOUNCEMENT_OBSERVATION.BASIC_INFO') }}</p>
    <div class="col-md-4 col-sm-6 col-xs-12 avatarContainer">
      <img class="userAvatar" :src="user.avatar" :alt="user.firstName">
    </div>
    <div class="col-md-8 col-sm-6 col-xs-12">
      <div class="row">
        <div class="col-md-6 col-xs-12 pb-3">
          <label for="inputName">{{ $t('NAME') }}</label>
          <input
            id="inputName"
            v-model="user.firstName"
            required
            class="form-control"
          />
        </div>
        <div class="col-md-6 col-xs-12 pb-3">
          <label for="lastName">{{ $t('SUBSCRIPTION.LAST_NAME') }}</label>
          <input
            id="lastName"
            v-model="user.lastName"
            required
            class="form-control"
          />
        </div>
        <div class="col-md-6 col-xs-12 pb-3">
          <label for="email">{{ $t('USER.EMAIL') }}</label>
          <input
            id="email"
            v-model="user.email"
            required
            class="form-control"
          />
        </div>
        <div class="col-md-6 col-xs-12 pb-3">
          <label for="language">{{ $t('USERS.FORM.LOCALE') }}</label>
          <select
            id="language"
            v-model="user.language"
            required
            class="form-control"
          >
            <option v-for="item in locales" :key="item.key" :value="item.value">{{ item.text }}</option>
          </select>
        </div>
        <div class="col-md-6 col-xs-12 pb-3">
          <label for="code">{{ $t('CODE') }}</label>
          <input
            id="code"
            v-model="user.code"
            required
            class="form-control"
          />
        </div>
        <div class="col-md-6 col-xs-12 pb-3">
          <label for="company">{{ $t('USER.LABEL.COMPANY') }}</label>
          <select
            id="company"
            v-model="user.company"
            required
            class="form-control"
          >
            <option v-for="item in companies" :key="item.key" :value="item.value">{{ item.text }}</option>
          </select>
        </div>
        <div class="col-md-6 col-xs-12 pb-3">
          <label for="zone">{{ $t('TIMEZONE') }}</label>
          <select
            id="zone"
            v-model="user.zone"
            required
            class="form-control"
          >
            <option v-for="item in timezones" :key="item.key" :value="item.value">{{ item.text }}</option>
          </select>
        </div>
        <div class="col-md-6 col-xs-12 pb-3">
          <label for="dni">{{ $t('USER.USER_FIELDS_FUNDAE.DNI') }}</label>
          <input
            id="dni"
            v-model="user.dni"
            required
            class="form-control"
          />
        </div>
        <div class="col-md-6 col-xs-12 py-3">
          <div class="form-check pl-0 d-flex align-items-center gap-1 mb-2">
            <BaseSwitch
              v-model="user.openCampus"
              :tag="'visibleInCampus'"
            />
            <label class="form-check-label" for="visibleInCampus">
              {{ $t("USER.LABEL.OPEN_CAMPUS") }}
            </label>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <div class="row" v-if="user.extra?.length">
    <p class="col-12 title"><i class="fa fa-file-archive"/> {{ $t('ANNOUNCEMENT_OBSERVATION.EXTRA_INFO') }}</p>
    <div
      v-for="extraField in user.extra"
      class="col-md-3 col-sm-4 col-xs-12 pb-3">
      <label :for="extraField.key">{{ extraField.label }}</label>
      <input
        :id="extraField.key"
        v-model="extraField.value"
        required
        class="form-control"
      />
    </div>
  </div>
  
  <div class="row">
    <p class="col-12 title"><i class="fa fa-shield"/> {{ $t('ANNOUNCEMENT_OBSERVATION.SECURITY_INFO') }}</p>
    <div class="col-sm-6 col-xs-12 pb-3">
      <label for="roles">{{ $t('USER.ROLES.TITLE') }}</label>
      <Multiselect
        id="roles"
        v-model="user.roles"
        :multiple="true"
        :options="roles"
        :searchable="true"
        :placeholder="$t('MULTISELECT.PLACEHOLDER')"
        :selectLabel="$t('MULTISELECT.SELECT_LABEL')"
        track-by="key"
        label="value"
      />
    </div>
    <div class="col-md-4 col-sm-6 col-xs-12 pb-3">
      <label for="password">{{ $t('USER.LABEL.PASSWORD') }}</label>
      <input
        id="password"
        v-model="user.password"
        required
        class="form-control"
      />
    </div>
  </div>
  <div class="row">
    <p class="col-12 title"><i class="fa fa-tag"/> {{ $t('ANNOUNCEMENT_OBSERVATION.FILTER_INFO') }}</p>
    <div class="col-12 pb-3">
    {{ $t('USERS.FORM.FILTER1_DESCRIPTION') }}
    </div>
    <div class="col-12 pb-3">
      <CategoryFilter
        v-model="user.filters"
        :category-warning-status-text="$t('COURSE.AUDIENCE_WARNING_TEXT') + ''"
        :show-titles="true"
        :allow-all="true"
      />
    </div>
  </div>
  <div class="row" v-if="$isAdmin()">
    <p class="col-12 title"><i class="fa fa-users"/> {{ $t('ANNOUNCEMENT_OBSERVATION.MANAGEMENT_INFO') }}</p>
    <div class="col-12 pb-3">
      {{ $t('USERS.FORM.FILTER2_DESCRIPTION') }}
    </div>
    <div class="col-12 pb-3">
      <CategoryFilter
        v-model="user.management"
        :category-warning-status-text="$t('COURSE.AUDIENCE_WARNING_TEXT') + ''"
        :show-titles="true"
        :allow-all="true"
      />
    </div>
  </div>
</div>
</template>

<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>
<style scoped lang="scss">
.UsersFormView {
  padding: 2rem 1rem;
  .title {
    display: grid;
    grid-template-columns: 2rem auto;
    gap: 0.5rem;
    font-size: 1.2rem;
    margin: 1rem 0 1.5rem;
    padding-bottom: 0.5rem;
    position: relative;
    
    i {
      text-align: center;
      margin-top: 0.25rem;
    }
    
    &::before {
      content: "";
      position: absolute;
      height: 2px;
      width: calc(100% - 2rem);
      background-color: var(--color-neutral-mid);
      bottom: 0;
      left: 1rem;
    }
  }
  
  .avatarContainer {
    text-align: center;
    
    img {
      width: 200px;
      height: 200px;
      object-fit: cover;
      object-position: center;
    }
  }
}
</style>