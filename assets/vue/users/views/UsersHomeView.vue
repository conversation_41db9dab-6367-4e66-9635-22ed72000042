<script>
import { dispatch, get } from "vuex-pathify";
import Home from '../../base/Home.vue'
import { ROUTES } from '../constants/usersConstants'
import BaseSwitch from '../../base/BaseSwitch.vue'
import UserListTable from '../components/UserListTable.vue'

export default {
  name: "UsersHomeView",
  components: { UserListTable, BaseSwitch, Home },
  data() {
    return {
      filters: {
        role: '',
        dateFrom: '',
        dateTo: '',
        active: true
      },
      rolesAvailable: [],
      impersonatingUser: false
    }
  },
  computed: {
    getUserList: get('UsersHomeModule/getUserList'),
    userList() { return this.getUserList(); },
    ROUTES() {
      return ROUTES
    },
    appliedFiltersCount() {
      return Object.values(this.filters).filter((filter) => !!filter).length
    }
  },
  async mounted() {
    await this.$store.dispatch("contentTitleModule/addRoute", {
      routeName: this.$route.name,
      params: {
        linkName: this.$t("USER.LABEL_IN_PLURAL"),
        params: {},
      },
    });
    await this.loadFilters()
    await this.loadUsers()
  },
  methods: {
    async loadFilters() {
      // TODO: Load Filters
      const data = { }
      this.filters = {
        ...data,
        ...this.filters,
      }
    },
    async loadUsers() {
      await dispatch('UsersHomeModule/loadUserList')
    },
    applyFilters(filters) {},
    resetFilters(filters) {
    
    },
    toggleUserState(index) {
      this.userList.data[index].isUpdating = false
      // TODO: Update user status
    },
    async viewUserInfo(viewUrl) {
      await dispatch('UsersHomeModule/viewUserInfo', viewUrl)
    },
    async impersonateUser(userId) {
      if (this.impersonatingUser) return null;
      this.impersonatingUser = true;
      await dispatch('UsersHomeModule/impersonateUser', userId)
      this.impersonatingUser = false;
    },
    async updateUser(userId) {
      this.$router.push({ name: ROUTES.FORM, params: { id: userId } }).catch(() => {})
    },
    async deleteUser(deleteUrl) {
      this.$alertify.confirmWithTitle(
        this.$t('DELETE'),
        this.$t('ANNOUNCEMENT.DELETE_CALLED_USER.CONFIRM.TITLE'),
        async () => {
          await dispatch('UsersHomeModule/deleteUser', deleteUrl)
          await this.loadUsers()
        },
        () => {},
      );
    },
  }
}
</script>

<template>
<div class="UsersHomeView">
  <Home
    title="USER.LABEL_IN_PLURAL"
    description="USERS.HOME.DESCRIPTION"
    src-thumbnail="/assets/imgs/users_home_desc.svg"
    :allowFilters="true"
    :showFilterMessage="true"
    :numberOfFiltersApplied="appliedFiltersCount"
    @apply-filters="applyFilters"
    @clear-filters="resetFilters">
    
    <template v-slot:content-actions>
      <button class="btn btn-primary">
        <i class="fa fa-download"/> {{ $t("DOWNLOAD_REPORT") }}
      </button>
      <router-link
        :to="{ name: ROUTES.FORM, params: {} }"
        class="btn btn-primary"
      >
        <i class="fa fa-plus"/> {{ $t("USERS.HOME.NEW") }}
      </router-link>
    </template>
    
    <template v-slot:content-filters>
      <div class="row mb-3">
        <div class="col-md-4 col-sm-6 col-xs-12">
          <label>{{ $t('USERS.HOME.FILTERS.ROLE') }}</label>
          <select
            class="form-select"
            v-model="filters.role"
          >
            <option v-for="role in rolesAvailable" :key="role.key" :value="role.value">{{ role.text }}</option>
          </select>
        </div>
        
        <div class="col-md-4 col-sm-6 col-xs-12">
          <label>{{ $t('USERS.HOME.FILTERS.DATES') }}</label>
          <div class="row">
            <div class="col-6">
              <input
                type="date"
                v-model="filters.dateFrom"
                class="form-control"
              />
            </div>
            <div class="col-6">
              <input
                type="date"
                v-model="filters.dateTo"
                class="form-control"
              />
            </div>
          </div>
        </div>
        
        <div class="col-md-4 col-sm-6 col-xs-12">
          <div class="d-flex gap-3 align-items-center h-100">
            <BaseSwitch
              tag="switch-actives"
              v-model="filters.active"
              theme="light"
            />
            <label class="form-check-label" for="switch-actives">
              {{ $t("ACTIVE") }}
            </label>
          </div>
        </div>
        <div class="col-md-4 col-sm-6 col-xs-12"></div>
      </div>
    </template>
    
    <template v-slot:content-main>
      <UserListTable
        :user-list="userList.data"
        :current-page="userList.pagination.currentPage"
        :total-users="userList.pagination.totalItems"
        :loading="userList.loading"
        @update-page="loadUsers"
        @toggle-user-inactive="toggleUserState"
        @view="viewUserInfo"
        @impersonate="impersonateUser"
        @update="updateUser"
        @delete="deleteUser"
      />
    </template>
  </Home>
</div>
</template>