export default {
  data() {
    return {
      warningLocales: {},
    };
  },
  watch: {
    translations: {
      immediate: true,
      deep: true,
      handler(newTranslations) {
        if (!Array.isArray(newTranslations)) return;
        newTranslations.forEach((t) => {
          const isNameEmpty = !t.name || t.name.trim().length === 0;
          this.$set(this.warningLocales, t.locale, isNameEmpty);
        });
      },
    },
  },
};
