export default {
    namespaced: true,
    state: {
        deleteLastRoute: false,
        history: []
    },
    getters: {
        getDeleteLastRoute(state) { return state.deleteLastRoute; },
        getHistory(state) { return state.history; }
    },
    mutations: {
        SET_DELETE_LAST_ROUTE(state, deleteRoute = false) {
            state.deleteLastRoute = deleteRoute;
        },
        ADD_ROUTE_TO_HISTORY(state, route) {
            state.history.push({
                name: route.name,
                params: route.params
            });
        },
        SET_HISTORY(state, history = []) {
            state.history = history;
        },
        REMOVE_ROUTE(state, routeName) {
            state.history = state.history.filter(item => item.name !== routeName);
        }
    },
    actions: {
        setDeleteLastRoute({ commit }, deleteRoute = false) {
            commit('SET_DELETE_LAST_ROUTE', deleteRoute);
        },
        addRouteToHistory({ commit, getters }, route) {
            if (getters['getDeleteLastRoute']) {
                commit('SET_DELETE_LAST_ROUTE', false);
            } else {
                commit('ADD_ROUTE_TO_HISTORY', route);
            }
        },
        setHistory({ commit }, history = []) {
            commit('SET_HISTORY', history);
        },

        removeRoute({ commit }, routeName) {
            commit('REMOVE_ROUTE', routeName);
        }
    }
}
