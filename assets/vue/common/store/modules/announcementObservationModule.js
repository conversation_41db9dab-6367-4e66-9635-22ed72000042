import axios from "axios";
export default {
    namespaced: true,
    state: {
        loading: true,
        fileTypes: []
    },
    getters: {
        isLoading(state) { return state.loading; },
        getFileTypes(state) { return state.fileTypes; }
    },
    mutations: {
        SET_LOADING(state, loading) {
            state.loading = loading;
        },
        SET_FILE_TYPES(state, types = []) {
            state.fileTypes = types;
        }
    },
    actions: {
        async getAllowedFileTypes({ commit }) {
            const result = await axios.get('/admin/announcement-observation/allowed-file-types');
            commit('SET_FILE_TYPES', result.data.data);
        },
        async addNewObservation({ commit }, {id, formData }) {
            const headers = {
                'Content-Type': 'multipart/form-data'
            };
            const result = await axios.post(`/admin/announcement/${id}/new-observation`, formData, { headers });
            return result.data;
        },
        async updateObservation({ commit }, {id, formData }) {
            const headers = {
                'Content-Type': 'multipart/form-data'
            };
            const result = await axios.post(`/admin/announcement-observation/${id}/update`, formData, { headers });
            return result.data;
        },

        async getObservations({ commit }, id) {
            commit('SET_LOADING', true);
            try {
                const result = await axios.get(`/admin/announcement/${id}/observations`);
                return result.data;
            } finally {
                commit('SET_LOADING', false);
            }
        },

        async getObservation({ commit }, id) {
            commit('SET_LOADING', true);
            try {
                const result = await axios.get(`/admin/announcement-observation/${id}`);
                return result.data;
            } finally {
                commit('SET_LOADING', false);
            }
        },

        async getObservationFiles({ commit }, id) {
            const result = await axios.get(`/admin/announcement-observation/${id}/files`);
            return result.data;
        },

        async uploadObservationFiles({commit}, { id, formData }) {
            const headers = {
                'Content-Type': 'multipart/form-data'
            };
            const url = `/admin/announcement-observation/${id}/files`;
            const result = await axios.post(url, formData, { headers });
            return result.data;
        },

        async deleteObservationDocument({ commit }, id) {
            const url = `/admin/announcement-observation-document/${id}`;
            const result = await axios.delete(url);
            return result.data;
        },

        async deleteObservation({ commit }, id) {
            const result = await axios.delete(`/admin/announcement-observation/${id}`);
            return result.data;
        }
    }
}
