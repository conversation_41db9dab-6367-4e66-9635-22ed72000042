<script>

import $ from 'jquery'

export default {
  name: "LabelWithInfo",
  props: {
    info: {
      type: String,
      default: ''
    },

    location: {
      type: String,
      default: 'right'
    },
    id: {
      type: String|Number,
      default: ''
    }
  },
  data() {
    return {
      style: {
        width: '300px'
      }
    }
  },
  computed: {
    elementId() {
      const unq = Math.floor(Math.random() * Math.floor(Math.random() * Date.now()));
      return `${this.id}-label-${unq}`;
    }
  },
  mounted() {
    $('[data-toggle="tooltip"]').tooltip();
  }
}
</script>

<template>
  <label :id="elementId" class="LabelWithInfo">
    <slot></slot>
    <i class="fa fa-question-circle" data-toggle="tooltip" data-placement="top" data-html="true" :title="info"></i>
  </label>
</template>

<style scoped lang="scss">
.LabelWithInfo {
  position: relative;
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  z-index: 2;
  text-align: left;

  i {
    cursor: pointer;
    margin-left: .5rem;
    color: var(--color-primary);
  }
}
</style>
