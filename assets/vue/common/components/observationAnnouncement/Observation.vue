<template>
  <div class="Observation">
    <div class="Observation--content">
      <ul class="nav nav-tabs">
        <li class="nav-item" role="presentation">
          <button class="nav-link" :class="activePane === 'other-info' ? 'active' : ''" id="other-info-tab" @click="activePane = 'other-info'">
            <i class="fa fa-list"></i> {{ $t('ANNOUNCEMENT_OBSERVATION.BASIC_INFO') }}
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" :class="activePane === 'costs' ? 'active' : ''" id="costs-tab" @click="activePane = 'costs'">
            <i class="fa fa-euro-sign"></i> {{ $t('ANNOUNCEMENT_OBSERVATION.COSTS') }}
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" :class="activePane === 'observations' ? 'active' : ''" id="observations-tab" @click="activePane = 'observations'">
            <i class="fa fa-book"></i> {{ $t('ANNOUNCEMENT_OBSERVATION.OBSERVATIONS') }}
          </button>
        </li>
      </ul>

      <div class="tab-content" v-if="observation != null">
        <div class="tab-pane fade other-info" :class="activePane === 'other-info' ? 'active show' : ''">
          <div class="w-100 d-flex flex-row flex-wrap">
            <div class="col-xs-12 col-md-4 d-flex flex-column">
              <b>{{ $t('ANNOUNCEMENT_OBSERVATION.COMUNICADO_FUNDAE') }}</b>
              <label class="value">{{ $t(observation?.comunicadoFundae ? 'YES' : 'NO') }}</label>
            </div>
            <div class="col-xs-12 col-md-4 d-flex flex-column">
              <b>{{ $t('ANNOUNCEMENT_OBSERVATION.COMUNICADO_ABILITIA') }}</b>
              <label class="value">{{ $t(observation?.comunicadoAbilitia ? 'YES' : 'NO') }}</label>
            </div>
            <div class="col-xs-12 col-md-4 d-flex flex-column">
              <b>{{ $t('ANNOUNCEMENT_OBSERVATION.ECONOMIC_MODULE') }}</b>
              <label class="value">{{ observation?.economicModule }}</label>
            </div>
            <div class="col-xs-12 col-md-4 d-flex flex-column">
              <b>{{ $t('ANNOUNCEMENT_OBSERVATION.TRAVEL_AND_MAINTENANCE') }}</b>
              <label class="value">{{ observation?.travelAndMaintenance }}</label>
            </div>
            <div class="col-xs-12 col-md-4 d-flex flex-column">
              <b>{{ $t('ANNOUNCEMENT_OBSERVATION.PROVIDER_INVOICE_NUMBER') }}</b>
              <label class="value">{{ observation?.providerInvoiceNumber }}</label>
            </div>
            <div class="col-xs-12 col-md-4 d-flex flex-column">
              <b>{{ $t('ANNOUNCEMENT_OBSERVATION.HEDIMA_MANAGEMENT_INVOICE_NUMBER') }}</b>
              <label class="value">{{ observation?.hedimaManagementInvoiceNumber }}</label>
            </div>
            <div class="col-xs-12 col-md-4 d-flex flex-column">
              <b>{{ $t('ANNOUNCEMENT_OBSERVATION.INVOICE_STATUS') }}</b>
              <label class="value">{{ observation?.invoiceStatus }}</label>
            </div>
            <div class="col-xs-12 col-md-4 d-flex flex-column">
              <b>{{ $t('ANNOUNCEMENT_OBSERVATION.COURSE_STATUS') }}</b>
              <label class="value">{{ observation?.courseStatus }}</label>
            </div>
          </div>
        </div>
        <div class="tab-pane fade costs" :class="activePane === 'costs' ? 'active show' : ''">
          <div class="w-100 d-flex flex-row flex-wrap">
            <div class="col-xs-12 col-md-4 d-flex flex-column">
              <b>{{ $t('ANNOUNCEMENT_OBSERVATION.PROVIDER_COST') }}</b>
              <label class="value">{{ observation?.providerCost }}</label>
            </div>
            <div class="col-xs-12 col-md-4 d-flex flex-column">
              <b>{{ $t('ANNOUNCEMENT_OBSERVATION.HEDIMA_MANAGEMENT_COST') }}</b>
              <label class="value">{{ observation?.hedimaManagementCost }}</label>
            </div>
            <div class="col-xs-12 col-md-4 d-flex flex-column">
              <b>{{ $t('ANNOUNCEMENT_OBSERVATION.TRAVEL_AND_MAINTENANCE_COST') }}</b>
              <label class="value">{{ observation?.travelAndMaintenanceCost }}</label>
            </div>
            <div class="col-xs-12 col-md-4 d-flex flex-column">
              <b>{{ $t('ANNOUNCEMENT_OBSERVATION.TOTAL_COST') }}</b>
              <label class="value">{{ observation?.totalCost }}</label>
            </div>
            <div class="col-xs-12 col-md-4 d-flex flex-column">
              <b>{{ $t('ANNOUNCEMENT_OBSERVATION.FINAL_PAX') }}</b>
              <label class="value">{{ observation?.finalPax }}</label>
            </div>
            <div class="col-xs-12 col-md-4 d-flex flex-column">
              <b>{{ $t('ANNOUNCEMENT_OBSERVATION.MAXIMUM_BONUS') }}</b>
              <label class="value">{{ observation?.maximumBonus }}</label>
            </div>
            <div class="col-xs-12 col-md-4 d-flex flex-column">
              <b>{{ $t('ANNOUNCEMENT_OBSERVATION.SUBSIDIZED_AMOUNT') }}</b>
              <label class="value">{{ observation?.subsidizedAmount }}</label>
            </div>
            <div class="col-xs-12 col-md-4 d-flex flex-column">
              <b>{{ $t('ANNOUNCEMENT_OBSERVATION.PRIVATE_AMOUNT') }}</b>
              <label class="value">{{ observation?.privateAmount }}</label>
            </div>
          </div>
        </div>
        <div class="tab-pane fade observations" :class="activePane === 'observations' ? 'active show' : ''">
          <div v-html="observation.observations"></div>
        </div>
      </div>
    </div>
    <div class="Observation--files">
      <div class="w-100 d-flex align-items-center justify-content-end">
        <button class="btn btn-primary" @click="openNewFileModal()">{{ $t('FILE_UPLOAD.UPLOAD_FILES') }}</button>
      </div>
      <div v-if="loadingFiles" class="d-flex align-items-center justify-content-center">
        <spinner />
      </div>
      <table class="table mt-1" v-else>
        <thead>
        <tr>
          <td>{{ $t('FILE') }}</td>
          <td class="text-center">{{ $t('FILE_UPLOAD.FILE_TYPE') }}</td>
          <td class="text-center">{{ $t('CREATED_AT') }}</td>
          <td class="text-right">{{ $t('ACTIONS') }}</td>
        </tr>
        </thead>
        <tbody>
        <tr v-for="file in files" :key="file.id">
          <td>{{ file.originalName }}</td>
          <td class="text-center text-nowrap">{{ file.mimeType }}</td>
          <td class="text-center text-nowrap">{{ file.createdAt }}</td>
          <td class="text-right">
            <button type="button" class="btn btn-danger btn-sm" @click="deleteFile(file)"><i class="fa fa-trash"></i></button>
            <button type="button" class="btn btn-info btn-sm" @click="openFileId = file.id"><i class="fa fa-eye"></i></button>
            <viewer base-path="uploads/files/announcement_observation"
                    :file="file"
                    :custom="false"
                    :open="openFileId === file.id"
                    @close="openFileId = -1"
            />
          </td>
        </tr>
        </tbody>
      </table>
    </div>
    <div class="modal fade" id="observationFileModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="observationFileModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="observationFileModalLabel">{{ $t('FILE_UPLOAD.UPLOAD_FILES') }}</h5>
            <button type="button" class="btn-close btn-close-white" @click="closeFileModal()" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <upload-observation-files :id="$route.params.id" @success="onSuccess()" @cancel="closeFileModal()"/>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Spinner from "../../../admin/components/base/Spinner.vue";
import FormFileUploader from "../../../common/components/FormFileUploader.vue";
import UploadObservationFiles from "./UploadObservationFiles.vue";

import $ from "jquery";
import Viewer from "../viewer/Viewer.vue";


export default {
  name: "Observation",
  components: {Viewer, UploadObservationFiles, FormFileUploader, Spinner},
  data() {
    return {
      activePane: 'other-info',
      observation: null,

      loadingFiles: true,
      files: [],

      openFileId: -1
    };
  },
  computed: {
    useGlobalEventBus() {
      return this.$store.getters['contentTitleModule/getUseGlobalEventBus'];
    }
  },
  created() {
    this.$store.dispatch('contentTitleModule/setActions', {
      route: this.$route.name, actions: [
        {
          name: this.$t('DELETE'),
          event: 'delete',
          class: 'btn btn-danger',
          icon: 'fa fa-trash'
        }, {
          name: this.$t('EDIT'),
          event: 'edit',
          class: 'btn btn-info',
          icon: 'fa fa-pencil'
        },
      ]
    });

    this.getObservation();
    this.$store.dispatch('announcementObservationModule/getAllowedFileTypes');

  },
  mounted() {
    if (this.useGlobalEventBus) {
      this.$eventBus.$on('edit', () => {
        this.$router.replace({ name: 'UpdateObservation', params: {id: this.$route.params.id }});
      });
      this.$eventBus.$on('delete', () => {
        this.deleteObservation();
      });
    }
  },
  beforeDestroy() {
    if (this.useGlobalEventBus) {
      this.$eventBus.$off('edit');
      this.$eventBus.$off('delete');
    }
  },
  methods: {
    async getObservation() {
      const { data, error } = await this.$store.dispatch('announcementObservationModule/getObservation', this.$route.params.id);
      this.observation = data;
      this.loadFiles();
    },
    loadFiles() {
      this.loadingFiles = true;
      this.$store.dispatch('announcementObservationModule/getObservationFiles', this.$route.params.id).then(res => {
        const { data, error } = res;
        this.files = data;
      }).finally(() => {
        this.loadingFiles = false;
      })
    },
    deleteFile(file) {
      this.$alertify.confirmWithTitle(
          this.$t('FILE_UPLOAD.CONFIRM_DELETE.TITLE'),
          this.$t('FILE_UPLOAD.CONFIRM_DELETE.DESCRIPTION'),
          () => {
            this.$store.dispatch('announcementObservationModule/deleteObservationDocument', file.id).then(res => {
              const { error } = res;
              if (error) {
                this.$toast.error(this.$t('FILE_UPLOAD.DELETE.FAILED', [file.originalName]) + '');
              } else {
                this.$toast.success(this.$t('FILE_UPLOAD.DELETE.SUCCESS', [file.originalName]) + '');
                const index = this.files.findIndex(item => item.id === file.id);
                if (index >= 0) {
                  this.files.splice(index, 1);
                }
              }
            })
          },
          () => {},
      )
    },

  /*   openNewFileModal() {
      $('#observationFileModal').modal({
        show  : true,
        static  : true,
        backdrop: false,
        keyboard: false
      });
    },
 */
    openNewFileModal(){
      const modal = document.getElementById("observationFileModal");
      modal.classList.add("show");
      modal.style.display = 'block';

      modal.setAttribute("aria-modal", "true");
      modal.setAttribute('role', 'dialog');
      modal.removeAttribute('aria-hidden');
      modal.setAttribute('style', 'display: block; padding-right: 17px;');
    },

    closeFileModal() {
      const modal = document.getElementById("observationFileModal");
      modal.classList.remove("show");
      modal.style.display = 'none';

      modal.setAttribute("aria-modal", "false");
      modal.setAttribute('role', 'dialog');
      modal.setAttribute('aria-hidden', 'true');
      modal.setAttribute('style', 'display: none; padding-right: 17px;');
    },


   /*  closeFileModal() {
      $('#observationFileModal').modal('hide');
    }, */

    onSuccess() {
      this.closeFileModal();
      this.loadFiles();
    },

    deleteObservation() {
      this.$alertify.confirmWithTitle(
          this.$t('ANNOUNCEMENT_OBSERVATION.CONFIRM_DELETE.TITLE'),
          this.$t('ANNOUNCEMENT_OBSERVATION.CONFIRM_DELETE.DESCRIPTION'),
          () => {
            this.$store.dispatch('announcementObservationModule/deleteObservation', this.$route.params.id).then(res => {
              const { error } = res;
              if (error) {
                this.$toast.error(this.$t('ANNOUNCEMENT_OBSERVATION.DELETE.FAILED') + '');
              } else {
                this.$toast.success(this.$t('ANNOUNCEMENT_OBSERVATION.DELETE.SUCCESS') + '');
                if (this.useGlobalEventBus) {
                  this.$store.dispatch('routerModule/setDeleteLastRoute', true);
                  this.$eventBus.$emit('go-back');
                }
              }
            })
          },
          () => {},
      );
    }
  }
}
</script>

 <style scoped lang="scss"> 
.Observation {
  padding-top: 0.5rem;
  &--content {
    @include nav-bar-style;
  }

  &--files {
    border: 1px solid $base-border-color;
    background-color: #FFFFFF;
    padding: 0.5rem 3rem 0.25rem 3rem;
  }
}
</style>
