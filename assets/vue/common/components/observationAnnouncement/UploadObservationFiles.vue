<template>
  <div class="UploadObservationFiles">
    <form-file-uploader
        id="form-upload-observation-files"
        :file-types="fileTypes"
        :uploading="uploading"
        :show-cancel="true"
        :show-submit="true"
        @on-type-selection="selectedType = $event"
        @cancel="$emit('cancel')"
        @upload="upload"
    >
    </form-file-uploader>
  </div>
</template>

<script>
import FormFileUploader from "../../../common/components/FormFileUploader.vue";

export default {
  name: "UploadObservationFiles",
  components: {FormFileUploader},
  props: {
    id: {
      type: Number|String,
      required: true
    }
  },
  data() {
    return {
      uploading: false,
      selectedType: null
    };
  },
  computed: {
    fileTypes() {
      return this.$store.getters['announcementObservationModule/getFileTypes'];
    },
  },
  methods: {
    upload(formData) {
      this.$alertify.confirmWithTitle(
          this.$t('FILE_UPLOAD.CONFIRM_UPLOAD.TITLE'),
          this.$t('FILE_UPLOAD.CONFIRM_UPLOAD.DESCRIPTION'),
          () => {
            this.uploading = true;
            this.$store.dispatch('announcementObservationModule/uploadObservationFiles', { id: this.id, formData}).then(res => {
              const { data, error } = res;
              if (error) {
                this.$toast.error(this.$t('FILE_UPLOAD.UPLOAD.FAILED') + '');
              } else {
                this.$toast.success(this.$t('FILE_UPLOAD.UPLOAD.SUCCESS') + '');
                this.$emit('success');
              }
            }).finally(() => this.uploading = false);
          },
          () => {},
      );
    }
  }
}
</script>

 <style scoped lang="scss"> 

</style>
