<script>
import {get} from "vuex-pathify";

export default {
  name: "ChatG<PERSON>",
  props: {
    group: {
      type: Object|Array,
      default: () => ({
        id: null,
        parentId: null,
        name: '',
        type: null
      })
    },
    modal: {
      type: Boolean,
      default: false
    },
    selectType: {
      type: Boolean,
      default: false
    },
    users: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      userList: {},
      loadingUsers: false,
      searchByName: ''
    }
  },
  computed: {
    chatChannelTypes: get('baseChatModule/chatChannelTypes'),
    userListFiltered() {
      if (!this.searchByName) return this.userList;
      const search = this.searchByName.toLowerCase()
      return Object.keys(this.userList)
        .filter((key) => this.userList[key].name.toLowerCase().includes(search))
        .map((key) => this.userList[key])
    }
  },
  mounted() {
    this.userList = this.users.reduce((acc, user) => ({ ...acc, [`user_${user.id}`]: { name: user.name, id: user.id, checked: false, saving: false } }), {});
    if (this.group.channelId) this.getChannelUsers()
  },
  methods: {
    getChannelUsers() {
      this.loadingUsers = true
      this.$store.dispatch('baseChatModule/getChannelUsers', { channelId: this.group.channelId }).then(r => {
        const { error, data } = r;
        if (!error) data.forEach((user) => { this.setCheckValue(user.id, true) })
        this.loadingUsers = false
      })
    },
    
    setCheckValue(userId, value) {
      if (this.userList[`user_${userId}`])
        this.userList[`user_${userId}`].checked = value;
    },
    
    toggleUserCheck(userId) {
      if (!this.group.channelId) return;
      if (!this.userList[`user_${userId}`] || this.userList[`user_${userId}`].saving) return;
      if (!this.userList[`user_${userId}`].checked) this.addUserToGroup(userId);
      else this.removeUserFromGroup(userId)
    },
    
    addUserToGroup(userId) {
      this.userList[`user_${userId}`].saving = true;
      this.$store.dispatch('baseChatModule/addUserToChannel', {
        channelId: this.group.channelId,
        userId
      }).then(r => {
        const { error } = r;
        if (!error) this.setCheckValue(userId, true)
      }).finally(() => {
        this.userList[`user_${userId}`].saving = false;
      })
    },
    
    removeUserFromGroup(userId) {
      this.$alertify.confirmWithTitle(
        this.$t('WARNING'),
        this.$t('GLOBAL_CHAT.DELETE_USER_FROM_GROUP'),
        () => {
          this.userList[`user_${userId}`].saving = true;
          this.$store.dispatch('baseChatModule/deleteUserFromChannel', {
            channelId: this.group.channelId,
            userId
          }).then(r => {
            const { error } = r;
            if (!error) this.setCheckValue(userId, false)
          }).finally(() => {
            this.userList[`user_${userId}`].saving = false;
          })
        },
      )
    },
  }
}
</script>

<template>
  <div class="ChatGroup modal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h6 class="modal-title">[{{ $t(group.channelId ? 'LIBRARY.EDIT' : 'COMMON.CREATE') }}] {{ $t('GLOBAL_CHAT.GROUP_TITLE') }}</h6>
          <button type="button" class="close" aria-label="Close" @click="$emit('close')">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="body-content form-group bg-gray my-0 subgroupName">
            <div class="subgroupInput">
              <label for="ChatGroup--name">{{ $t('GLOBAL_CHAT.MODAL_INPUT') }}</label>
              <input
                type="text"
                name="name"
                id="ChatGroup--name"
                class="form-control"
                v-model="group.name"
                :placeholder="$t('GLOBAL_CHAT.MODAL_INPUT_PLACEHOLDER')">
            </div>
            
            <button
              @click="$emit('save')"
              class="btn btn-md btn-primary"
              type="button">
              {{ $t('LIBRARY.SAVE') }}
            </button>
          </div>
          <div class="body-content" :class="{ isPreview: !group.channelId}">
            <p class="text-center" v-if="loadingUsers"><i class="fa fa-spinner"/> {{ $t("LOADING") }}...</p>
            <div v-else>
              <div class="search">
                <i class="fa fa-search icon"/>
                <input
                  type="text"
                  name="name"
                  id="ChatGroup--people-name"
                  class="form-control"
                  v-model="searchByName"
                  :disabled="!group.channelId"
                  :placeholder="$t('ITINERARY.USERS.SEARCH_USER')">
              </div>
              <div class="table-responsive">
                <table class="table table-condensed">
                  <thead>
                  <tr>
                    <th><b>{{ $t("ANNOUNCEMENT.PEOPLE") }}</b></th>
                    <th class="text-center"><b>{{ $t("GLOBAL_CHAT.MODAL_ADD") }}</b></th>
                  </tr>
                  </thead>
                  <tbody>
                  <tr v-for="user in userListFiltered" :key="'user_' + user.id">
                    <td>{{ user.name }}</td>
                    <td class="text-center">
                      <i
                        class="fa fa-check-square cursor-pointer"
                        :class="user.checked ? 'text-primary' : 'fa-stop text-gray'"
                        @click="toggleUserCheck(user.id)"
                      ></i>
                    </td>
                  </tr>
                  </tbody>
                </table>
              </div>
            </div>
            </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.ChatGroup {
  display: initial !important;
  background-color: rgba(0,0,0,0.3);
  
  .close {
    color: white;
    opacity: 1;
  }
  
  .modal-body {
    padding: 0 !important;
  }
  
  .body-content {
    padding: 1rem !important;
    
    &.subgroupName {
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      gap: 1rem;
      
      .subgroupInput {
        flex: 1;
      }
    }
    
    &.isPreview {
      opacity: 0.3;
    }
  }
  
  .table-responsive {
    max-height: 500px;
    overflow-y: auto;
  }
  
  td {
    word-break: break-all;
    white-space: pre-wrap;
  }
  
  .search {
    width: clamp(300px, 100%, 500px);
    margin: 0 0 1rem;
    position: relative;
    
    input {
      padding: 0 0 0 2rem;
    }
    
    .icon {
      position: absolute;
      color: var(--color-neutral-mid-dark);
      top: 0.65rem;
      left: 0.5rem;
    }
  }
}
</style>
