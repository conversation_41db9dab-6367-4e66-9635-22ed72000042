<template>
  <div class="AddTaskCourseFile">
    <form-file-uploader
        id="form-upload-task-files"
        :file-types="fileTypes"
        :uploading="uploadingFiles"
        :show-cancel="true"
        :show-submit="true"
        @on-type-selection="selectedType = $event"
        @cancel="$emit('cancel')"
        @upload="upload"
    >
    </form-file-uploader>
  </div>
</template>

<script>
import FormFileUploader from "../FormFileUploader.vue";

export default {
  name: "AddTaskCourseFile",
  components: {FormFileUploader},
  props: {
    id: {
      type: Number|String,
      required: true
    }
  },
  data() {
    return {
      uploadingFiles: false,
      selectedType: undefined
    };
  },

  computed: {
    fileTypes() {
      return this.$store.getters['taskCourseModule/getAllowedFileTypes'];
    }
  },

  methods: {
    upload(formData) {
      this.$alertify.confirmWithTitle(
          this.$t('FILE_UPLOAD.CONFIRM_UPLOAD.TITLE'),
          this.$t('FILE_UPLOAD.CONFIRM_UPLOAD.DESCRIPTION'),
          () => {
            this.uploadingFiles = true;
            this.$store.dispatch('taskCourseModule/uploadTaskCourseFiles', { id: this.id, formData }).then(res => {
              const { error } = res;
              if (error) {
                this.$toast.error(this.$t('FILE_UPLOAD.UPLOAD.FAILED') + '');
              } else {
                this.$toast.success(this.$t('FILE_UPLOAD.UPLOAD.SUCCESS') + '');
                this.$emit('success');
              }
            }).finally(() => {
              this.uploadingFiles = false;
            })
          },
          () => {}
      );
    }
  }
}
</script>

 <style scoped lang="scss"> 
.AddTaskCourseFile {

}
</style>
