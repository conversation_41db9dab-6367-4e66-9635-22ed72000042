<template>
  <div class="TaskCourseHistory">
    <div class="d-flex align-items-center justify-content-center" v-if="isLoading">
      <loader :is-loaded="isLoading"/>
    </div>
    <div class="table-container table-responsive" v-else>
      <table class="table">
        <thead>
        <tr>
          <th class="text-center">{{ $t('AVATAR') }}</th>
          <th>{{ $t('NAME') }}</th>
          <th class="text-center">{{ $t('SENT') }}</th>
          <th class="text-center">{{ $t('STATUS') }}</th>
          <th class="text-center">{{ $t('ACTIONS') }}</th>
        </tr>
        </thead>
        <tbody>
        <tr v-for="history in taskHistory" :key="history.id">
          <td>
            <div class="avatar" :style="{
              'background-image': `url(/uploads/users/avatars/${history.avatar ?? 'default.svg'})`
            }"></div>
          </td>
          <td>{{ history.firstName }} {{ history.lastName }}</td>
          <td class="text-center">{{ history.createdAt }}</td>
          <td class="text-center">{{ $t(historyDeliveryTaskStates[history.state + '']) }}</td>
          <td class="text-center">
            <button class="btn btn-sm btn-info" @click="showTaskHistoryItem(history)"><i class="fa fa-eye"></i></button>
          </td>
        </tr>
        </tbody>
      </table>
    </div>
    <div>
      <pagination
          :total-items="totalItems"
          :prop-current-page="currentPage"
          @current-page="onPageChange" />
    </div>

    <div class="modal fade" tabindex="-1"
         id="task-course-history-item-modal"
         aria-labelledby="task-course-history-item-modal"
         aria-hidden="true">
      <div class="modal-dialog modal-xl">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">
            {{ modalHistory?.firstName }} {{ modalHistory?.lastName }}
          </h5>
          <button
              type="button"
              class="btn-close close"
              data-bs-dismiss="modal"
              aria-label="Close"
          ></button>
        </div>
        <div class="modal-content">
          <task-course-history-item
              :history-delivery-task-id="modalHistoryDeliveryTaskId"
              :history-delivery-item="modalHistory"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import $ from 'jquery';
import 'bootstrap';
import Loader from "../../../admin/components/Loader.vue";
import Pagination from "../../../admin/components/Pagination.vue";
import TaskCourseHistoryItem from "./TaskCourseHistoryItem.vue";

export default {
  name: "TaskCourseHistory",
  components: {TaskCourseHistoryItem, Pagination, Loader},
  $,
  props: {
    taskCourseId: {
      type: Number|String,
      required: true
    }
  },
  data() {
    return {
      isLoading: false,
      taskHistory: [],
      pageSize: 10,
      totalItems: 0,
      currentPage: 1,
      modalHistoryDeliveryTaskId: 0,
      modalHistory: null
    };
  },
  computed: {
    historyDeliveryTaskStates() {
      return this.$store.getters['taskCourseModule/getHistoryDeliveryTaskStates'];
    }
  },
  created() {
    this.loadHistory();
  },
  methods: {
    onPageChange(page) {
      this.currentPage = page;
      this.loadHistory();
    },
    loadHistory() {
        this.isLoading = true;
      this.$store.dispatch('taskCourseModule/loadTaskCourseHistory', { id: this.taskCourseId, page: this.currentPage }).then(res => {
        const { data, error } = res;
        this.taskHistory = data.items;
        this.totalItems = data['total-items'];
      }).finally(() => {
        this.isLoading = false;
      })
    },
    showTaskHistoryItem(history) {
      this.modalHistoryDeliveryTaskId = history.historyDeliveryTaskId;
      this.modalHistory = history;
      $('#task-course-history-item-modal').modal('show');
    }
  }
}
</script>

 <style scoped lang="scss"> 
.TaskCourseHistory {
  .avatar {
    @include avatar;
  }
}
</style>
