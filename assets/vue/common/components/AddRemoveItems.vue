<template>
  <div class="AddRemoveItems" :class="allowAddRemoveAll ? 'all' : ''">
    <h1 class="AddRemoveItems--title" v-if="showTitle">
      {{ title }}
    </h1>
    <div class="AddRemoveItems--src">
      <div class="query-container">
        <input
          type="text"
          class="form-control"
          :placeholder="placeholderSrc"
          v-model="srcQuery"
        />
      </div>
      <div class="content">
        <div
          class="item card"
          v-for="(item, index) in availableFiltered"
          :key="idFieldName ? item['' + idFieldName] : index"
        >
          <span class="info">
            {{ item["" + viewFieldName] }}
          </span>
          <span class="action">
            <button type="button" class="btn btn-primary" @click="add(item)">
              <i class="fa fa-plus"></i>
            </button>
          </span>
        </div>
      </div>
    </div>
    <div class="AddRemoveItems--action-all" v-if="allowAddRemoveAll">
      <button type="button" class="btn btn-primary" @click="addAll()">
        <i class="fa fa-angle-double-right"></i>
      </button>
      <button type="button" class="btn btn-danger" @click="removeAll()">
        <i class="fa fa-angle-double-left"></i>
      </button>
    </div>
    <div class="AddRemoveItems--dst">
      <div class="query-container">
        <input
          type="text"
          class="form-control"
          :placeholder="placeholderDst"
          v-model="dstQuery"
        />
      </div>
      <div class="content">
        <div class="item card" v-for="item in selectedFiltered" :key="item.id">
          <span class="info">
            {{ item["" + viewFieldName] }}
          </span>
          <span class="action">
            <button type="button" class="btn btn-danger" @click="remove(item)">
              <i class="fa fa-minus"></i>
            </button>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios";
export default {
  name: "AddRemoveItems",
  props: {
    showTitle: {
      type: Boolean,
      default: true,
    },
    title: {
      type: String,
      default: "Title",
    },
    items: {
      type: Object | Array,
      default() {
        return [];
      },
    },
    selectedItemsProp: {
      type: Object | Array,
      default() {
        return [];
      },
    }, // Only when realtime is false

    realtime: {
      type: Boolean,
      default: true,
    },
    restMethod: {
      type: Boolean,
      default: true,
    },

    allowAddRemoveAll: {
      type: Boolean,
      default: false,
    },

    urlAdd: {
      type: String,
      default: null,
    },
    urlAddAll: {
      type: String,
      default: null,
    },

    urlRemove: {
      type: String,
      default: null,
    },
    urlRemoveAll: {
      type: String,
      default: null,
    },

    idFieldName: {
      type: String,
      default: "id",
    },
    viewFieldName: {
      type: String,
      default: "name",
    },

    placeholderSrc: {
      type: String,
      default: "Introducir búsqueda",
    },

    placeholderDst: {
      type: String,
      default: "Introducir búsqueda",
    },
  },
  data() {
    return {
      selected: [],
      srcQuery: "",
      dstQuery: "",
    };
  },

  watch: {
    selectedItemsProp() {
      this.selected = this.selectedItemsProp;
    },
  },

  created() {
    if (!this.realtime) {
      this.selected = this.selectedItemsProp;
    }
  },
  computed: {
    available() {
      if (this.selected && this.selected.length > 0) {
        return this.items.filter((item) => {
          const index = this.selected.findIndex((selected) => {
            if (this.idFieldName) {
              return (
                item["" + this.idFieldName] === selected["" + this.idFieldName]
              );
            }
            return selected === item;
          });
          return index < 0;
        });
      } else {
        return this.items;
      }
    },

    availableFiltered() {
      const available = this.available;
      if (this.srcQuery.length > 0) {
        const query = this.normalize(this.srcQuery);
        return available.filter((item) => {
          if (this.viewFieldName && this.viewFieldName.length > 0) {
            return this.normalize(item["" + this.viewFieldName]).includes(
              query
            );
          } else {
            return this.normalize(item).includes(query);
          }
        });
      }
      return available;
    },

    selectedFiltered() {
      if (this.dstQuery.length > 0) {
        const query = this.normalize(this.dstQuery);
        return this.selected.filter((item) => {
          if (this.viewFieldName && this.viewFieldName.length > 0) {
            return this.normalize(item["" + this.viewFieldName]).includes(
              query
            );
          } else {
            return this.normalize(item).includes(query);
          }
        });
      }
      return this.selected;
    },
  },
  methods: {
    add(item) {
      if (this.realtime) {
        if (this.urlAdd == null || this.urlAdd.length <= 0) {
          this.$toast.error(this.$t("URL_ADD_NOT_PROVIDED") + "");
          return;
        }

        function executeRequest(restMethod, url, item, idFieldName) {
          if (restMethod)
            return axios.post(
              `${url}/${idFieldName ? item["" + idFieldName] : item}`
            );
          else return axios.post(url, item);
        }

        executeRequest(this.restMethod, this.urlAdd, item, this.idFieldName)
          .then((res) => {
            const { data, error } = res.data;
            if (error) {
              this.$toast.error(data);
            } else {
              this.selected.push(item);
              this.$toast.success(data);
            }
          })
          .catch(() => {
            this.$toast.error("Failed to add element");
          })
          .finally(() => {});
      } else {
        // Emit added elements
        this.selected.push(item);
        this.emitSelectedElements();
      }
    },

    remove(item) {
      if (this.realtime) {
        if (this.urlRemove == null || this.urlRemove.length <= 0) {
          this.$toast.error(this.$t("URL_REMOVE_NOT_PROVIDED") + "");
          return;
        }

        function executeRequest(restMethod, url, item, idFieldName) {
          if (restMethod)
            return axios.delete(
              `${url}/${idFieldName ? item["" + idFieldName] : item}`
            );
          else return axios.post(url, item);
        }

        executeRequest(this.restMethod, this.urlRemove, item, this.idFieldName)
          .then((res) => {
            const { data, error } = res.data;
            if (error) {
              this.$toast.error(data);
            } else {
              this.removeItemFromSelected(item);
              this.$toast.success(data);
            }
          })
          .catch(() => {
            this.$toast.error("Failed to add element");
          })
          .finally(() => {});
      } else {
        this.removeItemFromSelected(item);
        this.emitSelectedElements();
      }
    },

    removeItemFromSelected(item) {
      const index = this.selected.findIndex((selected) => {
        if (this.idFieldName) {
          return (
            item["" + this.idFieldName] === selected["" + this.idFieldName]
          );
        }
        return selected === item;
      });
      if (index >= 0) this.selected.splice(index, 1);
    },

    addAll() {
      const items = structuredClone(this.availableFiltered);
      if (this.realtime) {
        if (!this.urlAddAll || this.urlAddAll.length < 0) {
          this.$toast.error(this.$t("URL_ADD_ALL_NOT_PROVIDED") + "");
          return;
        }
        axios
          .post(this.urlAddAll, items)
          .then((res) => {
            const { error, data } = res.data;
            if (error) {
              this.$toast.error(data);
            } else {
              items.forEach((item) => {
                this.selected.push(item);
              });
              this.$toast.success(data);
            }
          })
          .catch((error) => {
            this.$toast.error(
              "Exception raised when trying to add all elements"
            );
          });
      } else {
        items.forEach((item) => {
          this.selected.push(item);
        });
        this.emitSelectedElements();
      }
    },

    removeAll() {
      const items = structuredClone(this.selectedFiltered);
      if (items.length < 1) return;
      if (this.realtime) {
        if (!this.urlRemoveAll || this.urlRemoveAll.length < 0) {
          this.$toast.error(this.$t("URL_REMOVE_ALL_NOT_PROVIDED") + "");
          return;
        }
        axios
          .post(this.urlRemoveAll, items)
          .then((res) => {
            const { error, data } = res.data;
            if (error) {
              this.$toast.error(data);
            } else {
              items.forEach((item) => {
                this.removeItemFromSelected(item);
              });
              this.$toast.success(data);
            }
          })
          .catch((error) => {
            this.$toast.error(
              "Exception raised when trying to remove all elements"
            );
          });
      } else {
        items.forEach((item) => {
          this.removeItemFromSelected(item);
        });
        this.emitSelectedElements();
      }
    },

    emitSelectedElements() {
      this.$emit("on-updated", this.selected);
    },

    normalize(string) {
      return string
        .toLowerCase()
        .normalize("NFD")
        .replace(/[\u0300-\u036f]/g, "");
    },
  },
};
</script>

 <style scoped lang="scss"> 
.AddRemoveItems {
  width: 100%;
  display: grid;
  grid-template-columns: auto auto;
  gap: 1rem;

  &--title {
    width: 100%;
    font-size: 18px;
    text-align: center;
    grid-column-start: 1;
    grid-column-end: 3;
  }

  &.all {
    grid-template-columns: 45% auto 45%;

    .AddRemoveItems--title {
      grid-column-start: 1;
      grid-column-end: 4;
    }
  }

  &--src {
    border: 1px solid red;
  }
  &--action-all {
    display: flex;
    flex-flow: column;
    align-items: center;
    justify-content: center;

    button {
      margin-bottom: 0.15rem;
    }
  }
  &--dst {
    border: 1px solid blue;
  }

  &--src,
  &--dst {
    display: flex;
    flex-flow: column;
    padding: 0.5rem;
    height: 250px;
    overflow-y: auto;

    background: var(--color-neutral-lighter);
    border: 1px solid var(--color-neutral-mid-light);
    border-radius: 5px;

    .query-container {
      width: 100%;
      padding: 12px 0;
    }

    .content {
      display: flex;
      flex-flow: column;
      width: 100%;
      .item {
        display: flex;
        flex-flow: row nowrap;
        padding: 0.25rem;
        align-items: center;
        margin-bottom: 0.25rem;

        .info {
          flex-grow: 1;
        }
      }
    }
  }
}
</style>
