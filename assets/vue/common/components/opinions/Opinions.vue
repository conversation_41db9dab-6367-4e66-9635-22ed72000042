<template>
  <div class="Opinions">
    <opinion
      v-for="(opinion, key) in opinions"
      :key="key"
      :opinion="opinion"
      @on-opinion-visibility-change="$emit('on-opinion-visibility-change', $event)"
      @on-opinion-highlight-change="$emit('on-opinion-highlight-change', $event)"
      @open-details="$emit('open-details', $event)"
    />
  </div>
</template>

<script>
import Opinion from "./Opinion.vue";

export default {
  name: "Opinions",
  components: {Opinion},
  props: {
    opinions: {
      type: Object|Array,
      default: function () {
        return [];
      },
    },
  }
}
</script>

 <style scoped lang="scss"> 
.Opinions {
  display: grid;
  max-width: 1300px;
  margin-inline: auto;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1rem;
}
</style>
