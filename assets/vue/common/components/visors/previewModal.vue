<template>
<div class="previewModal">
  <div
      class="modal fade"
      :id="id"
      tabindex="-1"
      :aria-labelledby="id"
      aria-hidden="true"
  >
    <div class="modal-dialog modal-xl">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" :id="id + '_label'">
            {{ fileName }}
          </h5>
          <button
              type="button"
              class="btn-close btn-close-white"
              data-bs-dismiss="modal"
              aria-label="Close"
          ></button>
        </div>
        <div class="modal-body body-visor">
          <div v-if="fileType === 'compresed'" class="buttonContiner">
            <a
               :href="urlFile"
               :download="fileName"
               target="_blank"
               class="btn btn-sm btn-primary">
              {{ $t('ANNOUNCEMENT.INFOTAB.DOWNLOAD_TEXT') }}
            </a>
          </div>
          <component
              v-else
              :is="fileVisor"
              :name="urlFile"
              :base="base"
              :key="'file_' + file.id"
              identifierVideo="undefined"
              urlMaterial="undefined"
              codeVideo="undefined"
              :autoplay="false"
          ></component>
        </div>
      </div>
    </div>
  </div>
</div>
</template>

<script>

import VisorPdf       from "../../../admin/components/material-course/visors/visorPdf";
import VisorOffice    from "../../../admin/components/material-course/visors/visorOffice";
import VisorImagen    from "../../../admin/components/material-course/visors/visorImagen";
import VisorTxt       from "../../../admin/components/material-course/visors/visorTxt";
import VisorVideo     from "../../../admin/components/material-course/visors/visorVideo";
export default {
  name: "previewModal",
  components: {VisorVideo, VisorTxt, VisorImagen, VisorOffice, VisorPdf},
  props: {
    name: {
      type: String,
      default: ''
    },
    file: {
      type: Object,
      default: () => ({})
    },
    base: {
      type: String,
      default: ''
    }
  },
  computed: {
    id() {
      return `previewModal_${this.name}`
    },
    urlFile() {
      const domain = window.location.origin;
      return `${this.base}/${this.file.urlFile}`;
    },
    fileName() {
      const { fileOriginalName } = this.file;
      return fileOriginalName || 'File';
    },
    fileType() {
      if (this.existInMimeType('application/')) {
        if (this.existInMimeType('pdf')) return 'pdf';
        if (
            this.existInMimeType('openxmlformats-officedocument') ||
            this.existInMimeType('msword') ||
            this.existInMimeType('vnd.ms-powerpoint') ||
            this.existInMimeType('vnd.ms-excel')
        ) return 'office';
      }
      if (this.existInMimeType('image/')) return 'image';
      if (this.existInMimeType('video/')) return 'video';
      if (this.existInMimeType('text/')) return 'txt';
      return 'compresed'
    },
    fileVisor() {
      return {
        pdf       : 'VisorPdf',
        video     : 'VisorVideo',
        image     : 'VisorImagen',
        office    : 'VisorOffice',
        txt       : 'VisorTxt',
      }[this.fileType];
    },
  },
  methods: {
    existInMimeType(word) {
      const { fileMimeType } = this.file;
      return (fileMimeType || '').search(word) > -1;
    }
  }
}
</script>

 <style scoped lang="scss"> 
.previewModal {
  .modal-header {
    display: flex;
    background-color: var(--color-neutral-darker);
    align-items: center;

    .modal-title {
      color: var(--color-neutral-lightest);
    }

    .btn-close {
      opacity: 1;
    }
  }

  .modal-body {
    padding: 0;
  }

  .buttonContiner {
    padding: 1rem;
    text-align: center;
  }

  ::v-deep {
    .visor-imagen {
      img {
        background-color: var(--color-neutral-dark);
      }
    }
  }
}
</style>
