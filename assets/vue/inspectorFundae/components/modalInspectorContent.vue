<template>
<div class="modalInspectorContent">
  <BaseModalInspector
      :identifier="tag + 'modalInspectorContent'"
      :title="`${$t('CONTENT.CONFIGUREFIELD.CONTENT')}`"
      padding="1rem 2.5rem"
      size="modal-lg">
    <TaskContentDetails/>
  </BaseModalInspector>
</div>
</template>

<script>
import TaskContentDetails from "../../announcement/components/details/taskContentDetails";
import BaseModalInspector from "./BaseModalInspector";
export default {
  name: "modalInspectorContent",
  components: {BaseModalInspector, TaskContentDetails},
  props: {
    tag: { type: String, default: '' },
  }
}
</script>

 <style scoped lang="scss"> 
.modalInspectorContent {}
</style>
