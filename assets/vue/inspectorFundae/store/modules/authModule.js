import { make } from "vuex-pathify";
import { getToken, setToken, setRefreshToken , deleteRefreshToken, deleteToken} from "../../../common/utils/axiosInterceptor";
import axios from "axios";

const state = {
    token: getToken(),
    hash: null
};

export default {
    namespaced: true,
    state,
    getters: {
        ...make.getters(state),
        isAuthenticated(state) {
            return state.token && state.token.length > 0;
        },
    },
    mutations: {
        SET_TOKEN(state, { token, refresh_token }) {
            state.token = token;
            setToken(token);
            setRefreshToken(refresh_token);
        },
        logout() {
            state.token = null;
            deleteToken();
            deleteRefreshToken();
        },
        SET_HASH(state, hash) {
            state.hash = hash;
        }
    },
    actions: {

        /**
         *
         * @param commit
         * @param user
         * @param password
         * @param token Required for access validation
         * @returns {Promise<boolean>}
         */
        async login({commit, getters, dispatch}, {
            user, password
        }) {
            try {
                const hash = getters.hash;
                const url = '/inspector/login';
                const result = await axios.post(url, {
                    user, password, token: hash
                });
                const { error, data } = result.data;
                const { token, refresh_token} = data;
                commit('SET_TOKEN', { token, refresh_token });
                return true;
            } catch (error) {
                const status = error?.response?.status;
                if (status === 401) {
                    dispatch('errorModule/setError', 'Unauthorized', { root: true });
                }
                return false;
            }
        },

        async logout({ commit }) {
            try {
                await axios.post('/inspector/logout');
                commit('logout');
            } finally {

            }
        },

        setHash({ commit }, hash) {
            commit('SET_HASH', hash);
        }
    }
};
