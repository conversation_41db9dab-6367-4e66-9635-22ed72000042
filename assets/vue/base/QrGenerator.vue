<template>
  <img class="QrGenerator" :src="qrCode" alt=" " :style="{maxWidth: `${size}px`}"/>
</template>

<script>
import QRious from 'qrious'
export default {
  name: "QrGenerator",
  props: {
    value: { type: String, default: '' },
    size: { type: Number, default: 300 },
  },
  data() {
    return {
      qrcode: new QRious({size: this.size})
    }
  },
  computed: {
    qrCode() {
      if (this.qrcode) {
        this.qrcode.value = this.value;
        const dataURL = this.qrcode.toDataURL();
        this.$emit('update', dataURL)
        return dataURL;
      }
    }
  },
  watch: {
    qrCode() {
      this.$emit('update', this.qrCode)
    }
  }
}
</script>

 <style scoped lang="scss"> 
.QrGenerator {
  width: 100%;
  height: 100%;
  margin: auto;
}
</style>
