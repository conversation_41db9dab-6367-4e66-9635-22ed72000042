<template>
  <div class="time-picker">
    <div class="select-wrapper" v-if="showHours">
      <label for="hours-select">{{ translationsVue.hours }}</label>
      <select
        v-model="selectedHour"
        id="hours-select"
        class="form-select"
        size="1"
        @change="emitTimeUpdate"
        :disabled="disabled"
      >
        <option value="" disabled selected>{{ translationsVue.hours }}</option>
        <option v-for="hour in filteredHours" :value="hour" :key="hour">
          {{ hour }}
        </option>
      </select>
    </div>

    <div class="select-wrapper" v-if="showMinutes">
      <label for="minutes-select">{{ translationsVue.minutes }}</label>
      <select
        v-model="selectedMinute"
        id="minutes-select"
        class="form-select minutes-select"
        size="1"
        @change="emitTimeUpdate"
        :disabled="disabled"
      >
        <option value="" disabled selected>
          {{ translationsVue.minutes }}
        </option>
        <option v-for="minute in filteredMinutes" :value="minute" :key="minute">
          {{ minute }}
        </option>
      </select>
    </div>

    <div class="select-wrapper" v-if="showSeconds">
      <label for="seconds-select">{{ translationsVue.seconds }}</label>
      <select
        v-model="selectedSecond"
        id="seconds-select"
        class="form-select"
        size="1"
        @change="emitTimeUpdate"
        :disabled="disabled"
      >
        <option value="" disabled selected>
          {{ translationsVue.seconds }}
        </option>
        <option v-for="second in seconds" :value="second" :key="second">
          {{ second }}
        </option>
      </select>
    </div>
  </div>
</template>

  <script>
export default {
  props: {
    options: {
      type: Array,
      default: () => ["hours", "minutes", "seconds"],
    },
    disabled: {
      type: Boolean,
      default: false
    },
    time: {
      type: String,
      default: "00:00:30",
    },
    maxHours: {
      type: Number,
      default: 24,
    },
    maxMinutes: {
      type: Number,
      default: 60,
    },
  },
  data() {
    return {
      selectedHour: "",
      selectedMinute: "",
      selectedSecond: "",
      hours: [],
      minutes: [],
      seconds: [],
      showHours: false,
      showMinutes: false,
      showSeconds: false,
      translationsVue,
    };
  },
  mounted() {
    this.populateTime();
  },
  watch: {
    time: {
      immediate: true,
      handler(newTime) {
        if (newTime) {
          const [hour, minute, second] = newTime.split(":");
          this.selectedHour = hour;
          this.selectedMinute = minute;
          this.selectedSecond = second;
        }
      },
    },
  },
  computed: {
    filteredHours() {
      return this.hours.slice(0, this.maxHours);
    },
    filteredMinutes() {
      return this.minutes.slice(0, this.maxMinutes);
    },
  },
  methods: {
    populateTime() {
      if (this.options.includes("hours")) {
        this.showHours = true;
        for (let hour = 0; hour < 24; hour++) {
          this.hours.push(this.padNumber(hour));
        }
      }

      if (this.options.includes("minutes")) {
        this.showMinutes = true;
        for (let minute = 0; minute < 60; minute++) {
          this.minutes.push(this.padNumber(minute));
        }
      }

      if (this.options.includes("seconds")) {
        this.showSeconds = true;
        for (let second = 0; second < 60; second++) {
          this.seconds.push(this.padNumber(second));
        }
      }

      const [defaultHour, defaultMinute, defaultSecond] = this.time.split(":");

      if (this.options.includes("hours")) {
        this.selectedHour = defaultHour || "00";
      }

      if (this.options.includes("minutes")) {
        this.selectedMinute = defaultMinute || "00";
      }

      if (this.options.includes("seconds")) {
        this.selectedSecond = defaultSecond || "30";
      }
    },
    padNumber(number) {
      return String(number).padStart(2, "0");
    },
    emitTimeUpdate() {
      const time = `${this.selectedHour}:${this.selectedMinute}:${this.selectedSecond}`;
      this.$emit("time-update", time);
    },
  },
};
</script>

   <style scoped lang="scss"> 
.time-picker {
  display: flex;
  align-items: center;
  margin-top: 1.5rem;
  gap: 1rem;
}

.select-wrapper {
  position: relative;
  width: 100%;
}

.select-wrapper label {
  position: absolute;
  top: -1.5rem;
  left: 0.75rem;
  font-size: 0.875rem;
  color: #6c757d;
}

.form-select {
  /* Estilos del select */
  appearance: none;
  padding: 0.375rem 1.75rem 0.375rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  background-color: #fff;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  width: 100%;
}

.minutes-select {
  /* Estilos del select de minutos */
  max-height: 150px; /* Tamaño máximo de la lista desplegable */
  overflow-y: auto; /* Habilitar el desplazamiento vertical */
}
</style>
