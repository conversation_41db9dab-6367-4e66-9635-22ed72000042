<template>
  <div class="info-icon-wrapper">
    <i
        class="fas fa-plus-circle" style="color: hsl(198, 99%, 44%)"
        @click.stop="toggleTooltip"
    ></i>

    <div
        v-if="isOpen"
        class="info-tooltip"
        :class="position"
        @click.stop
    >
      <h4 id="title">{{$t('TRANSLATIONS.LABEL_IN_PLURAL')}}</h4>
      <div class="languages-container">
        <span v-for="(language, index) in tooltipItems.slice(3)" :key="index">
          <router-link
              :to="{
                  name: route,
                  params: {
                    ...$route.params,
                    id: language.idCourse,
                    name: language.nameCourse
                  },
                }" custom v-slot="{ navigate }">
                      <a
                          @click="navigate"
                          :style="!language.published ? { color: 'orange' } : { color: 'hsl(198, 99%, 34%)' }"
                          class="language-link"
                          :data-tooltip="!language.published ? $t('COURSE.LANGUAGES.NOAVAILABLE')  : null"
                        >
                        <span v-html="language.locale"></span>
                      </a>
          </router-link>
          <span v-if="index < tooltipItems.slice(3).length - 1"> - </span>
        </span>
      </div>
    </div>

    <div
        v-if="isOpen"
        class="tooltip-backdrop"
        @click="closeTooltip"
    ></div>
  </div>
</template>
<script>
export default {
  name: "BaseIconInfo",
  props: {
    tooltipItems: {  // Cambiamos de tooltipText a tooltipItems
      type: Array,
      default: () => [],
    },
    position: {
      type: String,
      default: 'top', // 'top', 'right', 'bottom', 'left'
      validator: value => ['top', 'right', 'bottom', 'left'].includes(value)
    },
    route: {
      type: String,
      default: ''
    },

  },
  data() {
    return {
      isOpen: false
    };
  },
  methods: {
    toggleTooltip() {
      this.isOpen = !this.isOpen;
    },
    closeTooltip() {
      this.isOpen = false;
    }
  }
};
</script>

<style scoped>
.languages-container {
  display: inline-flex;
  align-items: center;
  flex-wrap: nowrap;
  white-space: nowrap;
  font-size: 1rem;
}

.languages-container > span {
  display: inline-flex;
  align-items: center;
}

.info-icon-wrapper {
  display: inline-block;
  position: relative;
  z-index: auto;
}

.fa-question-circle {
  cursor: pointer;
  color: var(--color-primary);
  font-size: 16px;
  transition: opacity 0.2s;
  margin: 0 4px;
}

.fa-question-circle:hover {
  opacity: 0.8;
}

.info-tooltip {
  position: absolute;
  background: white;
  border: 1px solid var(--color-primary);
  border-radius: 4px;
  padding: 8px 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1002;
  max-width: 300px;
  font-size: 14px;
}

.info-tooltip.top {
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 8px;
}

.info-tooltip.right {
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  margin-left: 8px;
}

/* Fondo oscuro (opcional) */
.tooltip-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1001;
  background: rgba(0, 0, 0, 0.1);
}
#title {
  text-transform: lowercase;
  display: inline-block;
  font-size: 1.1rem;
}

#title::first-letter {
  text-transform: uppercase;
}

.language-link {
  text-decoration: none;
  position: relative;
  cursor: pointer;
}
.language-link:hover {
  text-decoration: underline;
  cursor: pointer;
}

/* Tooltip personalizado */
.language-link[data-tooltip]::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: #ff9800; /* Fondo naranja */
  color: white; /* Letras blancas */
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: bold;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
  z-index: 1000;
  pointer-events: none;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
  text-transform: none !important; /* Anula mayúsculas */
  font-variant: normal !important;
}

.language-link[data-tooltip]::after {
  display: inline-block;
}

/* Flecha del tooltip */
.language-link[data-tooltip]::before {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(70%);
  border-width: 5px;
  border-style: solid;
  border-color: #ff9800 transparent transparent transparent;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
  z-index: 1001;
}

/* Mostrar tooltip al pasar el mouse */
.language-link[data-tooltip]:hover::after,
.language-link[data-tooltip]:hover::before {
  opacity: 1;
  visibility: visible;
}

/* Posicionamiento mejorado */
.language-link[data-tooltip]:hover::after {
  transform: translateX(-50%) translateY(-10px);
}

.language-link[data-tooltip]:hover::before {
  transform: translateX(-50%) translateY(0);
}
</style>