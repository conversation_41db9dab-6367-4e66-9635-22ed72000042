<template>
  <form action="" id="new-category" @submit.prevent class="LibraryCategory">
    <div class="col-12">
      <button type="button" class="close" @click="$emit('on-cancel')">&times;</button>
    </div>
    <div class="form__content">
      <div class="col-12 form-group">
        <input type="text" class="form-control" id="name" name="name" :placeholder="$t('LIBRARY.CATEGORY_NAME')" v-model="name">
      </div>

      <div class="col-12 p-0">
        <ul class="nav nav-tabs" id="locale-tabs" role="tablist">
          <li class="nav-item" role="presentation" v-for="(name, key) in locales">
            <button class="nav-link" :class="activeTab === key ? 'active' : ''" @click="activeTab = key">{{ name }}</button>
          </li>
        </ul>
        <div class="tab-content" id="locale-content">
          <div v-for="(name, key) in locales" class="tab-pane fade" :class="activeTab === key ? 'show active' : ''" role="tabpanel" :aria-labelledby="'locale_' + key + '_tab'">
            <div class="row">
              <div class="form-group col-12">
                <input :placeholder="translations.name[activeTab]" type="text" :name="'locale_' + key" class="form-control" :id="'locale_' + key">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="form__footer d-flex flex-row align-items-center justify-content-end mr-1">
      <button type="button" class="btn btn-warning m-1" @click="$emit('on-cancel')">{{ $t('LIBRARY.CANCEL') }}</button>
      <button type="button" class="btn btn-info m-1" @click="saveCategory()">{{ $t('LIBRARY.SAVE') }}</button>
    </div>
  </form>
</template>

<script>
import $ from 'jquery';
export default {
  name: "NewLibraryCategory",
  $,
  props: {
    category: null
  },
  data() {
    return {
      activeTab: '',
      name: this.category?.name ?? '',
      translations: {
        name: {
          en: 'Category Name',
          es: 'Nombre Categoría',
          pt: 'Categoria Nome',
        }
      }
    };
  },
  computed: {
    defaultLocale() {
      return this.$store.getters['localeModule/getDefaultLocale'];
    },
    locales() {
      return this.$store.getters['localeModule/getLocales'];
    }
  },
  mounted() {
    this.activeTab = this.defaultLocale;
    this.setCategoryFields();
  },
  watch: {
    category(newValue) {
      this.name = newValue?.name ?? '';
      this.setCategoryFields();
    },
  },
  methods: {
    setCategoryFields() {
      const keys = Object.keys(this.locales);
      keys.forEach(key => {
        const value = this.category?.translations[key];
        $(`#locale_${key}`).val( value );
      });
    },
    saveCategory() {
      const currentForm = document.forms['new-category'];
      const formData = new FormData(currentForm);
      if (this.name.length < 1) {
        this.$toast.error(this.$t('LIBRARY.NO_TEXT'))
        return;
      }
      this.$alertify.confirmWithTitle(
          this.$t('LIBRARY.CONFIRM_SAVE'),
          this.$t('LIBRARY.NEW_CATEGORY_INFO'),
          () => {
            if (this.category) {
              this.$store.dispatch('categoryModule/updateCategory', {categoryId: this.category.id, formData}).then(r => {
                const { data, error } = r;
                if (error) {
                  this.$toast.error(data);
                } else {
                  this.$toast.success(data);
                  this.$emit('on-success', true)
                }
              })
            } else {
              this.$store.dispatch('categoryModule/newCategory', formData).then(r => {
                const { data, error } = r;
                if (error) {
                  this.$toast.error(data);
                } else {
                  this.$toast.success(data);
                  this.$emit('on-success', true)
                }
              })
            }
          },
          () => {}
      )
    }
  }
}
</script>

 <style scoped lang="scss"> 
.LibraryCategory {
  width: 100%;
  display: flex;
  flex-flow: column;
  background-color: #F6F7F8;

  .form__header {
    width: 100%;
    padding: 0.25rem;

    .title {
      color: #1E293B;
      font-size: 22px;
      margin: 0 auto 0 1rem;
    }

    .close {
      cursor: pointer;
    }
  }
  .form__content {
    .nav-link {
      text-transform: uppercase;
      color: #707070;
      &.active {
        border-color: #CBD5E1 #CBD5E1 #ffffff;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        color: #1E293B;
      }
    }
    .tab-content {
      background-color: #FFFFFF;
      border-right: 1px solid #CBD5E1;
      border-bottom: 1px solid #CBD5E1;
      border-left: 1px solid #CBD5E1;

      .tab-pane {
        padding: 1rem 3rem;
      }
    }
  }
}
</style>
