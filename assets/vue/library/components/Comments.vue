<template>
  <div class="Comments">
    <div class="w-100 d-flex align-items-center justify-content-center" v-if="isLoadingComments">
      <loader :is-loaded="isLoadingComments"></loader>
    </div>
    <div class="Comments__content">
      <div class="no-data" v-if="pagination.totalItems === 0 && !isLoadingComments">
        {{ $t('LIBRARY.COMMENTS.NO_AVAILABLE') }}
      </div>
      <user-comment
                    v-for="comment in comments"
                    :key="comment.id"
                    :library-id="library.id"
                    :show-rating="library.enableRating"
                    :comment="comment"
                    @reload="loadComments()"
      >
      </user-comment>
    </div>
    <div class="Comments__footer" v-if="pagination.totalItems > pageSize">
      <pagination
          :total-items="pagination.totalItems"
          :page-size="pageSize"
          @current-page="onCurrentPage"
      ></pagination>
    </div>
  </div>
</template>

<script>
import UserComment from "./UserComment.vue";
import Loader from "../../admin/components/Loader.vue";
import Pagination from "../../admin/components/Pagination.vue";

export default {
  name: "Comments",
  components: {Pagination, Loader, UserComment},
  props: {
    library: null
  },
  data() {
    return {
      comments: [],
      pagination: {
        currentPage: 1,
        totalItems: 0
      },
    };
  },
  computed: {
    isLoadingComments() {
      return this.$store.getters['libraryModule/isLoadingComments'];
    },
    pageSize() {
      return this.$store.getters['getPageSize'];
    }
  },
  watch: {
    library() {
      this.loadComments();
    }
  },
  created() {
    this.loadComments()
  },
  methods: {
    async loadComments() {
      if (this.library && this.library.enableComments) {
        this.$store.dispatch('libraryModule/getComments', { id: this.library.id, page: this.pagination.currentPage }).then(r => {
          const { data, error } = r;
          if (!error) {
            this.comments = data.items;
            this.pagination.totalItems = data['total-items'];
          }
        })
      }
    },

    onCurrentPage(page) {

    }
  }
}
</script>

 <style scoped lang="scss"> 
.Comments {
  &__content {

  }
}
</style>
