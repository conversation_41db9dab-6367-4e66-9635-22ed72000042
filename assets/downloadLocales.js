const fs = require('fs');
const axios = require('axios');

const dir = './assets/vue/locales';
const locales = ['es', 'en', 'pt', 'fr', 'it', 'eu', 'ca', 'fi', 'de', 'pl', 'ro', 'uk'];

if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir);
}

function downloadLocale(localeArr) {
    const locale = localeArr.pop()
    axios.get(`https://localise.biz/api/export/locale/${locale}.json?status=translated&index=id&key=u-8l-rykYW5bwO4Y0YqfOTKczNmi456B`)
      .then((result) => {
          const data = { ...result.data };
          fs.writeFile(`${dir}/${locale}.json`, JSON.stringify(data), err => {
              if (err) console.log(err);
          });
          console.log(`${dir}/${locale}.json`);
      }).finally(() => {
        if (localeArr.length) downloadLocale(localeArr);
      })
}

downloadLocale(locales)
