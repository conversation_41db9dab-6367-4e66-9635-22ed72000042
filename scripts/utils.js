const { spawn, spawnSync} = require('child_process');

const printLoader = () => {
    const shapes = ['\\', '|', '/', '-'];
    let index = 0;
    return setInterval(() => {
        process.stdout.write(`\r${shapes[index]}`);
        index += 1;
        // eslint-disable-next-line no-bitwise
        index &= 3;
    }, 250);
};

const runNodeCommand = (command, {silentMode, loadingMode} = {silentMode: true, loadingMode: true}) => {
    const interval = loadingMode ? printLoader() : null;

    return new Promise((resolve, reject) => {
        const task = spawn(command, {shell: true});
        let stdoutData = '';
        let stderrData = '';

        if (!silentMode) {
            task.stdout.on('data', (data) => {
                const dataStr = data.toString();
                stdoutData += dataStr;
                process.stdout.write(dataStr);
            });
            task.stderr.on('data', (data) => {
                const dataStr = data.toString();
                stderrData += dataStr;
                process.stdout.write(dataStr);
            });
        }
        else {
            // Capturar la salida pero no mostrarla en tiempo real
            task.stdout.on('data', (data) => {
                stdoutData += data.toString();
            });
            task.stderr.on('data', (data) => {
                stderrData += data.toString();
            });
        }

        task.on('error', (error) => {
            reject(error);
        });

        task.on('exit', (code) => {
            if (interval) {
                clearInterval(interval);
            }

            if (code === 0) {
                resolve();
            } else {
                console.log(`Comando fallido: ${command}`);
                if (stderrData) {
                    console.log('Error de salida:');
                    console.log('---------------------------------');
                    console.log(stderrData);
                }
                if (stdoutData && silentMode) {
                    console.log('Salida estándar:');
                    console.log('---------------------------------');
                    console.log(stdoutData);
                }
                reject(new Error(`El comando falló con el código de salida ${code}`));
            }
        });

        task.on('close', (code) => {
            if (interval) {
                clearInterval(interval);
            }

            if (code === 0) {
                resolve();
            } else {
                if (!task.exitCode) { // Si no se ha manejado ya en el evento 'exit'
                    console.log(`Comando fallido: ${command}`);
                    if (stderrData) {
                        console.log('Error de salida:');
                        console.log('---------------------------------');
                        console.log(stderrData);
                    }
                    if (stdoutData && silentMode) {
                        console.log('Salida estándar:');
                        console.log('---------------------------------');
                        console.log(stdoutData);
                    }
                }
                reject(new Error(`El comando falló con el código de salida ${code}`));
            }
        });
    });
};


const getLatestTag = () => {
    const branchResponse = spawnSync('git rev-parse --abbrev-ref HEAD', {shell: true});
    const currentBranch = branchResponse.stdout.toString().replace('\n', '');

    const tagsResponse = spawnSync(`git tag --merged ${currentBranch} --sort=creatordate`, {shell: true});
    const tags = tagsResponse.stdout.toString().split('\n').filter(value => value);

    return tags[tags.length - 1];
}

const getNextTag = (latestTag, version) => {
    version = version || 'patch';

    const tag = latestTag.replace('v', '');
    const [major, minor, patch] = tag.split('.');

    if (version === 'major') {
        return `v${parseInt(major) + 1}.0.0`;
    }

    if (version === 'minor') {
        return `v${major}.${parseInt(minor) + 1}.0`;
    }

    return `v${major}.${minor}.${parseInt(patch) + 1}`;
}

const getNamedArgs = () => {
    const args = process.argv.slice(2);
    const namedArgs = {};

    args.forEach(arg => {
        const [key, value] = arg.split('=');
        if (key.startsWith('--')) {
            namedArgs[key.slice(2)] = value;
        }
    });

    return namedArgs;
}

module.exports = {
    printLoader,
    runNodeCommand,
    getLatestTag,
    getNextTag,
    getNamedArgs
};
