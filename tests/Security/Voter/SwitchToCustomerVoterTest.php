<?php

declare(strict_types=1);

namespace App\Tests\Security\Voter;

use App\Entity\User;
use App\Security\Voter\SwitchToCustomerVoter;
use App\Tests\Mother\Entity\UserMother;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\VoterInterface;
use Symfony\Component\Security\Core\User\UserInterface;

class SwitchToCustomerVoterTest extends TestCase
{
    private SwitchToCustomerVoter $voter;
    private TokenInterface $token;

    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        $this->voter = new SwitchToCustomerVoter();
        $this->token = $this->createMock(TokenInterface::class);
    }

    /**
     * @throws Exception
     */
    #[DataProvider('providerImpersonationRoles')]
    public function testUserCanSwitchBasedOnRole(array $userRoles, bool $expectedDecision): void
    {
        $user = UserMother::create(id: 1, roles: $userRoles);
        $subject = $this->createMock(UserInterface::class);

        $this->token->method('getUser')->willReturn($user);

        $result = $this->voter->vote($this->token, $subject, ['CAN_SWITCH_USER']);

        $this->assertSame(
            $expectedDecision ? VoterInterface::ACCESS_GRANTED : VoterInterface::ACCESS_DENIED,
            $result
        );
    }

    public static function providerImpersonationRoles(): \Generator
    {
        yield 'Super admin can switch' => [
            'userRoles' => [User::ROLE_SUPER_ADMIN],
            'expectedDecision' => true,
        ];

        yield 'Admin can switch' => [
            'userRoles' => [User::ROLE_ADMIN],
            'expectedDecision' => true,
        ];

        yield 'Support can switch' => [
            'userRoles' => [User::ROLE_SUPPORT],
            'expectedDecision' => true,
        ];

        yield 'Manager cannot switch' => [
            'userRoles' => [User::ROLE_MANAGER],
            'expectedDecision' => false,
        ];

        yield 'Tutor cannot switch' => [
            'userRoles' => [User::ROLE_TUTOR],
            'expectedDecision' => false,
        ];

        yield 'User cannot switch' => [
            'userRoles' => [User::ROLE_USER],
            'expectedDecision' => false,
        ];
    }

    public function testVoteDeniesAccessForAnonymousUser(): void
    {
        $subject = $this->createMock(UserInterface::class);
        $this->token->method('getUser')->willReturn(null);

        $result = $this->voter->vote($this->token, $subject, ['CAN_SWITCH_USER']);

        $this->assertSame(VoterInterface::ACCESS_DENIED, $result);
    }

    public function testVoteAbstainsForNonUserSubject(): void
    {
        $user = UserMother::create(id: 1, roles: [User::ROLE_ADMIN]);
        $subject = new \stdClass(); // Not a UserInterface

        $this->token->method('getUser')->willReturn($user);

        $result = $this->voter->vote($this->token, $subject, ['CAN_SWITCH_USER']);

        $this->assertSame(VoterInterface::ACCESS_ABSTAIN, $result);
    }
}
