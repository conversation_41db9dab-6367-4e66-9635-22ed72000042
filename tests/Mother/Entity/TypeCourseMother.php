<?php

declare(strict_types=1);

namespace App\Tests\Mother\Entity;

use App\Entity\TypeCourse;
use App\Enum\TypeCourse as EnumTypeCourse;

class TypeCourseMother
{
    public const int ONLINE_ID = 1;
    public const string DEFAULT_NAME = 'Teleformación';
    public const string DEFAULT_DESCRIPTION = 'Para los cursos de teleformación';
    public const string DEFAULT_CODE = 'online';

    public static function create(
        ?int $id = null,
        ?string $name = null,
        ?string $description = null,
        bool $active = true,
        ?string $code = null,
        ?string $denomination = null,
        ?\DateTimeInterface $deletedAt = null,
    ): TypeCourse {
        $typeCourse = new TypeCourse();

        if (null !== $id) {
            $typeCourse->setId($id);
        }

        $typeCourse->setName($name ?? self::DEFAULT_NAME);
        $typeCourse->setDescription($description ?? self::DEFAULT_DESCRIPTION);
        $typeCourse->setActive($active);
        $typeCourse->setCode($code ?? self::DEFAULT_CODE);
        $typeCourse->setDenomination($denomination ?? EnumTypeCourse::INTERN);
        $typeCourse->setDeletedAt($deletedAt);

        return $typeCourse;
    }
}
