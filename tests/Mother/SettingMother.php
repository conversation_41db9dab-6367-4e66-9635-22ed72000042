<?php

declare(strict_types=1);

namespace App\Tests\Mother;

use App\Entity\Setting;
use App\Entity\SettingGroup;

class SettingMother
{
    public static function create(
        ?int $id = null,
        string $code = 'app.setting.default',
        string $name = 'Default Setting',
        ?string $description = 'Description',
        int $sort = 999,
        array $options = [],
        string $type = 'bool',
        string $value = 'false',
        ?SettingGroup $settingGroup = null
    ): Setting {
        $setting = new Setting();

        if (null !== $id) {
            $setting->setId($id);
        }

        $setting->setCode($code)
            ->setName($name)
            ->setDescription($description)
            ->setSort($sort)
            ->setValue($value)
            ->setOptions($options)
            ->setSettingGroup($settingGroup)
            ->setType($type);

        return $setting;
    }
}
