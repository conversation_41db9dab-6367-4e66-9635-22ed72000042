<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait\Endpoints\Admin;

class AdminUsersEndpoints
{
    public static function getUsersEndpoint(
        ?int $page = null,
        ?int $pageSize = null,
        ?bool $isActive = null,
        ?string $startDate = null,
        ?string $endDate = null,
        ?string $sortBy = null,
        ?string $sortDir = null,
        ?string $search = null,
        ?string $role = null
    ): string {
        $queryParams = [];

        if (null !== $page && null !== $pageSize) {
            $queryParams['page'] = $page;
            $queryParams['page_size'] = $pageSize;
        }

        if (null !== $isActive) {
            $queryParams['is_active'] = $isActive ? 'true' : 'false';
        }

        if (null !== $startDate) {
            $queryParams['start_date'] = $startDate;
        }

        if (null !== $endDate) {
            $queryParams['end_date'] = $endDate;
        }

        if (null !== $sortBy && null !== $sortDir) {
            $queryParams['sort_by'] = $sortBy;
            $queryParams['sort_dir'] = $sortDir;
        }

        if (null !== $search) {
            $queryParams['search'] = $search;
        }

        if (null !== $role) {
            $queryParams['role'] = $role;
        }

        $url = '/api/v2/admin/users';

        if (!empty($queryParams)) {
            $url .= '?' . http_build_query($queryParams);
        }

        return $url;
    }

    public static function getCreatorsEndpoint(?string $search = null, ?int $page = null, ?int $pageSize = null): string
    {
        $url = '/api/v2/admin/users/creators';
        $params = [];

        if (null !== $search) {
            $params['search'] = $search;
        }

        if (null !== $page) {
            $params['page'] = $page;
        }

        if (null !== $pageSize) {
            $params['page_size'] = $pageSize;
        }

        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }

        return $url;
    }
}
