<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait;

use App\Entity\ChapterType;
use App\Tests\Mother\Entity\ChapterTypeMother;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;

trait ChapterTypeHelperTrait
{
    /**
     * @throws OptimisticLockException
     * @throws ORMException
     *
     * @deprecated Params limit the type creation
     * @see createAndGetChapterTypeRevised
     */
    protected function createAndGetChapterType(string $name = 'scorm'): ChapterType
    {
        $em = $this->getEntityManager();

        $chapterType = new ChapterType();
        $chapterType->setName($name);
        $chapterType->setActive(true);
        $chapterType->setType($name);
        $chapterType->setNormalized($name);

        $em->persist($chapterType);
        $em->flush();

        return $chapterType;
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function createAndGetChapterTypeRevised(
        ?int $id = null,
        ?string $name = null,
        ?string $description = null,
        ?string $type = null,
        ?string $code = null,
        bool $active = true,
        ?string $normalized = null,
        ?string $video = null,
        ?string $videoEn = null,
        ?float $percentageCompleted = null,
        ?string $icon = null,
        ?string $playerUrl = null,
    ): ChapterType {
        $em = $this->getEntityManager();

        if (null !== $id) {
            $chapterType = $em->getRepository(ChapterType::class)->find($id);
            if (null !== $chapterType) {
                return $chapterType;
            }
        }

        $chapterType = ChapterTypeMother::create(
            id: $id,
            name: $name,
            description: $description,
            type: $type,
            code: $code,
            active: $active,
            normalized: $normalized,
            video: $video,
            videoEn: $videoEn,
            percentageCompleted: $percentageCompleted,
            icon: $icon,
            playerUrl: $playerUrl,
        );

        $originalMetadata = $this->setCustomIdToEntity($chapterType);
        $em->persist($chapterType);
        $em->flush();
        $this->restoreEntityMetadata($chapterType, $originalMetadata);

        return $chapterType;
    }

    protected function getEntityManager(): EntityManager
    {
        return static::getContainer()->get('doctrine')->getManager();
    }
}
