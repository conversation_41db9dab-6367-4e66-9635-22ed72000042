<?php

declare(strict_types=1);

namespace App\Tests\Functional\Frontend\Language;

use App\Entity\Setting;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Frontend\FrontendLanguagesEndpoints;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\Response;

class LanguageFunctionalTest extends FunctionalTestCase
{
    private string $defaultLanguages = '';

    public function testLanguagesBySettings()
    {
        $response = $this->makeFrontendApiRequest('GET', FrontendLanguagesEndpoints::languagesEndpoint());
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $languagesSetting = $this->getLanguagesSetting();
        $responseData = $this->extractResponseData($response);

        foreach ($responseData as $key => $value) {
            $this->assertStringContainsString($key, $languagesSetting->getValue());
        }
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testLanguagesByParams()
    {
        // Change language settings name to force use params instead of settings.
        $languageSettings = $this->getLanguagesSetting();
        $languageSettings->setCode('app.languages.disabled'); // Change to non-existent setting
        $this->entityManagerSave();

        // Now the languages should be taken from the parameters.
        $response = $this->makeFrontendApiRequest('GET', FrontendLanguagesEndpoints::languagesEndpoint());
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $languagesParams = implode(',', $this->getLanguagesParams());
        $responseData = $this->extractResponseData($response);

        foreach ($responseData as $key => $value) {
            $this->assertStringContainsString($key, $languagesParams);
        }

        // Restore the original language settings name.
        $languageSettings->setCode('app.languages');
        $this->entityManagerSave();
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     *
     * @dataProvider languagesProvider
     */
    public function testSetLanguagesSettings(string $languages)
    {
        $this->defaultLanguages = $this->getLanguagesSetting()->getValue();
        $this->setSettingLanguages($languages);
        $this->checkLanguages($languages);
    }

    public static function languagesProvider(): \Generator
    {
        yield 'Korean & Japanese' => [
            'languages' => '["ko", "ja"]',
        ];
    }

    public function checkLanguages(string $languages)
    {
        $response = $this->makeFrontendApiRequest('GET', FrontendLanguagesEndpoints::languagesEndpoint());
        $responseData = $this->extractResponseData($response);

        foreach ($responseData as $key => $value) {
            $this->assertStringContainsString($key, $languages);
        }
    }

    /**
     * @throws NotSupported
     */
    private function getLanguagesSetting(): Setting
    {
        $settingsRepository = $this->getRepository(Setting::class);

        return $settingsRepository->findOneBy(['code' => 'app.languages']);
    }

    private function getLanguagesParams(): array
    {
        return $this->client->getContainer()->getParameter('app.languages');
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    private function setSettingLanguages($languages)
    {
        $languagesSetting = $this->getLanguagesSetting();
        $languagesSetting->setValue($languages);

        $this->entityManagerSave();
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    private function entityManagerSave()
    {
        $em = $this->getEntityManager();
        $em->flush();
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws MappingException
     */
    protected function tearDown(): void
    {
        if ('' !== $this->defaultLanguages) {
            $this->setSettingLanguages($this->defaultLanguages);
        }
        parent::tearDown();
    }
}
