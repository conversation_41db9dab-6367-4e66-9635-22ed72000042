<?php

declare(strict_types=1);

namespace App\Tests\Functional\Frontend\User;

use App\Enum\SettingGroupCode;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Frontend\FrontendUserEndpoints;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\Encoder\UserPasswordEncoderInterface;

class ChangePasswordTest extends FunctionalTestCase
{
    private const NEW_VALID_PASSWORD = '123456789aA!';
    private const NEW_INVALID_PASSWORD = '1';
    private const OLD_INVALID_PASSWORD = 'wrongPassword';

    private const SETTING_PASSWORD_MINIMUN = 3;
    private const SETTING_PASSWORD_POLICY = true;

    protected function setUp(): void
    {
        parent::setUp();

        $this->updateSettingsInDatabase([
            'password.policy.enabled' => self::SETTING_PASSWORD_POLICY,
            'password.minimum' => self::SETTING_PASSWORD_MINIMUN,
        ]);
    }

    protected function requestChangePassword(
        string $token,
        ?string $actualPassword = null,
        ?string $newPassword = null,
        ?string $email = null
    ): Response {
        $payload = [];

        if (null !== $actualPassword) {
            $payload['actualPassword'] = $actualPassword;
        }
        if (null !== $newPassword) {
            $payload['newPassword'] = $newPassword;
        }
        if (null !== $email) {
            $payload['email'] = $email;
        }

        return $this->makeFrontendApiRequest(
            'POST',
            FrontendUserEndpoints::changePasswordEndpoint(),
            $payload,
            [],
            [],
            $token
        );
    }

    protected function requestLogin(
        ?string $locale,
        string $email,
        string $password
    ): Response {
        return $this->makeFrontendApiRequest(
            'POST',
            FrontendUserEndpoints::loginEndpoint(),
            [
                'email' => $email,
                'password' => $password,
            ],
            [],
            [],
            $locale
        );
    }

    /**
     * @dataProvider resetPasswordOptionsProvider
     */
    public function testChangePasswordWithLocalesAndPayloads(
        ?array $payload = null,
        ?int $expectedResponseCode = null,
        ?array $expectedMessageOrMessages = null,
        ?bool $expectedError = null,
        ?string $locale = null
    ): void {
        $expectedMessage = null;
        $expectedMessages = null;

        if (isset($expectedMessageOrMessages[0]) && \is_array($expectedMessageOrMessages[0])) {
            $expectedMessages = $expectedMessageOrMessages;
        } elseif (isset($expectedMessageOrMessages['type'])) {
            $expectedMessage = $expectedMessageOrMessages;
        }

        $this->setLocaleForUser($locale);

        $token = $this->loginAndGetToken();

        $response = $this->requestChangePassword(
            $token,
            $payload['actualPassword'] ?? null,
            $payload['newPassword'] ?? null,
            $payload['email'] ?? null
        );

        if (null !== $expectedResponseCode) {
            $this->assertEquals(
                $expectedResponseCode,
                $response->getStatusCode(),
                "[Locale: $locale] HTTP response code mismatch."
            );
        }

        $responseData = json_decode($response->getContent(), true);

        if (null !== $expectedError) {
            if ($expectedError) {
                $this->assertTrue(
                    $responseData['error'] ?? false,
                    "[Locale: $locale] Expected error to be true."
                );
            } else {
                $this->assertFalse(
                    $responseData['error'] ?? false,
                    "[Locale: $locale] Expected error to be false."
                );
            }
        }

        if (null !== $expectedMessage) {
            $this->assertTranslatedMessage($responseData, $expectedMessage, $locale);
        }

        if (null !== $expectedMessages) {
            foreach ($expectedMessages as $msg) {
                $this->assertTranslatedMessage($responseData, $msg, $locale);
            }
        }
    }

    /**
     * @dataProvider loginPasswordChangedOptionsProvider
     */
    public function testChangePasswordAndThenLogin(
        ?array $payload,
        ?int $expectedResponseCode,
        ?string $loginPassword,
        ?int $expectedLoginStatusCode,
        ?bool $expectedLoginError,
        ?array $expectedMessage,
        ?string $locale = null
    ): void {
        $this->setLocaleForUser($locale);
        $token = $this->loginAndGetToken();

        $response = $this->requestChangePassword(
            $token,
            $payload['actualPassword'] ?? null,
            $payload['newPassword'] ?? null,
            $payload['email'] ?? null
        );

        $this->assertEquals(
            $expectedResponseCode,
            $response->getStatusCode(),
            "[Locale: $locale] HTTP response code mismatch."
        );

        $loginResponse = $this->requestLogin($locale, self::DEFAULT_USER_EMAIL, $loginPassword);

        $this->assertEquals(
            $expectedLoginStatusCode,
            $loginResponse->getStatusCode(),
            "[Locale: $locale] Post-change login: code mismatch."
        );

        if (false === $expectedLoginError) {
            $loginTokenData = $this->extractResponseData($loginResponse);
            $this->assertArrayHasKey('token', $loginTokenData);
            $this->assertNotEmpty($loginTokenData['token']);
        } else {
            if (null !== $expectedMessage) {
                $loginData = json_decode($loginResponse->getContent(), true);
                $this->assertTranslatedMessage($loginData, $expectedMessage, $locale);
            }
        }
    }

    public static function loginPasswordChangedOptionsProvider(): \Generator
    {
        $originalYields = [
            'login with new password (success)' => [
                'payload' => [
                    'actualPassword' => self::DEFAULT_USER_PASSWORD,
                    'newPassword' => self::NEW_VALID_PASSWORD,
                    'email' => null,
                ],
                'expectedResponseCode' => Response::HTTP_OK,
                'loginPassword' => self::NEW_VALID_PASSWORD,
                'expectedLoginStatusCode' => Response::HTTP_OK,
                'expectedLoginError' => false,
                'expectedMessage' => [],
            ],

            'login with old password (fail)' => [
                'payload' => [
                    'actualPassword' => self::DEFAULT_USER_PASSWORD,
                    'newPassword' => self::NEW_VALID_PASSWORD,
                    'email' => null,
                ],
                'expectedResponseCode' => Response::HTTP_OK,
                'loginPassword' => self::DEFAULT_USER_PASSWORD,
                'expectedLoginStatusCode' => Response::HTTP_UNAUTHORIZED,
                'expectedLoginError' => true,
                'expectedMessage' => [
                    'type' => 'translation',
                    'key' => 'message_api.base.credentials_no_correct',
                    'origin' => 'message_api',
                    'params' => null,
                ],
            ],
        ];

        yield from self::addLocalesToYields($originalYields);
    }

    public static function resetPasswordOptionsProvider(): \Generator
    {
        $originalYields = [
            'empty payload' => [
                'payload' => [
                    'actualPassword' => null,
                    'newPassword' => null,
                    'email' => null,
                ],
                'expectedResponseCode' => Response::HTTP_ACCEPTED,
                'expectedMessageOrMessages' => [
                    'type' => 'direct',
                    'message' => 'Current and new password required',
                ],
                'expectedError' => true,
            ],

            'only email provided' => [
                'payload' => [
                    'actualPassword' => null,
                    'newPassword' => null,
                    'email' => self::DEFAULT_USER_EMAIL,
                ],
                'expectedResponseCode' => Response::HTTP_ACCEPTED,
                'expectedMessageOrMessages' => [
                    'type' => 'direct',
                    'message' => 'Current and new password required',
                ],
                'expectedError' => true,
            ],

            'only password provided' => [
                'payload' => [
                    'actualPassword' => self::DEFAULT_USER_PASSWORD,
                    'newPassword' => null,
                    'email' => null,
                ],
                'expectedResponseCode' => Response::HTTP_ACCEPTED,
                'expectedMessageOrMessages' => [
                    'type' => 'direct',
                    'message' => 'Current and new password required',
                ],
                'expectedError' => true,
            ],

            'invalid old password' => [
                'payload' => [
                    'actualPassword' => self::OLD_INVALID_PASSWORD,
                    'newPassword' => self::NEW_VALID_PASSWORD,
                    'email' => null,
                ],
                'expectedResponseCode' => Response::HTTP_ACCEPTED,
                'expectedMessageOrMessages' => [
                    'type' => 'translation',
                    'key' => 'email.messages_to_user.password_does_not_match',
                    'origin' => 'email',
                    'params' => null,
                ],
                'expectedError' => true,
            ],

            'invalid new password - Multiple Error' => [
                'payload' => [
                    'actualPassword' => self::DEFAULT_USER_PASSWORD,
                    'newPassword' => self::NEW_INVALID_PASSWORD,
                    'email' => null,
                ],
                'expectedResponseCode' => Response::HTTP_ACCEPTED,
                'expectedMessageOrMessages' => [
                    [
                        'type' => 'translation',
                        'key' => 'password.minimum',
                        'origin' => 'messages',
                        'params' => [self::SETTING_PASSWORD_MINIMUN],
                    ],
                    [
                        'type' => 'translation',
                        'key' => 'password.uppercase',
                        'origin' => 'messages',
                        'params' => [],
                    ],
                    [
                        'type' => 'translation',
                        'key' => 'password.lowercase',
                        'origin' => 'messages',
                        'params' => [],
                    ],
                    [
                        'type' => 'translation',
                        'key' => 'password.special_characters',
                        'origin' => 'messages',
                        'params' => [],
                    ],
                ],
                'expectedError' => true,
            ],

            'valid new password' => [
                'payload' => [
                    'actualPassword' => self::DEFAULT_USER_PASSWORD,
                    'newPassword' => self::NEW_VALID_PASSWORD,
                    'email' => null,
                ],
                'expectedResponseCode' => Response::HTTP_OK,
                'expectedMessageOrMessages' => [
                    'type' => 'translation',
                    'key' => 'email.messages_to_user.change_pasword',
                    'origin' => 'email',
                    'params' => null,
                ],
                'expectedError' => false,
            ],
        ];

        yield from self::addLocalesToYields($originalYields);
    }

    private function restoreOriginalUserPass()
    {
        /** @var \Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface $passwordHasher */
        $passwordHasher = $this->client
            ->getContainer()
            ->get('Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface');

        $defaultUser = $this->getDefaultUser();
        $defaultUser->setPassword(
            $passwordHasher->hashPassword($defaultUser, self::DEFAULT_USER_PASSWORD)
        );
    }

    protected function tearDown(): void
    {
        /** @var UserPasswordEncoderInterface $passwordEncoder */
        $passwordEncoder = self::$container->get(UserPasswordEncoderInterface::class);

        $user = $this->getDefaultUser();
        $hashedPassword = $passwordEncoder->encodePassword($user, self::DEFAULT_USER_PASSWORD);
        $user->setPassword($hashedPassword);

        $user->setLocaleCampus(self::DEFAULT_USER_LOCALE_CAMPUS);
        $user->setLocale(self::DEFAULT_USER_LOCALE);

        $this->getEntityManager()->persist($user);

        $this->updateSettingsInDatabase([
            'password.policy.enabled' => $this->getSettingValue(SettingGroupCode::PLATFORM, 'password.policy.enabled'),
            'password.minimum' => $this->getSettingValue(SettingGroupCode::PLATFORM, 'password.minimum'),
        ]);

        $this->getEntityManager()->flush();

        parent::tearDown();
    }
}
