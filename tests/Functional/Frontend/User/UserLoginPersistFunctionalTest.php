<?php

declare(strict_types=1);

namespace App\Tests\Functional\Frontend\User;

use App\Entity\UserLogin;
use App\Repository\UserLoginRepository;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Frontend\FrontendUserEndpoints;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\Persistence\Mapping\MappingException;

class UserLoginPersistFunctionalTest extends FunctionalTestCase
{
    /**
     * @throws NotSupported
     */
    public function testLoginPersist(): void
    {
        $userLoginRepository = $this->getRepository(UserLogin::class);
        $this->assertCount(0, $userLoginRepository->findAll());

        $this->makeFrontendApiRequest('POST', FrontendUserEndpoints::loginEndpoint(), [
            'email' => self::DEFAULT_USER_EMAIL,
            'password' => self::DEFAULT_USER_PASSWORD,
        ]);

        $userLogins = $userLoginRepository->findAll();
        $this->assertCount(1, $userLogins);
        $this->assertEquals(self::DEFAULT_USER_ID, $userLogins[0]->getUser()->getId());
    }

    /**
     * @dataProvider withPreviousUserLoginDataProvider
     *
     * @throws ORMException
     */
    public function testWithPreviousUserLogin(
        \DateTimeImmutable $previousCreatedAt,
        int $expectedFinalRegisters
    ): void {
        $em = $this->getEntityManager();

        /** @var UserLoginRepository $userLoginRepository */
        $userLoginRepository = $this->getRepository(UserLogin::class);
        $this->assertCount(0, $userLoginRepository->findAll());

        $user = $this->getDefaultUser();

        $previousUserLogin = new UserLogin();
        $previousUserLogin->setUser($user);
        $previousUserLogin->setCreatedAt($previousCreatedAt);

        $em->persist($previousUserLogin);
        $em->flush();

        $this->makeFrontendApiRequest('POST', FrontendUserEndpoints::loginEndpoint(), [
            'email' => self::DEFAULT_USER_EMAIL,
            'password' => self::DEFAULT_USER_PASSWORD,
        ]);

        $this->assertCount($expectedFinalRegisters, $userLoginRepository->findAll());
    }

    public static function withPreviousUserLoginDataProvider(): iterable
    {
        yield 'with previous user login more than 2 hours ago' => [
            'previousCreatedAt' => new \DateTimeImmutable('-3 hours'),
            'expectedFinalRegisters' => 2,
        ];

        yield 'with previous user login less than 2 hours ago' => [
            'previousCreatedAt' => new \DateTimeImmutable('-1 hour'),
            'expectedFinalRegisters' => 1,
        ];
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([UserLogin::class]);

        parent::tearDown();
    }
}
