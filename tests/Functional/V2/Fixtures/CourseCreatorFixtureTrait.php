<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Fixtures;

use App\Tests\V2\Mother\Course\Creator\CourseCreatorMother;
use App\V2\Domain\Course\Creator\CourseCreator;

trait CourseCreatorFixtureTrait
{
    /**
     * Interacting with the InMemory repository.
     */
    private function setAndGetCourseCreatorInRepository(
        ?int $userId = null,
        ?int $courseId = null,
    ): CourseCreator {
        $creator = CourseCreatorMother::create(
            userId: $userId,
            courseId: $courseId,
        );

        $this->client->getContainer()->get('App\V2\Domain\Course\Creator\CourseCreatorRepository')->insert($creator);

        return $creator;
    }
}
