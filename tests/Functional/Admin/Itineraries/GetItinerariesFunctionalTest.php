<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Itineraries;

use App\Entity\Itinerary;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminItinerariesEndpoint;
use App\Tests\Functional\HelperTrait\ItineraryHelperTrait;

class GetItinerariesFunctionalTest extends FunctionalTestCase
{
    use ItineraryHelperTrait;

    /**
     * @dataProvider provideItineraryOrder
     */
    public function testItineraryOrder(array $itineraries, int $totalExpected, array $expected)
    {
        foreach ($itineraries as $itinerary) {
            $this->createAndGetItinerary(id: $itinerary['id'], name: $itinerary['name'], description: $itinerary['description'], active: $itinerary['active'], sort: $itinerary['sort']);
        }
        $userToken = $this->loginAndGetToken();

        $response = $this->makeAdminApiRequest(
            'GET',
            AdminItinerariesEndpoint::itinerariesEndpoint(1),
            [],
            [],
            [],
            $userToken
        );
        $this->assertEquals(200, $response->getStatusCode());
        $data = $this->extractResponseData($response);
        $this->assertCount($totalExpected, $data['items']);
        $normalizedData = $this->normalizeItineraryData($data);
        $this->assertEquals($expected, $normalizedData);
    }

    public static function provideItineraryOrder(): \Generator
    {
        yield 'Itineraries with 1 order and others null' => [
            'itineraries' => [
                ['id' => 1, 'name' => 'Example Itinerary 1', 'description' => 'Description 1', 'active' => true, 'sort' => 1],
                ['id' => 2, 'name' => 'Example Itinerary 2', 'description' => 'Description 2', 'active' => true, 'sort' => null],
                ['id' => 3, 'name' => 'Example Itinerary 3', 'description' => 'Description 3', 'active' => true, 'sort' => null],
            ],
            'totalExpected' => 3,
            'expected' => [
                'items' => [
                    [
                        'id' => 3,
                        'name' => 'Example Itinerary 3',
                        'active' => true,
                        'user_id' => 1,
                        'firstName' => 'Soporte',
                        'lastName' => 'Gestionet',
                        'totalCourses' => 0,
                        'viewHref' => '',
                        'editHref' => '',
                        'sort' => null,
                    ],
                    [
                        'id' => 2,
                        'name' => 'Example Itinerary 2',
                        'active' => true,
                        'user_id' => 1,
                        'firstName' => 'Soporte',
                        'lastName' => 'Gestionet',
                        'totalCourses' => 0,
                        'viewHref' => '',
                        'editHref' => '',
                        'sort' => null,
                    ],
                    [
                        'id' => 1,
                        'name' => 'Example Itinerary 1',
                        'active' => true,
                        'user_id' => 1,
                        'firstName' => 'Soporte',
                        'lastName' => 'Gestionet',
                        'totalCourses' => 0,
                        'viewHref' => '',
                        'editHref' => '',
                        'sort' => 1,
                    ],
                ],
                'total-items' => 3,
            ],
        ];
        yield 'One itinerary One expected' => [
            'itineraries' => [['id' => 1, 'name' => 'Example Itinerary', 'description' => 'Description', 'active' => true, 'sort' => 1]],
            'totalExpected' => 1,
            'expected' => [
                'items' => [
                    [
                        'id' => 1,
                        'name' => 'Example Itinerary',
                        'active' => true,
                        'user_id' => 1,
                        'firstName' => 'Soporte',
                        'lastName' => 'Gestionet',
                        'totalCourses' => 0,
                        'viewHref' => '',
                        'editHref' => '',
                        'sort' => 1,
                    ],
                ],
                'total-items' => 1,
            ],
        ];

        yield 'Multiple itineraries with different sort values' => [
            'itineraries' => [
                ['id' => 1, 'name' => 'Example 1', 'description' => 'Desc 1', 'active' => true, 'sort' => 2],
                ['id' => 2, 'name' => 'Example 2', 'description' => 'Desc 2', 'active' => true, 'sort' => 1],
                ['id' => 3, 'name' => 'Example 3', 'description' => 'Desc 3', 'active' => true, 'sort' => 3],
            ],
            'totalExpected' => 3,
            'expected' => [
                'items' => [
                    [
                        'id' => 2,
                        'name' => 'Example 2',
                        'active' => true,
                        'user_id' => 1,
                        'firstName' => 'Soporte',
                        'lastName' => 'Gestionet',
                        'totalCourses' => 0,
                        'viewHref' => '',
                        'editHref' => '',
                        'sort' => 1,
                    ],
                    [
                        'id' => 1,
                        'name' => 'Example 1',
                        'active' => true,
                        'user_id' => 1,
                        'firstName' => 'Soporte',
                        'lastName' => 'Gestionet',
                        'totalCourses' => 0,
                        'viewHref' => '',
                        'editHref' => '',
                        'sort' => 2,
                    ],
                    [
                        'id' => 3,
                        'name' => 'Example 3',
                        'active' => true,
                        'user_id' => 1,
                        'firstName' => 'Soporte',
                        'lastName' => 'Gestionet',
                        'totalCourses' => 0,
                        'viewHref' => '',
                        'editHref' => '',
                        'sort' => 3,
                    ],
                ],
                'total-items' => 3,
            ],
        ];
        yield 'Itineraries with same sort value' => [
            'itineraries' => [
                ['id' => 1, 'name' => 'Example 1', 'description' => 'Desc 1', 'active' => true, 'sort' => 1],
                ['id' => 2, 'name' => 'Example 2', 'description' => 'Desc 2', 'active' => true, 'sort' => 1],
            ],
            'totalExpected' => 2,
            'expected' => [
                'items' => [
                    [
                        'id' => 2,
                        'name' => 'Example 2',
                        'active' => true,
                        'user_id' => 1,
                        'firstName' => 'Soporte',
                        'lastName' => 'Gestionet',
                        'totalCourses' => 0,
                        'viewHref' => '',
                        'editHref' => '',
                        'sort' => 1,
                    ],
                    [
                        'id' => 1,
                        'name' => 'Example 1',
                        'active' => true,
                        'user_id' => 1,
                        'firstName' => 'Soporte',
                        'lastName' => 'Gestionet',
                        'totalCourses' => 0,
                        'viewHref' => '',
                        'editHref' => '',
                        'sort' => 1,
                    ],
                ],
                'total-items' => 2,
            ],
        ];
        yield 'Itineraries with false state' => [
            'itineraries' => [
                ['id' => 1, 'name' => 'Example Itinerary 1', 'description' => 'Description 1', 'active' => true, 'sort' => 1],
                ['id' => 2, 'name' => 'Example Itinerary 2', 'description' => 'Description 2', 'active' => false, 'sort' => 2],
                ['id' => 3, 'name' => 'Example Itinerary 3', 'description' => 'Description 3', 'active' => true, 'sort' => 3],
            ],
            'totalExpected' => 3,
            'expected' => [
                'items' => [
                    [
                        'id' => 1,
                        'name' => 'Example Itinerary 1',
                        'active' => true,
                        'user_id' => 1,
                        'firstName' => 'Soporte',
                        'lastName' => 'Gestionet',
                        'totalCourses' => 0,
                        'viewHref' => '',
                        'editHref' => '',
                        'sort' => 1,
                    ],
                    [
                        'id' => 2,
                        'name' => 'Example Itinerary 2',
                        'active' => false,
                        'user_id' => 1,
                        'firstName' => 'Soporte',
                        'lastName' => 'Gestionet',
                        'totalCourses' => 0,
                        'viewHref' => '',
                        'editHref' => '',
                        'sort' => 2,
                    ],
                    [
                        'id' => 3,
                        'name' => 'Example Itinerary 3',
                        'active' => true,
                        'user_id' => 1,
                        'firstName' => 'Soporte',
                        'lastName' => 'Gestionet',
                        'totalCourses' => 0,
                        'viewHref' => '',
                        'editHref' => '',
                        'sort' => 3,
                    ],
                ],
                'total-items' => 3,
            ],
        ];
        yield 'Itineraries with combined sorts' => [
            'itineraries' => [
                ['id' => 1, 'name' => 'Example Itinerary 1', 'description' => 'Description 1', 'active' => true, 'sort' => 1],
                ['id' => 2, 'name' => 'Example Itinerary 2', 'description' => 'Description 2', 'active' => false, 'sort' => null],
                ['id' => 3, 'name' => 'Example Itinerary 3', 'description' => 'Description 3', 'active' => true, 'sort' => 3],
                ['id' => 4, 'name' => 'Example Itinerary 4', 'description' => 'Description 4', 'active' => true, 'sort' => null],
            ],
            'totalExpected' => 4,
            'expected' => [
                'items' => [
                    [
                        'id' => 4,
                        'name' => 'Example Itinerary 4',
                        'active' => true,
                        'user_id' => 1,
                        'firstName' => 'Soporte',
                        'lastName' => 'Gestionet',
                        'totalCourses' => 0,
                        'viewHref' => '',
                        'editHref' => '',
                        'sort' => null,
                    ],
                    [
                        'id' => 2,
                        'name' => 'Example Itinerary 2',
                        'active' => false,
                        'user_id' => 1,
                        'firstName' => 'Soporte',
                        'lastName' => 'Gestionet',
                        'totalCourses' => 0,
                        'viewHref' => '',
                        'editHref' => '',
                        'sort' => null,
                    ],
                    [
                        'id' => 1,
                        'name' => 'Example Itinerary 1',
                        'active' => true,
                        'user_id' => 1,
                        'firstName' => 'Soporte',
                        'lastName' => 'Gestionet',
                        'totalCourses' => 0,
                        'viewHref' => '',
                        'editHref' => '',
                        'sort' => 1,
                    ],
                    [
                        'id' => 3,
                        'name' => 'Example Itinerary 3',
                        'active' => true,
                        'user_id' => 1,
                        'firstName' => 'Soporte',
                        'lastName' => 'Gestionet',
                        'totalCourses' => 0,
                        'viewHref' => '',
                        'editHref' => '',
                        'sort' => 3,
                    ],
                ],
                'total-items' => 4,
            ],
        ];
    }

    public function normalizeItineraryData(array $itineraries): array
    {
        foreach ($itineraries['items'] as &$itinerary) {
            $itinerary['viewHref'] = '';
            $itinerary['editHref'] = '';
        }

        return $itineraries;
    }

    /**
     * @throws \Doctrine\DBAL\Exception
     * @throws \Doctrine\Persistence\Mapping\MappingException
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([Itinerary::class]);

        parent::tearDown();
    }
}
