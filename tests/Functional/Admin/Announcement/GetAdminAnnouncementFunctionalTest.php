<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Announcement;

use App\Entity\Announcement;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\Setting;
use App\Entity\SettingGroup;
use App\Entity\TypeCourse;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminAnnouncementEndpoints;
use App\Tests\Functional\HelperTrait\SettingHelperTrait;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\Response;

class GetAdminAnnouncementFunctionalTest extends FunctionalTestCase
{
    use SettingHelperTrait;

    public const string MANAGER_SHARED_ANNOUNCEMENT_SETTING = 'app.announcement.managers.sharing';
    private Course $course;
    private Announcement $announcement;
    private User $user;
    private ?Setting $databaseSetting;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = $this->getDefaultUser();
        $this->course = $this->createAndGetCourse();
        $this->announcement = $this->createAndGetAnnouncement(course: $this->course, createdBy: $this->user);
    }

    public function testGetAnnouncementStructure(): void
    {
        $token = $this->loginAndGetToken();

        $response = $this->makeAdminApiRequest(
            method: 'GET',
            uri: AdminAnnouncementEndpoints::getAnnouncementEndPoint(
                announcementId: $this->announcement->getId(),
            ),
            bearerToken: $token
        );
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $data = $this->extractResponseData($response);
        $this->assertArrayHasKey('alertsTutor', $data);
        $this->assertArrayHasKey('aprovalCriteria', $data);
        $this->assertArrayHasKey('bonification', $data);
        $this->assertArrayHasKey('channels', $data);
        $this->assertArrayHasKey('chatChannel', $data);
        $this->assertArrayHasKey('comunications', $data);
        $this->assertArrayHasKey('course', $data);
        $this->assertArrayHasKey('createdAt', $data);
        $this->assertArrayHasKey('createdAtText', $data);
        $this->assertArrayHasKey('diploma', $data);
        $this->assertArrayHasKey('extra', $data);
        $this->assertArrayHasKey('finishAt', $data);
        $this->assertArrayHasKey('groupBasicInfo', $data);
        $this->assertArrayHasKey('guideTitle', $data);
        $this->assertArrayHasKey('guideURL', $data);
        $this->assertArrayHasKey('hasAlertTutor', $data);
        $this->assertArrayHasKey('hasAprovedCriteria', $data);
        $this->assertArrayHasKey('hasBonification', $data);
        $this->assertArrayHasKey('hasComunication', $data);
        $this->assertArrayHasKey('hasDigitalSignature', $data);
        $this->assertArrayHasKey('hasDiploma', $data);
        $this->assertArrayHasKey('hasMaterials', $data);
        $this->assertArrayHasKey('hasReportZip', $data);
        $this->assertArrayHasKey('hasSurvey', $data);
        $this->assertArrayHasKey('hasTasks', $data);
        $this->assertArrayHasKey('hasTemporalization', $data);
        $this->assertArrayHasKey('id', $data);
        $this->assertArrayHasKey('inspector', $data);
        $this->assertArrayHasKey('mainIdentification', $data);
        $this->assertArrayHasKey('notifiedAt', $data);
        $this->assertArrayHasKey('numberOfSessions', $data);
        $this->assertArrayHasKey('objectives', $data);
        $this->assertArrayHasKey('percentageForAproved', $data);
        $this->assertArrayHasKey('server', $data);
        $this->assertArrayHasKey('shareEnabled', $data);
        $this->assertArrayHasKey('source', $data);
        $this->assertArrayHasKey('startAt', $data);
        $this->assertArrayHasKey('status', $data);
        $this->assertArrayHasKey('subsidized', $data);
        $this->assertArrayHasKey('survey', $data);
        $this->assertArrayHasKey('temporization', $data);
        $this->assertArrayHasKey('timezone', $data);
        $this->assertArrayHasKey('totalHours', $data);
        $this->assertArrayHasKey('type', $data);
        $this->assertArrayHasKey('usersPerGroup', $data);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testManagerSharedAnnouncementSetting(): void
    {
        $token = $this->loginAndGetToken();

        $databaseSetting = $this->getEntityManager()
            ->getRepository(Setting::class)
            ->findOneBy(['code' => self::MANAGER_SHARED_ANNOUNCEMENT_SETTING]);

        if ($databaseSetting) {
            $this->databaseSetting = $databaseSetting;
            $this->getEntityManager()->remove($databaseSetting);
            $this->getEntityManager()->flush();
        }

        $serviceYamlValue = $this->client->getContainer()->getParameter(self::MANAGER_SHARED_ANNOUNCEMENT_SETTING);

        $response = $this->makeAdminApiRequest(
            method: 'GET',
            uri: AdminAnnouncementEndpoints::getAnnouncementEndPoint(
                announcementId: $this->announcement->getId(),
            ),
            bearerToken: $token
        );
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $data = $this->extractResponseData($response);

        // THe value is from service.yaml
        $this->assertEquals($serviceYamlValue, $data['shareEnabled']);

        // Create a new setting with the same code
        $newSetting = $this->createAndGetSetting(
            code: self::MANAGER_SHARED_ANNOUNCEMENT_SETTING,
            value: 'true',
            settingGroup: $this->getEntityManager()
                ->getRepository(SettingGroup::class)
                ->find(3) // Assuming group ID 3 is the correct one
        );

        $this->client->enableReboot();

        $response = $this->makeAdminApiRequest(
            method: 'GET',
            uri: AdminAnnouncementEndpoints::getAnnouncementEndPoint(
                announcementId: $this->announcement->getId(),
            ),
            bearerToken: $token
        );
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $data = $this->extractResponseData($response);

        // The value is from the database setting
        $this->assertEquals(filter_var($newSetting->getValue(), FILTER_VALIDATE_BOOLEAN), $data['shareEnabled']);

        $this->client->disableReboot();

        // change the value to false
        $newSetting = $this->getEntityManager()->getRepository(Setting::class)->find($newSetting->getId());
        $newSetting->setValue('false');
        $this->getEntityManager()->flush($newSetting);

        $this->client->enableReboot();

        $response = $this->makeAdminApiRequest(
            method: 'GET',
            uri: AdminAnnouncementEndpoints::getAnnouncementEndPoint(
                announcementId: $this->announcement->getId(),
            ),
            bearerToken: $token
        );
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $data = $this->extractResponseData($response);

        // The value is from the database setting
        $this->assertEquals(filter_var($newSetting->getValue(), FILTER_VALIDATE_BOOLEAN), $data['shareEnabled']);

        $this->client->disableReboot();

        // change the value to false
        $newSetting = $this->getEntityManager()->getRepository(Setting::class)->find($newSetting->getId());
        $this->getEntityManager()->remove($newSetting);

        // Reset setting if exist
        if (isset($this->databaseSetting)) {
            $this->createAndGetSetting(
                id: $this->databaseSetting->getId(),
                code: self::MANAGER_SHARED_ANNOUNCEMENT_SETTING,
                name: $this->databaseSetting->getName(),
                description: $this->databaseSetting->getDescription(),
                sort: $this->databaseSetting->getSort(),
                options: $this->databaseSetting->getOptions(),
                type: $this->databaseSetting->getType(),
                settingGroup: $this->getEntityManager()
                    ->getRepository(SettingGroup::class)
                    ->find($this->databaseSetting->getSettingGroup()->getId()) // Assuming group ID 3 is the correct one
            );
        }
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([
            Course::class,
            TypeCourse::class,
            CourseCategory::class,
            UserCourse::class,
            Announcement::class,
        ]);

        parent::tearDown();
    }
}
