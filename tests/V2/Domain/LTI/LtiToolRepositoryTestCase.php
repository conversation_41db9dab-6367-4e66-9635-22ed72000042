<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\LTI;

use App\Tests\V2\Mother\LTI\LtiToolMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\LTI\Exceptions\LtiToolNotFoundException;
use App\V2\Domain\LTI\LtiToolCollection;
use App\V2\Domain\LTI\LtiToolCriteria;
use App\V2\Domain\LTI\LtiToolRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\UuidCollection;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

abstract class LtiToolRepositoryTestCase extends TestCase
{
    abstract protected function getRepository(): LtiToolRepository;

    /**
     * @throws LtiToolNotFoundException
     * @throws InfrastructureException
     * @throws InvalidUuidException
     */
    public function testPut(): void
    {
        $registrationId1 = UuidMother::create();
        $registrationId2 = UuidMother::create();
        $registrationId3 = UuidMother::create();

        $tool1 = LtiToolMother::create(registrationId: $registrationId1);
        $tool2 = LtiToolMother::create(registrationId: $registrationId2);
        $tool3 = LtiToolMother::create(registrationId: $registrationId3);

        $repository = $this->getRepository();

        $repository->put($tool1);
        $repository->put($tool2);
        $repository->put($tool3);

        $found = $repository->findOneBy(
            LtiToolCriteria::createEmpty()
                ->filterByRegistrationId($registrationId1)
        );
        $this->assertEquals($tool1, $found);

        $found = $repository->findOneBy(
            LtiToolCriteria::createEmpty()
                ->filterById($tool2->getId())
        );
        $this->assertEquals($tool2, $found);
    }

    /**
     * @throws LtiToolNotFoundException
     * @throws InfrastructureException
     * @throws InvalidUuidException
     */
    public function testFindOneBy(): void
    {
        $tool1 = LtiToolMother::create();
        $tool2 = LtiToolMother::create();
        $tool3 = LtiToolMother::create(audience: 'Test audience 1');

        $repository = $this->getRepository();

        $repository->put($tool1);
        $repository->put($tool2);
        $repository->put($tool3);

        $this->assertEquals(
            $tool2,
            $repository->findOneBy(
                LtiToolCriteria::createEmpty()
                    ->filterById($tool2->getId())
            )
        );

        $this->assertEquals(
            $tool3,
            $repository->findOneBy(
                LtiToolCriteria::createEmpty()
                    ->filterById($tool3->getId())
                    ->filterByRegistrationId($tool3->getRegistrationId())
            )
        );

        $this->assertEquals(
            $tool3,
            $repository->findOneBy(
                LtiToolCriteria::createEmpty()
                    ->filterByAudience('Test audience 1')
            )
        );
    }

    /**
     * @throws InfrastructureException
     */
    #[DataProvider('provideFindBy')]
    public function testFindBy(
        LtiToolCollection $input,
        LtiToolCriteria $criteria,
        int $expectedCount,
        array $expectedResults,
    ): void {
        $repository = $this->getRepository();
        foreach ($input->all() as $tool) {
            $repository->put($tool);
        }

        $result = $repository->findBy(
            LtiToolCriteria::createEmpty()
        );
        $this->assertCount($input->count(), $result);
        $this->assertCount(
            0,
            array_diff(
                $input->all(),
                $result->all()
            )
        );

        $result = $repository->findBy($criteria);
        $this->assertCount($expectedCount, $result);
        $this->assertCount(
            0,
            array_diff(
                $expectedResults,
                $result->all(),
            )
        );
    }

    /**
     * @throws CollectionException
     * @throws InvalidUuidException
     */
    public static function provideFindBy(): \Generator
    {
        $tool1 = LtiToolMother::create();
        $tool2 = LtiToolMother::create();
        $tool3 = LtiToolMother::create();

        yield '3 tools get all' => [
            'input' => new LtiToolCollection([$tool1, $tool2, $tool3]),
            'criteria' => LtiToolCriteria::createEmpty(),
            'expectedCount' => 3,
            'expectedResults' => [$tool1, $tool2, $tool3],
        ];

        yield '3 tools get tool 1 by id' => [
            'input' => new LtiToolCollection([$tool1, $tool2, $tool3]),
            'criteria' => LtiToolCriteria::createEmpty()
                ->filterById($tool2->getId()),
            'expectedCount' => 1,
            'expectedResults' => [$tool2],
        ];

        yield '3 tools get tool 1 by registration' => [
            'input' => new LtiToolCollection([$tool1, $tool2, $tool3]),
            'criteria' => LtiToolCriteria::createEmpty()
                ->filterByRegistrationId($tool3->getRegistrationId()),
            'expectedCount' => 1,
            'expectedResults' => [$tool3],
        ];

        yield '3 tools get tool 1 by registration and by id' => [
            'input' => new LtiToolCollection([$tool1, $tool2, $tool3]),
            'criteria' => LtiToolCriteria::createEmpty()
                ->filterById($tool1->getId())
                ->filterByRegistrationId($tool1->getRegistrationId()),
            'expectedCount' => 1,
            'expectedResults' => [$tool1],
        ];

        yield '3 tools get by two registrations' => [
            'input' => new LtiToolCollection([$tool1, $tool2, $tool3]),
            'criteria' => LtiToolCriteria::createEmpty()
                ->filterByRegistrationIds(
                    new UuidCollection([$tool1->getRegistrationId(), $tool3->getRegistrationId()])
                ),
            'expectedCount' => 2,
            'expectedResults' => [$tool1, $tool3],
        ];
    }

    /**
     * @throws LtiToolNotFoundException
     * @throws InfrastructureException
     * @throws InvalidUuidException
     */
    public function testDelete(): void
    {
        $tool1 = LtiToolMother::create();
        $tool2 = LtiToolMother::create();
        $tool3 = LtiToolMother::create();

        $repository = $this->getRepository();

        $repository->put($tool1);
        $repository->put($tool2);
        $repository->put($tool3);

        $found = $repository->findOneBy(
            LtiToolCriteria::createEmpty()
                ->filterById($tool2->getId())
        );
        $this->assertEquals($tool2, $found);

        $repository->delete($tool2);

        try {
            $repository->findOneBy(
                LtiToolCriteria::createEmpty()
                    ->filterById($tool2->getId())
            );

            $this->fail('Expected exception');
        } catch (LtiToolNotFoundException) {
        }

        $found = $repository->findOneBy(
            LtiToolCriteria::createEmpty()
                ->filterById($tool3->getId())
                ->filterByRegistrationId($tool3->getRegistrationId())
        );

        $this->assertEquals($tool3, $found);
    }
}
