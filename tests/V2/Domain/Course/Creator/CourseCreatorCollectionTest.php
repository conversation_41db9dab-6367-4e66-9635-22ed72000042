<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Course\Creator;

use App\Tests\V2\Domain\Shared\Collection\CollectionTestCase;
use App\Tests\V2\Mother\Course\Creator\CourseCreatorMother;
use App\V2\Domain\Course\Creator\CourseCreator;
use App\V2\Domain\Course\Creator\CourseCreatorCollection;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;

class CourseCreatorCollectionTest extends CollectionTestCase
{
    /**
     * @throws CollectionException
     */
    protected function createCollection(array $items): Collection
    {
        return new CourseCreatorCollection($items);
    }

    protected function getExpectedType(): string
    {
        return CourseCreator::class;
    }

    protected function getItem(): object
    {
        return CourseCreatorMother::create();
    }
}
