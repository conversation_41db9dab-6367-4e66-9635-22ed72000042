<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Purchase;

use App\Tests\V2\Domain\Shared\Collection\CollectionTestCase;
use App\Tests\V2\Mother\Purchase\PurchasableItemMother;
use App\V2\Domain\Purchase\PurchasableItem;
use App\V2\Domain\Purchase\PurchasableItemCollection;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;

class PurchasableItemCollectionTest extends CollectionTestCase
{
    /**
     * @throws CollectionException
     */
    protected function createCollection(array $items): Collection
    {
        return new PurchasableItemCollection($items);
    }

    protected function getExpectedType(): string
    {
        return PurchasableItem::class;
    }

    /**
     * @throws InvalidUuidException
     */
    protected function getItem(): object
    {
        return PurchasableItemMother::create();
    }
}
