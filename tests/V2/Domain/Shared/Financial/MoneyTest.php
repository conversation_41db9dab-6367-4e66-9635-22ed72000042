<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Shared\Financial;

use App\V2\Domain\Shared\Financial\Currency;
use App\V2\Domain\Shared\Financial\Exception\CurrencyException;
use App\V2\Domain\Shared\Financial\Money;
use PHPUnit\Framework\TestCase;

class MoneyTest extends TestCase
{
    public function testFromCentsWithDefaultCurrency(): void
    {
        $money = Money::create(1500);

        $this->assertSame(1500, $money->value());
        $this->assertEquals(Currency::EUR(), $money->currency());
        $this->assertSame(15.0, $money->formattedValue());
    }

    public function testFromCentsWithSpecificCurrency(): void
    {
        $currency = Currency::USD();

        $money = Money::create(2500, $currency);

        $this->assertSame(2500, $money->value());
        $this->assertEquals($currency, $money->currency());
        $this->assertSame(25.0, $money->formattedValue());
    }

    public function testFromFloatWithDefaultCurrency(): void
    {
        $money = Money::createFromFloat(19.99);

        $this->assertSame(1999, $money->value());
        $this->assertEquals(Currency::EUR(), $money->currency());
        $this->assertSame(19.99, $money->formattedValue());
    }

    public function testFromFloatWithSpecificCurrency(): void
    {
        $currency = Currency::USD();

        $money = Money::createFromFloat(50.75, $currency);

        $this->assertSame(5075, $money->value());
        $this->assertEquals($currency, $money->currency());
        $this->assertSame(50.75, $money->formattedValue());
    }

    public function testFromFloatWithIntegerValue(): void
    {
        $money = Money::createFromFloat(100);

        $this->assertSame(10000, $money->value());
        $this->assertSame(100.0, $money->formattedValue());
    }

    public function testFromFloatWithRounding(): void
    {
        $money1 = Money::createFromFloat(19.996); // Should round to 20.00
        $money2 = Money::createFromFloat(19.994); // Should round to 19.99

        $this->assertSame(2000, $money1->value());
        $this->assertSame(20.0, $money1->formattedValue());
        $this->assertSame(1999, $money2->value());
        $this->assertSame(19.99, $money2->formattedValue());
    }

    public function testFromFloatThrowsExceptionForNaN(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Amount must be a finite number');

        Money::createFromFloat(NAN);
    }

    public function testFromFloatThrowsExceptionForInfinite(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Amount must be a finite number');

        Money::createFromFloat(INF);
    }

    public function testFromFloatThrowsExceptionForNegativeInfinite(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Amount must be a finite number');

        Money::createFromFloat(-INF);
    }

    public function testToString(): void
    {
        $money1 = Money::create(1500, Currency::EUR());
        $money2 = Money::create(2500, Currency::USD());

        $this->assertSame('15.00 EUR', (string) $money1);
        $this->assertSame('25.00 USD', (string) $money2);
    }

    public function testEqualsWithSameAmountAndCurrency(): void
    {
        $money1 = Money::create(1500, Currency::EUR());
        $money2 = Money::create(1500, Currency::EUR());

        $this->assertTrue($money1->equals($money2));
        $this->assertTrue($money2->equals($money1));
    }

    public function testEqualsWithDifferentAmountSameCurrency(): void
    {
        $money1 = Money::create(1500, Currency::EUR());
        $money2 = Money::create(2000, Currency::EUR());

        $this->assertFalse($money1->equals($money2));
        $this->assertFalse($money2->equals($money1));
    }

    public function testEqualsWithSameAmountDifferentCurrency(): void
    {
        $money1 = Money::create(1500, Currency::EUR());
        $money2 = Money::create(1500, Currency::USD());

        $this->assertFalse($money1->equals($money2));
        $this->assertFalse($money2->equals($money1));
    }

    public function testAddWithSameCurrency(): void
    {
        $money1 = Money::create(1500, Currency::EUR());
        $money2 = Money::create(2500, Currency::EUR());

        $result = $money1->add($money2);

        $this->assertSame(4000, $result->value());
        $this->assertEquals(Currency::EUR(), $result->currency());
        $this->assertSame(40.0, $result->formattedValue());
    }

    public function testAddWithDifferentCurrencyThrowsException(): void
    {
        $money1 = Money::create(1500, Currency::EUR());
        $money2 = Money::create(2500, Currency::USD());

        $this->expectException(CurrencyException::class);
        $this->expectExceptionMessage('Cannot operate on Money objects with different currencies: EUR and USD');

        $money1->add($money2);
    }

    public function testSubtractWithSameCurrency(): void
    {
        $money1 = Money::create(2500, Currency::EUR());
        $money2 = Money::create(1500, Currency::EUR());

        $result = $money1->subtract($money2);

        $this->assertSame(1000, $result->value());
        $this->assertEquals(Currency::EUR(), $result->currency());
        $this->assertSame(10.0, $result->formattedValue());
    }

    public function testSubtractWithDifferentCurrencyThrowsException(): void
    {
        $money1 = Money::create(2500, Currency::EUR());
        $money2 = Money::create(1500, Currency::USD());

        $this->expectException(CurrencyException::class);
        $this->expectExceptionMessage('Cannot operate on Money objects with different currencies: EUR and USD');

        $money1->subtract($money2);
    }

    public function testSubtractResultingInNegativeAmount(): void
    {
        $money1 = Money::create(1000, Currency::EUR());
        $money2 = Money::create(1500, Currency::EUR());

        $result = $money1->subtract($money2);

        $this->assertSame(-500, $result->value());
        $this->assertSame(-5.0, $result->formattedValue());
    }

    public function testZeroAmount(): void
    {
        $money = Money::create(0);

        $this->assertSame(0, $money->value());
        $this->assertSame(0.0, $money->formattedValue());
        $this->assertSame('0.00 EUR', (string) $money);
    }

    public function testNegativeAmount(): void
    {
        $money = Money::create(-1500);

        $this->assertSame(-1500, $money->value());
        $this->assertSame(-15.0, $money->formattedValue());
        $this->assertSame('-15.00 EUR', (string) $money);
    }

    public function testFromFloatWithZero(): void
    {
        $money = Money::createFromFloat(0.0);

        $this->assertSame(0, $money->value());
        $this->assertSame(0.0, $money->formattedValue());
    }

    public function testFromFloatWithNegativeValue(): void
    {
        $money = Money::createFromFloat(-25.50);

        $this->assertSame(-2550, $money->value());
        $this->assertSame(-25.5, $money->formattedValue());
    }
}
