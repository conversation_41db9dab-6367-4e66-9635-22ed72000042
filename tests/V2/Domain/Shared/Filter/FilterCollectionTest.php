<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Shared\Filter;

use App\Entity\Filter;
use App\Tests\V2\Domain\Shared\Collection\CollectionTestCase;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Filter\FilterCollection;

class FilterCollectionTest extends CollectionTestCase
{
    /**
     * @throws CollectionException
     */
    protected function createCollection(array $items): Collection
    {
        return new FilterCollection($items);
    }

    protected function getExpectedType(): string
    {
        return Filter::class;
    }

    protected function getItem(): object
    {
        return new Filter();
    }
}
