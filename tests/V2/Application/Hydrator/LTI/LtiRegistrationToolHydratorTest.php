<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\Hydrator\LTI;

use App\Tests\V2\Mother\LTI\LtiRegistrationMother;
use App\Tests\V2\Mother\LTI\LtiToolMother;
use App\V2\Application\Hydrator\HydratorPriority;
use App\V2\Application\Hydrator\LTI\LtiRegistrationToolHydrator;
use App\V2\Domain\LTI\LtiRegistrationCollection;
use App\V2\Domain\LTI\LtiRegistrationHydrationCriteria;
use App\V2\Domain\LTI\LtiToolCollection;
use App\V2\Domain\LTI\LtiToolRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Hydrator\HydratorException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class LtiRegistrationToolHydratorTest extends TestCase
{
    /**
     * @throws Exception
     */
    private function getHydrator(
        ?LtiToolRepository $ltiToolRepository = null
    ): LtiRegistrationToolHydrator {
        return new LtiRegistrationToolHydrator(
            ltiToolRepository: $ltiToolRepository ?? $this->createMock(LtiToolRepository::class),
        );
    }

    public function testPriority(): void
    {
        $hydrator = $this->getHydrator();
        $this->assertEquals(HydratorPriority::First, $hydrator->getPriority());
    }

    /**
     * @throws Exception
     */
    public function testSupports(): void
    {
        $hydrator = $this->getHydrator();
        $this->assertTrue(
            $hydrator->supports(
                LtiRegistrationHydrationCriteria::createEmpty()
                    ->withTool()
            )
        );

        $this->assertFalse(
            $hydrator->supports(
                LtiRegistrationHydrationCriteria::createEmpty()
            )
        );
    }

    /**
     * @throws InfrastructureException
     * @throws HydratorException
     * @throws Exception
     * @throws CollectionException
     */
    public function testEmptyCollection(): void
    {
        $collection = new LtiRegistrationCollection([]);
        $hydrator = $this->getHydrator();
        $hydrator->hydrate(
            $collection,
            LtiRegistrationHydrationCriteria::createEmpty()
                ->withTool()
        );
        $this->assertEmpty($collection->all());
    }

    /**
     * @throws InfrastructureException
     * @throws Exception
     * @throws HydratorException
     * @throws CollectionException
     * @throws InvalidUuidException
     */
    public function testHydrateWithTool(): void
    {
        $registration1 = LtiRegistrationMother::create();
        $registration2 = LtiRegistrationMother::create();
        $registration3 = LtiRegistrationMother::create();

        $tool1 = LtiToolMother::create(registrationId: $registration1->getId());
        $tool2 = LtiToolMother::create(registrationId: $registration2->getId());
        $tool3 = LtiToolMother::create(registrationId: $registration3->getId());

        $collection = new LtiRegistrationCollection([$registration1, $registration2, $registration3]);

        $ltiToolRepository = $this->createMock(LtiToolRepository::class);
        $ltiToolRepository->expects($this->once())->method('findBy')
            ->willReturn(new LtiToolCollection([$tool1, $tool2, $tool3]));

        $hydrator = $this->getHydrator(
            ltiToolRepository: $ltiToolRepository
        );
        $hydrator->hydrate(
            $collection,
            LtiRegistrationHydrationCriteria::createEmpty()
                ->withTool()
        );

        $this->assertNotNull($registration1->getTool());
        $this->assertNotNull($registration2->getTool());
        $this->assertNotNull($registration3->getTool());

        $this->assertEquals($tool1, $registration1->getTool());
        $this->assertEquals($tool2, $registration2->getTool());
        $this->assertEquals($tool3, $registration3->getTool());
    }
}
