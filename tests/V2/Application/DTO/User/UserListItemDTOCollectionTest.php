<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\DTO\User;

use App\Tests\V2\Domain\Shared\Collection\CollectionTestCase;
use App\Tests\V2\Mother\User\UserListItemDtoMother;
use App\V2\Application\DTO\User\UserListItemDTO;
use App\V2\Application\DTO\User\UserListItemDTOCollection;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;

class UserListItemDTOCollectionTest extends CollectionTestCase
{
    /**
     * @throws CollectionException
     */
    protected function createCollection(array $items): Collection
    {
        return new UserListItemDTOCollection($items);
    }

    protected function getExpectedType(): string
    {
        return UserListItemDTO::class;
    }

    protected function getItem(): object
    {
        return UserListItemDtoMother::create();
    }
}
