<?php

declare(strict_types=1);

namespace App\Tests\V2\Mother\User;

use App\V2\Application\DTO\User\UserListItemDTO;
use App\V2\Application\DTO\User\UserListItemDTOCollection;
use App\V2\Domain\Shared\Collection\CollectionException;

class UserListItemDtoCollectionMother
{
    /**
     * Create a UserListItemDTOCollection with default or custom items.
     *
     * @param UserListItemDTO[] $items Items to include in the collection (default: empty array)
     *
     * @throws CollectionException
     */
    public static function create(
        array $items = [],
    ): UserListItemDTOCollection {
        // If no items provided, create a default collection with 3 items
        if (empty($items)) {
            $items = [
                UserListItemDtoMother::create(id: 1, firstName: 'User 1'),
                UserListItemDtoMother::create(id: 2, firstName: 'User 2'),
                UserListItemDtoMother::create(id: 3, firstName: 'User 3'),
            ];
        }

        return new UserListItemDTOCollection($items);
    }

    /**
     * Create a UserListItemDTOCollection with a specific number of items.
     *
     * @param int $count Number of items to create
     *
     * @throws CollectionException
     */
    public static function createWithCount(
        int $count,
    ): UserListItemDTOCollection {
        $items = [];
        for ($i = 1; $i <= $count; ++$i) {
            $items[] = UserListItemDtoMother::create(
                id: $i,
                email: "user$<EMAIL>",
                firstName: "User $i",
            );
        }

        return new UserListItemDTOCollection($items);
    }
}
