<?php

declare(strict_types=1);

namespace App\Tests\V2\Mother\Announcement\Manager;

use App\Tests\V2\Mother\Shared\Email\EmailMother;
use App\V2\Domain\Announcement\Manager\Manager;
use App\V2\Domain\Shared\Email\Email;
use App\V2\Domain\Shared\Id\Id;

class ManagerMother
{
    private const int DEFAULT_ID = 1;
    private const string DEFAULT_FIRST_NAME = 'John';
    private const string DEFAULT_LAST_NAME = 'Doe';

    public static function create(
        ?Id $id = null,
        ?Email $email = null,
        ?string $firstName = null,
        ?string $lastName = null,
    ): Manager {
        return new Manager(
            id: $id ?? new Id(self::DEFAULT_ID),
            email: $email ?? EmailMother::create(),
            firstName: $firstName ?? self::DEFAULT_FIRST_NAME,
            lastName: $lastName ?? self::DEFAULT_LAST_NAME,
        );
    }

    public static function withName(string $firstName, string $lastName): Manager
    {
        return self::create(
            firstName: $firstName,
            lastName: $lastName
        );
    }

    public static function withEmail(string $email): Manager
    {
        return self::create(
            email: EmailMother::create($email)
        );
    }

    public static function withId(int $id): Manager
    {
        return self::create(
            id: new Id($id)
        );
    }
}
