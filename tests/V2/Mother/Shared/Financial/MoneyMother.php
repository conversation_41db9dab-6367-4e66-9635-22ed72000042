<?php

declare(strict_types=1);

namespace App\Tests\V2\Mother\Shared\Financial;

use App\V2\Domain\Shared\Financial\Currency;
use App\V2\Domain\Shared\Financial\CurrencyCode;
use App\V2\Domain\Shared\Financial\Money;

class MoneyMother
{
    private const int DEFAULT_AMOUNT = 1000;
    private const CurrencyCode DEFAULT_CURRENCY_CODE = CurrencyCode::EUR;

    public static function create(
        ?int $amount = null,
        ?Currency $currency = null,
    ): Money {
        return Money::create(
            amount: $amount ?? self::DEFAULT_AMOUNT,
            currency: $currency ?? new Currency(self::DEFAULT_CURRENCY_CODE),
        );
    }
}
