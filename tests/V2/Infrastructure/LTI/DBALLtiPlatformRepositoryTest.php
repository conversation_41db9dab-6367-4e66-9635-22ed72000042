<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\LTI;

use App\Tests\V2\Domain\LTI\LtiPlatformRepositoryTestCase;
use App\Tests\V2\Infrastructure\DBAL\DBALConnectionFactory;
use App\V2\Domain\LTI\LtiPlatformRepository;
use App\V2\Infrastructure\LTI\DBALLtiPlatformRepository;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Schema\SchemaException;
use Doctrine\ORM\EntityManager;

class DBALLtiPlatformRepositoryTest extends LtiPlatformRepositoryTestCase
{
    private const string TABLE_NAME = 'lti_platform';
    private Connection $connection;

    /**
     * @throws \PHPUnit\Framework\MockObject\Exception
     * @throws SchemaException
     * @throws Exception
     */
    protected function getRepository(): LtiPlatformRepository
    {
        /**
         * @var Schema $schema
         */
        [$this->connection, $schema] = DBALConnectionFactory::create();

        if ($schema->hasTable(self::TABLE_NAME)) {
            $this->dropTable();
        }

        $em = $this->createMock(EntityManager::class);
        $em->method('getConnection')
            ->willReturn($this->connection);

        $table = $schema->createTable(self::TABLE_NAME);
        $table->addColumn('id', 'string');
        $table->addColumn('registration_id', 'string');
        $table->addColumn('name', 'string');
        $table->addColumn('audience', 'string');
        $table->addColumn('oidc_authentication_url', 'string');
        $table->addColumn('oauth2_access_token_url', 'string');
        $table->addColumn('jwks_url', 'string');

        $table->setPrimaryKey(['id']);

        foreach ($this->connection->getDatabasePlatform()->getCreateTableSQL($table) as $sql) {
            $this->connection->executeStatement($sql);
        }

        return new DBALLtiPlatformRepository(
            em: $em,
            ltiPlatformTableName: self::TABLE_NAME,
        );
    }

    /**
     * @throws Exception
     */
    private function dropTable(): void
    {
        $this->connection->executeStatement('DROP TABLE IF EXISTS `' . self::TABLE_NAME . '`');
    }

    protected function tearDown(): void
    {
        $this->dropTable();
        parent::tearDown();
    }
}
