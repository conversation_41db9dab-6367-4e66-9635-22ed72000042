<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\LTI;

use App\Tests\V2\Mother\LTI\LtiDeploymentMother;
use App\Tests\V2\Mother\LTI\LtiPlatformMother;
use App\Tests\V2\Mother\LTI\LtiRegistrationMother;
use App\Tests\V2\Mother\LTI\LtiToolMother;
use App\V2\Application\Hydrator\LTI\LtiRegistrationHydratorCollection;
use App\V2\Application\Log\Logger;
use App\V2\Domain\LTI\LtiDeploymentCollection;
use App\V2\Domain\LTI\LtiPlatformRepository;
use App\V2\Domain\LTI\LtiRegistrationCollection;
use App\V2\Domain\LTI\LtiRegistrationHydrationCriteria;
use App\V2\Domain\LTI\LtiRegistrationRepository;
use App\V2\Domain\LTI\LtiToolRepository;
use App\V2\Infrastructure\LTI\CoreKeyChainGenerator;
use App\V2\Infrastructure\LTI\RegistrationRepository;
use OAT\Library\Lti1p3Core\Platform\Platform as CorePlatform;
use OAT\Library\Lti1p3Core\Registration\RegistrationFactory;
use OAT\Library\Lti1p3Core\Registration\RegistrationRepositoryInterface;
use OAT\Library\Lti1p3Core\Tool\Tool as CoreTool;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class RegistrationRepositoryTest extends TestCase
{
    /**
     * @throws Exception
     */
    private function getRegistrationRepository(
        ?LtiRegistrationRepository $ltiRegistrationRepository = null,
        ?LtiPlatformRepository $ltiPlatformRepository = null,
        ?LtiToolRepository $ltiToolRepository = null,
        ?LtiRegistrationHydratorCollection $hydratorCollection = null,
        ?CoreKeyChainGenerator $coreKeyChainGenerator = null,
        ?Logger $logger = null,
    ): RegistrationRepository {
        return new RegistrationRepository(
            registrationFactory: new RegistrationFactory(),
            ltiRegistrationRepository: $ltiRegistrationRepository
                ?? $this->createMock(LtiRegistrationRepository::class),
            ltiPlatformRepository: $ltiPlatformRepository ?? $this->createMock(LtiPlatformRepository::class),
            ltiToolRepository: $ltiToolRepository ?? $this->createMock(LtiToolRepository::class),
            hydratorCollection: $hydratorCollection ?? $this->createMock(LtiRegistrationHydratorCollection::class),
            coreKeyChainGenerator: $coreKeyChainGenerator ?? $this->createMock(CoreKeyChainGenerator::class),
            logger: $logger ?? $this->createMock(Logger::class),
        );
    }

    public function testInstanceOfRegistrationInterface(): void
    {
        $registration = $this->getRegistrationRepository();
        $this->assertInstanceOf(RegistrationRepositoryInterface::class, $registration);
    }

    public function testFind(): void
    {
        $registration1 = LtiRegistrationMother::create(clientId: 'registration1');
        $tool1 = LtiToolMother::create(registrationId: $registration1->getId(), name: 'Tool 1');
        $platform1 = LtiPlatformMother::create(registrationId: $registration1->getId(), name: 'Platform 1');
        $deployment1 = LtiDeploymentMother::create(
            registrationId: $registration1->getId(),
            deploymentId: 'deployment-1'
        );

        $ltiRegistrationRepository = $this->createMock(LtiRegistrationRepository::class);
        $ltiRegistrationRepository->expects($this->once())
            ->method('findOneBy')
            ->willReturn($registration1);

        $hydratorCollection = $this->createMock(LtiRegistrationHydratorCollection::class);
        $hydratorCollection->expects($this->once())
            ->method('hydrate')
            ->willReturnCallback(
                function (
                    LtiRegistrationCollection $collection,
                    LtiRegistrationHydrationCriteria $criteria
                ) use (
                    $tool1,
                    $platform1,
                    $deployment1
                ) {
                    foreach ($collection->all() as $registration) {
                        $registration->setTool($tool1)
                            ->setPlatform($platform1)
                            ->setDeployments(new LtiDeploymentCollection([$deployment1]));
                    }
                }
            );

        $repository = $this->getRegistrationRepository(
            ltiRegistrationRepository: $ltiRegistrationRepository,
            hydratorCollection: $hydratorCollection,
        );

        $coreRegistration = $repository->find($registration1->getId()->value());

        $this->assertEquals($registration1->getId(), $coreRegistration->getIdentifier());
        $this->assertEquals($registration1->getClientId(), $coreRegistration->getClientId());
        $this->assertEquals(new CoreTool(
            identifier: $tool1->getId()->value(),
            name: 'Tool 1',
            audience: 'EasyLearning',
            oidcInitiationUrl: 'https://example.com/lti1p3/oidc/initiation',
            launchUrl: 'https://example.com/lti1p3/launch',
            deepLinkingUrl: 'https://example.com/lti1p3/deep-linking',
        ), $coreRegistration->getTool());
        $this->assertEquals(new CorePlatform(
            identifier: $platform1->getId()->value(),
            name: 'Platform 1',
            audience: 'EasyLearning',
            oidcAuthenticationUrl: 'https://example.com/lti1p3/oidc/authentication',
            oAuth2AccessTokenUrl: 'https://example.com/lti1p3/auth/platformKey/token',
        ), $coreRegistration->getPlatform());
        $this->assertEquals(['deployment-1'], $coreRegistration->getDeploymentIds());
    }

    public function testFindAll(): void
    {
        $registration1 = LtiRegistrationMother::create(clientId: 'registration1');
        $tool1 = LtiToolMother::create(registrationId: $registration1->getId(), name: 'Tool 1');
        $platform1 = LtiPlatformMother::create(registrationId: $registration1->getId(), name: 'Platform 1');
        $deployment1 = LtiDeploymentMother::create(
            registrationId: $registration1->getId(),
            deploymentId: 'deployment-1'
        );

        $ltiRegistrationRepository = $this->createMock(LtiRegistrationRepository::class);
        $ltiRegistrationRepository->expects($this->once())
            ->method('findBy')
            ->willReturn(new LtiRegistrationCollection([$registration1]));

        $hydratorCollection = $this->createMock(LtiRegistrationHydratorCollection::class);
        $hydratorCollection->expects($this->once())
            ->method('hydrate')
            ->willReturnCallback(
                function (
                    LtiRegistrationCollection $collection,
                    LtiRegistrationHydrationCriteria $criteria
                ) use (
                    $tool1,
                    $platform1,
                    $deployment1
                ) {
                    foreach ($collection->all() as $registration) {
                        $registration->setTool($tool1)
                            ->setPlatform($platform1)
                            ->setDeployments(new LtiDeploymentCollection([$deployment1]));
                    }
                }
            );

        $repository = $this->getRegistrationRepository(
            ltiRegistrationRepository: $ltiRegistrationRepository,
            hydratorCollection: $hydratorCollection,
        );

        $result = $repository->findAll();
        $this->assertCount(1, $result);
        $coreRegistration = $result[0];
        $this->assertEquals($registration1->getId(), $coreRegistration->getIdentifier());
        $this->assertEquals($registration1->getClientId(), $coreRegistration->getClientId());
        $this->assertEquals(new CoreTool(
            identifier: $tool1->getId()->value(),
            name: 'Tool 1',
            audience: 'EasyLearning',
            oidcInitiationUrl: 'https://example.com/lti1p3/oidc/initiation',
            launchUrl: 'https://example.com/lti1p3/launch',
            deepLinkingUrl: 'https://example.com/lti1p3/deep-linking',
        ), $coreRegistration->getTool());
        $this->assertEquals(new CorePlatform(
            identifier: $platform1->getId()->value(),
            name: 'Platform 1',
            audience: 'EasyLearning',
            oidcAuthenticationUrl: 'https://example.com/lti1p3/oidc/authentication',
            oAuth2AccessTokenUrl: 'https://example.com/lti1p3/auth/platformKey/token',
        ), $coreRegistration->getPlatform());
        $this->assertEquals(['deployment-1'], $coreRegistration->getDeploymentIds());
    }

    public function testFindByClientId(): void
    {
        $registration1 = LtiRegistrationMother::create(clientId: 'registration1');
        $tool1 = LtiToolMother::create(registrationId: $registration1->getId(), name: 'Tool 1');
        $platform1 = LtiPlatformMother::create(registrationId: $registration1->getId(), name: 'Platform 1');
        $deployment1 = LtiDeploymentMother::create(
            registrationId: $registration1->getId(),
            deploymentId: 'deployment-1'
        );

        $ltiRegistrationRepository = $this->createMock(LtiRegistrationRepository::class);
        $ltiRegistrationRepository->expects($this->once())
            ->method('findOneBy')
            ->willReturn($registration1);

        $hydratorCollection = $this->createMock(LtiRegistrationHydratorCollection::class);
        $hydratorCollection->expects($this->once())
            ->method('hydrate')
            ->willReturnCallback(
                function (
                    LtiRegistrationCollection $collection,
                    LtiRegistrationHydrationCriteria $criteria
                ) use (
                    $tool1,
                    $platform1,
                    $deployment1
                ) {
                    foreach ($collection->all() as $registration) {
                        $registration->setTool($tool1)
                            ->setPlatform($platform1)
                            ->setDeployments(new LtiDeploymentCollection([$deployment1]));
                    }
                }
            );

        $repository = $this->getRegistrationRepository(
            ltiRegistrationRepository: $ltiRegistrationRepository,
            hydratorCollection: $hydratorCollection,
        );

        $coreRegistration = $repository->findByClientId($registration1->getClientId());

        $this->assertEquals($registration1->getId(), $coreRegistration->getIdentifier());
        $this->assertEquals($registration1->getClientId(), $coreRegistration->getClientId());
        $this->assertEquals(new CoreTool(
            identifier: $tool1->getId()->value(),
            name: 'Tool 1',
            audience: 'EasyLearning',
            oidcInitiationUrl: 'https://example.com/lti1p3/oidc/initiation',
            launchUrl: 'https://example.com/lti1p3/launch',
            deepLinkingUrl: 'https://example.com/lti1p3/deep-linking',
        ), $coreRegistration->getTool());
        $this->assertEquals(new CorePlatform(
            identifier: $platform1->getId()->value(),
            name: 'Platform 1',
            audience: 'EasyLearning',
            oidcAuthenticationUrl: 'https://example.com/lti1p3/oidc/authentication',
            oAuth2AccessTokenUrl: 'https://example.com/lti1p3/auth/platformKey/token',
        ), $coreRegistration->getPlatform());
        $this->assertEquals(['deployment-1'], $coreRegistration->getDeploymentIds());
    }

    public function testFindByPlatformIssuer(): void
    {
        $registration1 = LtiRegistrationMother::create(clientId: 'registration-1');

        $tool1 = LtiToolMother::create(registrationId: $registration1->getId(), name: 'Tool 1');
        $platform1 = LtiPlatformMother::create(registrationId: $registration1->getId(), name: 'Platform 1');
        $deployment1 = LtiDeploymentMother::create(
            registrationId: $registration1->getId(),
            deploymentId: 'deployment-1'
        );

        $platformRepository = $this->createMock(LtiPlatformRepository::class);
        $platformRepository->expects($this->exactly(2))
            ->method('findOneBy')
            ->willReturn($platform1);

        $ltiRegistrationRepository = $this->createMock(LtiRegistrationRepository::class);
        $ltiRegistrationRepository->expects($this->exactly(2))
            ->method('findOneBy')
            ->willReturn($registration1);

        $hydratorCollection = $this->createMock(LtiRegistrationHydratorCollection::class);
        $hydratorCollection->expects($this->exactly(2))
            ->method('hydrate')
            ->willReturnCallback(
                function (
                    LtiRegistrationCollection $collection,
                    LtiRegistrationHydrationCriteria $criteria
                ) use (
                    $tool1,
                    $platform1,
                    $deployment1
                ) {
                    foreach ($collection->all() as $registration) {
                        $registration->setTool($tool1)
                            ->setPlatform($platform1)
                            ->setDeployments(new LtiDeploymentCollection([$deployment1]));
                    }
                }
            );

        $repository = $this->getRegistrationRepository(
            ltiRegistrationRepository: $ltiRegistrationRepository,
            ltiPlatformRepository: $platformRepository,
            hydratorCollection: $hydratorCollection,
        );

        $coreRegistration = $repository->findByPlatformIssuer($platform1->getAudience());

        $this->assertEquals($registration1->getId(), $coreRegistration->getIdentifier());
        $this->assertEquals($registration1->getClientId(), $coreRegistration->getClientId());
        $this->assertEquals(new CoreTool(
            identifier: $tool1->getId()->value(),
            name: 'Tool 1',
            audience: 'EasyLearning',
            oidcInitiationUrl: 'https://example.com/lti1p3/oidc/initiation',
            launchUrl: 'https://example.com/lti1p3/launch',
            deepLinkingUrl: 'https://example.com/lti1p3/deep-linking',
        ), $coreRegistration->getTool());
        $this->assertEquals(new CorePlatform(
            identifier: $platform1->getId()->value(),
            name: 'Platform 1',
            audience: 'EasyLearning',
            oidcAuthenticationUrl: 'https://example.com/lti1p3/oidc/authentication',
            oAuth2AccessTokenUrl: 'https://example.com/lti1p3/auth/platformKey/token',
        ), $coreRegistration->getPlatform());
        $this->assertEquals(['deployment-1'], $coreRegistration->getDeploymentIds());

        $coreRegistration = $repository->findByPlatformIssuer($platform1->getAudience(), $registration1->getClientId());
        $this->assertEquals($registration1->getId(), $coreRegistration->getIdentifier());
        $this->assertEquals($registration1->getClientId(), $coreRegistration->getClientId());
        $this->assertEquals(new CoreTool(
            identifier: $tool1->getId()->value(),
            name: 'Tool 1',
            audience: 'EasyLearning',
            oidcInitiationUrl: 'https://example.com/lti1p3/oidc/initiation',
            launchUrl: 'https://example.com/lti1p3/launch',
            deepLinkingUrl: 'https://example.com/lti1p3/deep-linking',
        ), $coreRegistration->getTool());
        $this->assertEquals(new CorePlatform(
            identifier: $platform1->getId()->value(),
            name: 'Platform 1',
            audience: 'EasyLearning',
            oidcAuthenticationUrl: 'https://example.com/lti1p3/oidc/authentication',
            oAuth2AccessTokenUrl: 'https://example.com/lti1p3/auth/platformKey/token',
        ), $coreRegistration->getPlatform());
        $this->assertEquals(['deployment-1'], $coreRegistration->getDeploymentIds());
    }

    public function testFindByToolIssuer(): void
    {
        $registration1 = LtiRegistrationMother::create(clientId: 'registration-1');

        $tool1 = LtiToolMother::create(registrationId: $registration1->getId(), name: 'Tool 1');
        $platform1 = LtiPlatformMother::create(registrationId: $registration1->getId(), name: 'Platform 1');
        $deployment1 = LtiDeploymentMother::create(
            registrationId: $registration1->getId(),
            deploymentId: 'deployment-1'
        );

        $toolRepository = $this->createMock(LtiToolRepository::class);
        $toolRepository->expects($this->exactly(2))
            ->method('findOneBy')
            ->willReturn($tool1);

        $ltiRegistrationRepository = $this->createMock(LtiRegistrationRepository::class);
        $ltiRegistrationRepository->expects($this->exactly(2))
            ->method('findOneBy')
            ->willReturn($registration1);

        $hydratorCollection = $this->createMock(LtiRegistrationHydratorCollection::class);
        $hydratorCollection->expects($this->exactly(2))
            ->method('hydrate')
            ->willReturnCallback(
                function (
                    LtiRegistrationCollection $collection,
                    LtiRegistrationHydrationCriteria $criteria
                ) use (
                    $tool1,
                    $platform1,
                    $deployment1
                ) {
                    foreach ($collection->all() as $registration) {
                        $registration->setTool($tool1)
                            ->setPlatform($platform1)
                            ->setDeployments(new LtiDeploymentCollection([$deployment1]));
                    }
                }
            );

        $repository = $this->getRegistrationRepository(
            ltiRegistrationRepository: $ltiRegistrationRepository,
            ltiToolRepository: $toolRepository,
            hydratorCollection: $hydratorCollection,
        );

        $coreRegistration = $repository->findByToolIssuer($tool1->getAudience());

        $this->assertEquals($registration1->getId(), $coreRegistration->getIdentifier());
        $this->assertEquals($registration1->getClientId(), $coreRegistration->getClientId());
        $this->assertEquals(new CoreTool(
            identifier: $tool1->getId()->value(),
            name: 'Tool 1',
            audience: 'EasyLearning',
            oidcInitiationUrl: 'https://example.com/lti1p3/oidc/initiation',
            launchUrl: 'https://example.com/lti1p3/launch',
            deepLinkingUrl: 'https://example.com/lti1p3/deep-linking',
        ), $coreRegistration->getTool());
        $this->assertEquals(new CorePlatform(
            identifier: $platform1->getId()->value(),
            name: 'Platform 1',
            audience: 'EasyLearning',
            oidcAuthenticationUrl: 'https://example.com/lti1p3/oidc/authentication',
            oAuth2AccessTokenUrl: 'https://example.com/lti1p3/auth/platformKey/token',
        ), $coreRegistration->getPlatform());
        $this->assertEquals(['deployment-1'], $coreRegistration->getDeploymentIds());

        $coreRegistration = $repository->findByToolIssuer($tool1->getAudience(), $registration1->getClientId());
        $this->assertEquals($registration1->getId(), $coreRegistration->getIdentifier());
        $this->assertEquals($registration1->getClientId(), $coreRegistration->getClientId());
        $this->assertEquals(new CoreTool(
            identifier: $tool1->getId()->value(),
            name: 'Tool 1',
            audience: 'EasyLearning',
            oidcInitiationUrl: 'https://example.com/lti1p3/oidc/initiation',
            launchUrl: 'https://example.com/lti1p3/launch',
            deepLinkingUrl: 'https://example.com/lti1p3/deep-linking',
        ), $coreRegistration->getTool());
        $this->assertEquals(new CorePlatform(
            identifier: $platform1->getId()->value(),
            name: 'Platform 1',
            audience: 'EasyLearning',
            oidcAuthenticationUrl: 'https://example.com/lti1p3/oidc/authentication',
            oAuth2AccessTokenUrl: 'https://example.com/lti1p3/auth/platformKey/token',
        ), $coreRegistration->getPlatform());
        $this->assertEquals(['deployment-1'], $coreRegistration->getDeploymentIds());
    }
}
