<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\LTI;

use App\Tests\V2\Domain\LTI\LtiRegistrationRepositoryTestCase;
use App\Tests\V2\Infrastructure\DBAL\DBALConnectionFactory;
use App\V2\Domain\LTI\LtiRegistrationRepository;
use App\V2\Infrastructure\LTI\DBALLtiRegistrationRepository;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Schema\SchemaException;
use Doctrine\ORM\EntityManager;

class DBALLtiRegistrationRepositoryTest extends LtiRegistrationRepositoryTestCase
{
    private const string TABLE_NAME = 'lti_registration';
    private Connection $connection;

    /**
     * @throws \PHPUnit\Framework\MockObject\Exception
     * @throws SchemaException
     * @throws Exception
     */
    protected function getRepository(): LtiRegistrationRepository
    {
        /**
         * @var Schema $schema
         */
        [$this->connection, $schema] = DBALConnectionFactory::create();

        if ($schema->hasTable(self::TABLE_NAME)) {
            $this->dropTable();
        }

        $table = $schema->createTable(self::TABLE_NAME);
        $table->addColumn('id', 'string');
        $table->addColumn('name', 'string');
        $table->addColumn('client_id', 'string');

        $table->setPrimaryKey(['id']);
        $table->addUniqueConstraint(['client_id'], 'INX_LTI_REGISTRATION_CLIENT_ID_UNIQUE');

        foreach ($this->connection->getDatabasePlatform()->getCreateTableSQL($table) as $sql) {
            $this->connection->executeStatement($sql);
        }

        $em = $this->createMock(EntityManager::class);
        $em->method('getConnection')
            ->willReturn($this->connection);

        return new DBALLtiRegistrationRepository(
            em: $em,
            ltiRegistrationTableName: self::TABLE_NAME,
        );
    }

    /**
     * @throws Exception
     */
    private function dropTable(): void
    {
        $this->connection->executeStatement('DROP TABLE IF EXISTS `' . self::TABLE_NAME . '`');
    }

    /**
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->dropTable();

        parent::tearDown();
    }
}
