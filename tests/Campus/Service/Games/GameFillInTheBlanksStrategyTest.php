<?php

declare(strict_types=1);

namespace App\Tests\Campus\Service\Games;

use App\Campus\Games\FillInTheBlanks;
use App\Campus\Games\GameStrategyInterface;
use App\Entity\Chapter;
use App\Entity\ChapterType;
use Doctrine\ORM\EntityManagerInterface;

class GameFillInTheBlanksStrategyTest extends TestGameStrategyInterface
{
    public function getStrategy(): GameStrategyInterface
    {
        return new FillInTheBlanks($this->createMock(EntityManagerInterface::class));
    }

    public static function getValidData(): \Generator
    {
        $chapter = new Chapter();
        $chapterType = new ChapterType();
        $chapterType->setCode('999');
        $chapterType->setPercentageCompleted('0.75');
        $chapter->setType($chapterType);

        $answers = [
            [
                'id' => 1,
                'questionId' => 1,
                'time' => 10,
                'correct' => false,
                'attempts' => [
                    [
                        'id' => 1,
                        'word' => 'guau.',
                        'correct' => 0,
                    ],
                    [
                        'id' => 2,
                        'word' => 'guau.',
                        'correct' => 1,
                    ],
                    [
                        'id' => 1,
                        'word' => 'miau,',
                        'correct' => 1,
                    ],
                    [
                        'id' => 2,
                        'word' => 'guau.',
                        'correct' => 1,
                    ],
                ],
            ],
        ];

        $attempts = [
            [
                'id' => 1,
                'questionId' => 1,
                'time' => 5,
                'correct' => true,
            ],
            [
                'id' => 2,
                'questionId' => 2,
                'time' => 2,
                'correct' => true,
            ],
        ];

        yield 'result ko, no chapter passed' => [
            'data' => ['answers' => $answers,
                'holes' => 2,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => null,
            'expectedPoints' => null,
        ];

        yield 'result ko, no total time passed' => [
            'data' => ['answers' => $answers,
                'holes' => 2,
                'timeTotal' => null,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0,
        ];

        yield 'result ko, no answers passed' => [
            'data' => ['answers' => null,
                'holes' => 2,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0,
        ];

        yield 'result ok' => [
            'data' => ['answers' => $answers,
                'holes' => 2,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => null,
            ],
            'args' => $chapter,
            'expectedPoints' => 0.92,
        ];

        yield 'result ok 4 attempts per answer. With 2 attempts' => [
            'data' => ['answers' => $answers,
                'holes' => 2,
                'totalQuestions' => 2,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0.83,
        ];

        $attempts = [
            [
                'id' => 1,
                'questionId' => 1,
                'time' => 5,
                'correct' => true,
            ],
            [
                'id' => 2,
                'questionId' => 2,
                'time' => 2,
                'correct' => true,
            ],
            [
                'id' => 3,
                'questionId' => 1,
                'time' => 5,
                'correct' => true,
            ],
            [
                'id' => 4,
                'questionId' => 2,
                'time' => 2,
                'correct' => true,
            ],
        ];

        yield 'result ok 2 correct answers and less time. With 4 attempts' => [
            'data' => ['answers' => $answers,
                'holes' => 2,
                'totalQuestions' => 2,
                'timeTotal' => 30,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0.64,
            1];
    }
}
