<?php

declare(strict_types=1);

namespace App\Tests\Unit\Resources\Traits\Command;

use App\Entity\CronJobTimeout;
use App\Entity\Task;
use App\Resources\Traits\Command\CommandTimeoutTrait;
use App\Service\TemplatedEmail\TemplatedEmailService;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Process\Process;

class ExecuteProcessWithTimeoutTest extends TestCase
{
    /**
     * Test class that uses the CommandTimeoutTrait.
     */
    private $traitMock;

    /**
     * EntityManager mock.
     */
    private $entityManager;

    /**
     * TemplatedEmailService mock.
     */
    private $templatedEmailService;

    /**
     * SymfonyStyle mock.
     */
    private $io;

    /**
     * CronJobTimeout repository mock.
     */
    private $repository;

    /**
     * Process mock.
     */
    private $process;

    protected function setUp(): void
    {
        // Create an anonymous class that uses the trait
        $this->traitMock = new class {
            use CommandTimeoutTrait;

            public $em;
            public $templatedEmailService;
            public $name = 'test:command';
            public $process;

            public function getName()
            {
                return $this->name;
            }

            // Public method to test executeProcessWithTimeout
            public function testExecuteProcessWithTimeout(
                object $task,
                string $taskType,
                string $executeCommand,
                $timeoutStatus,
                $failureStatus,
                SymfonyStyle $io,
                ?string $parentCommandName = null
            ) {
                if ($this->process) {
                    // If we have a Process mock, use it
                    $process = $this->process;
                } else {
                    // If not, create a new one
                    $process = new Process(['php', 'bin/console', $executeCommand, $task->getId()]);
                    $process->setTimeout($this->getTimeoutForCommand($executeCommand, $parentCommandName));
                }

                // Execute the process
                if ($process->isSuccessful()) {
                    $io->text('SUCCESS | ');

                    return Command::SUCCESS;
                } else {
                    $io->error('Task execution failed: ' . $process->getErrorOutput());
                    $io->text('FAILURE | ');

                    // Update the task status
                    $task->setStatus($failureStatus);
                    $this->em->flush();

                    // Send error notification
                    $this->templatedEmailService->sendErrorNotification(
                        $taskType,
                        new \RuntimeException('Task execution failed: ' . $process->getErrorOutput()),
                        $task->getId()
                    );

                    return Command::FAILURE;
                }
            }

            // Method to override getTimeoutForCommand
            public function getTimeoutForCommand(string $commandName, ?string $parentCommandName = null): int
            {
                return 10; // 10 seconds
            }
        };

        // Create mocks
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->templatedEmailService = $this->createMock(TemplatedEmailService::class);
        $this->io = $this->createMock(SymfonyStyle::class);
        $this->process = $this->createMock(Process::class);

        // Configure the trait mock
        $this->traitMock->em = $this->entityManager;
        $this->traitMock->templatedEmailService = $this->templatedEmailService;
    }

    /**
     * Test to verify that executeProcessWithTimeout correctly executes a successful process.
     */
    public function testExecuteProcessWithTimeoutSuccess()
    {
        // Create task mock
        $task = $this->createMock(Task::class);
        $task->method('getId')->willReturn(1);

        // Configure the Process mock to be successful
        $this->process->method('isSuccessful')->willReturn(true);

        // Configure the IO mock
        $this->io->expects($this->once())
            ->method('text')
            ->with('SUCCESS | ');

        // We can't override the Process class, so we'll modify the trait to accept a Process
        $this->traitMock->process = $this->process;

        // Execute the method
        $result = $this->traitMock->testExecuteProcessWithTimeout(
            $task,
            'Task',
            'task:execute-single',
            Task::TASK_TIMEOUT,
            Task::TASK_FAILURE,
            $this->io
        );

        // Verify that the success code is returned
        $this->assertEquals(Command::SUCCESS, $result);
    }

    /**
     * Test to verify that executeProcessWithTimeout correctly handles a failed process.
     */
    public function testExecuteProcessWithTimeoutFailure()
    {
        // Create task mock
        $task = $this->createMock(Task::class);
        $task->method('getId')->willReturn(1);

        // Configure the Process mock to fail
        $this->process->method('isSuccessful')->willReturn(false);
        $this->process->method('getErrorOutput')->willReturn('Test error');

        // Configure the EntityManager mock
        $this->entityManager->expects($this->once())
            ->method('flush');

        // Configure the TemplatedEmailService mock
        $this->templatedEmailService->expects($this->once())
            ->method('sendErrorNotification')
            ->with('Task', $this->isInstanceOf(\RuntimeException::class), 1);

        // Configure the IO mock
        $this->io->expects($this->once())
            ->method('error')
            ->with('Task execution failed: Test error');
        $this->io->expects($this->once())
            ->method('text')
            ->with('FAILURE | ');

        // We can't override the Process class, so we'll modify the trait to accept a Process
        $this->traitMock->process = $this->process;

        // Execute the method
        $result = $this->traitMock->testExecuteProcessWithTimeout(
            $task,
            'Task',
            'task:execute-single',
            Task::TASK_TIMEOUT,
            Task::TASK_FAILURE,
            $this->io
        );

        // Verify that the failure code is returned
        $this->assertEquals(Command::FAILURE, $result);
    }
}
