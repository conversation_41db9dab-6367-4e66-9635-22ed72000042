<?php

declare(strict_types=1);

namespace App\Tests\Unit\Service\ZipFileTask;

use App\Entity\User;
use App\Entity\ZipFileTask;
use App\Exception\TaskLimitExceededException;
use App\Service\SettingsService;
use App\Service\SlotManagerService;
use App\Service\TaskCron\ExecutionSlot;
use App\Service\TaskLimitService;
use App\Service\TemplatedEmail\TemplatedEmailService;
use App\Service\ZipFileTask\ZipFileTaskService;
use Doctrine\ORM\AbstractQuery;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\QueryBuilder;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\Translation\TranslatorInterface;

class ZipFileTaskServiceTest extends TestCase
{
    private $em;
    private $taskLimitService;
    private $security;
    private $logger;
    private $templatedEmailService;
    private $zipFileTaskRepository;
    private $zipFileTaskService;
    private $translator;
    private $settings;
    private $slotManagerService;

    protected function setUp(): void
    {
        $this->em = $this->createMock(EntityManagerInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->translator = $this->createMock(TranslatorInterface::class);
        $this->settings = $this->createMock(SettingsService::class);
        $this->taskLimitService = $this->createMock(TaskLimitService::class);
        $this->security = $this->createMock(Security::class);
        $this->templatedEmailService = $this->createMock(TemplatedEmailService::class);
        $this->zipFileTaskRepository = $this->createMock(\App\Repository\ZipFileTaskRepository::class);

        // Mock the newZipFileTask method to return a ZipFileTask with the expected properties
        $this->zipFileTaskRepository->method('newZipFileTask')
            ->willReturnCallback(function (
                string $task,
                $entityId = null,
                ?string $type = null,
                array $params = [],
                ?string $originalName = null,
                ?object $user = null
            ) {
                $zipFileTask = new ZipFileTask();
                $zipFileTask->setTask($task);
                $zipFileTask->setEntityId($entityId);
                $zipFileTask->setType($type);
                $zipFileTask->setParams(['params' => $params]);
                $zipFileTask->setOriginalName($originalName);
                if ($user) {
                    $zipFileTask->setCreatedBy($user);
                }

                return $zipFileTask;
            });

        $this->slotManagerService = $this->createMock(SlotManagerService::class);

        $this->em
            ->method('getRepository')
            ->willReturnCallback(function ($entityClass) {
                if (ZipFileTask::class === $entityClass) {
                    return $this->zipFileTaskRepository;
                }

                return null;
            });

        $this->zipFileTaskService = new ZipFileTaskService(
            $this->em,
            $this->logger,
            $this->translator,
            $this->settings,
            $this->taskLimitService,
            $this->slotManagerService
        );
    }

    public function testEnqueueZipFileTaskSuccess(): void
    {
        $user = $this->createMock(User::class);
        $user->method('getId')->willReturn(1);

        // Configure TaskLimitService
        $this->taskLimitService->method('getLimit')->willReturn(5);

        $queryBuilder = $this->createMock(QueryBuilder::class);
        $query = $this->createMock(AbstractQuery::class);

        $queryBuilder->method('select')->willReturnSelf();
        $queryBuilder->method('where')->willReturnSelf();
        $queryBuilder->method('andWhere')->willReturnSelf();
        $queryBuilder->method('setParameter')->willReturnSelf();
        $queryBuilder->method('getQuery')->willReturn($query);
        $query->method('getSingleScalarResult')->willReturn(0);

        $this->zipFileTaskRepository->method('createQueryBuilder')->willReturn($queryBuilder);

        // We don't need to expect persist and flush here because they are called inside the repository's newZipFileTask method

        // Execute the method being tested
        $result = $this->zipFileTaskService->enqueueZipFileTask(
            $user,
            'test-task',
            ['param1' => 'value1'],
            'test-type',
            'test-file.zip',
            '123'
        );

        // Assert the result
        $this->assertInstanceOf(ZipFileTask::class, $result);
        $this->assertEquals('test-task', $result->getTask());
        $this->assertEquals('123', $result->getEntityId());
        $this->assertEquals('test-type', $result->getType());
        $this->assertEquals(['params' => ['param1' => 'value1']], $result->getParams());
        $this->assertEquals('test-file.zip', $result->getOriginalName());
    }

    public function testEnqueueZipFileTaskWithNullUser(): void
    {
        // We shouldn't validate limit for null user
        $this->zipFileTaskRepository
            ->expects($this->never())
            ->method('createQueryBuilder');

        // We don't need to expect persist and flush here because they are called inside the repository's newZipFileTask method

        // Execute the method being tested
        $result = $this->zipFileTaskService->enqueueZipFileTask(
            null,
            'test-task',
            ['param1' => 'value1'],
            'test-type',
            'test-file.zip',
            '123'
        );

        // Assert the result
        $this->assertInstanceOf(ZipFileTask::class, $result);
        $this->assertEquals('test-task', $result->getTask());
    }

    public function testEnqueueZipFileTaskLimitExceeded(): void
    {
        $user = $this->createMock(User::class);
        $user->method('getId')->willReturn(1);
        $user->method('getFirstName')->willReturn('Test User');

        // Configure TaskLimitService
        $this->taskLimitService->method('getLimit')->willReturn(5);

        $queryBuilder = $this->createMock(QueryBuilder::class);
        $query = $this->createMock(AbstractQuery::class);

        $queryBuilder->method('select')->willReturnSelf();
        $queryBuilder->method('where')->willReturnSelf();
        $queryBuilder->method('andWhere')->willReturnSelf();
        $queryBuilder->method('setParameter')->willReturnSelf();
        $queryBuilder->method('getQuery')->willReturn($query);
        $query->method('getSingleScalarResult')->willReturn(5);

        $this->zipFileTaskRepository->method('createQueryBuilder')->willReturn($queryBuilder);
        $this->zipFileTaskRepository->method('countPendingZipFileTasksByUser')->willReturn(5);

        $this->translator
            ->method('trans')
            ->willReturn('User Test User has reached the limit of 5 pending tasks');

        $this->expectException(TaskLimitExceededException::class);
        $this->expectExceptionMessage('User Test User has reached the limit of 5 pending tasks');

        $this->zipFileTaskService->enqueueZipFileTask(
            $user,
            'test-task',
            ['param1' => 'value1'],
            'test-type',
            'test-file.zip',
            '123'
        );
    }

    public function testCountPendingZipFileTasksHandlesException(): void
    {
        $user = $this->createMock(User::class);
        $user->method('getId')->willReturn(1);
        $user->method('getFirstName')->willReturn('Test User');

        // Configure the repository to throw an exception when counting pending tasks
        $this->zipFileTaskRepository->method('countPendingZipFileTasksByUser')
            ->willThrowException(new \Exception('Database error'));

        // Expect the logger to be called with an error message
        $this->logger
            ->expects($this->once())
            ->method('error')
            ->with($this->stringContains('countPendingZipFileTasksByUser failed'));

        // Expect the exception to be re-thrown
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        // Call the method that should trigger the exception
        $this->zipFileTaskService->enqueueZipFileTask(
            $user,
            'test-task',
            ['param1' => 'value1'],
            'test-type',
            'test-file.zip',
            '123'
        );
    }

    public function testGetNextTaskWithLongRunningTasksAllowed(): void
    {
        $task = new ZipFileTask();
        $task->setTask('test-task');
        $task->setStatus(ZipFileTask::STATUS_PENDING);

        // Configurar el mock de SlotManagerService para devolver un ExecutionSlot
        $executionSlot = new ExecutionSlot(true);
        $this->slotManagerService->method('getAvailableZipTaskExecutionSlot')->willReturn($executionSlot);

        // Configure main query builder
        $queryBuilder = $this->createMock(QueryBuilder::class);
        $query = $this->createMock(AbstractQuery::class);

        $this->zipFileTaskRepository
            ->expects($this->once())
            ->method('createQueryBuilder')
            ->willReturn($queryBuilder);

        $queryBuilder
            ->expects($this->once())
            ->method('where')
            ->with('z.status = :status')
            ->willReturnSelf();

        $queryBuilder
            ->expects($this->once())
            ->method('andWhere')
            ->with('z.deletedAt IS NULL')
            ->willReturnSelf();

        $queryBuilder
            ->expects($this->once())
            ->method('setParameter')
            ->with('status', ZipFileTask::STATUS_PENDING)
            ->willReturnSelf();

        $queryBuilder
            ->expects($this->once())
            ->method('orderBy')
            ->with('z.createdAt', 'ASC')
            ->willReturnSelf();

        $queryBuilder
            ->expects($this->once())
            ->method('setMaxResults')
            ->with(1)
            ->willReturnSelf();

        $queryBuilder
            ->expects($this->once())
            ->method('getQuery')
            ->willReturn($query);

        $query
            ->expects($this->once())
            ->method('getOneOrNullResult')
            ->willReturn($task);

        $result = $this->zipFileTaskService->getNextTask(true);
        $this->assertSame($task, $result);
    }

    public function testGetNextTaskWithLongRunningTasksNotAllowed(): void
    {
        $task = new ZipFileTask();
        $task->setTask('test-task');
        $task->setStatus(ZipFileTask::STATUS_PENDING);

        // Configurar el mock de SlotManagerService para devolver un ExecutionSlot
        $executionSlot = new ExecutionSlot(false);
        $this->slotManagerService->method('getAvailableZipTaskExecutionSlot')->willReturn($executionSlot);
        $this->slotManagerService->method('getZipTaskLongRunningTypes')->willReturn(['export-file']);

        // Configure main query builder
        $queryBuilder = $this->createMock(QueryBuilder::class);
        $query = $this->createMock(AbstractQuery::class);

        $this->zipFileTaskRepository
            ->expects($this->once())
            ->method('createQueryBuilder')
            ->willReturn($queryBuilder);

        $queryBuilder
            ->expects($this->once())
            ->method('where')
            ->with('z.status = :status')
            ->willReturnSelf();

        $queryBuilder
            ->method('andWhere')
            ->willReturnSelf();

        // Verificamos que se llama a andWhere con los parámetros correctos
        $queryBuilder
            ->expects($this->exactly(2))
            ->method('andWhere')
            ->willReturnSelf();

        $queryBuilder
            ->method('setParameter')
            ->willReturnSelf();

        // Verificamos que se llama a setParameter con los parámetros correctos
        $queryBuilder
            ->expects($this->exactly(2))
            ->method('setParameter')
            ->willReturnSelf();

        $queryBuilder
            ->expects($this->once())
            ->method('orderBy')
            ->with('z.createdAt', 'ASC')
            ->willReturnSelf();

        $queryBuilder
            ->expects($this->once())
            ->method('setMaxResults')
            ->with(1)
            ->willReturnSelf();

        $queryBuilder
            ->expects($this->once())
            ->method('getQuery')
            ->willReturn($query);

        $query
            ->expects($this->once())
            ->method('getOneOrNullResult')
            ->willReturn($task);

        $result = $this->zipFileTaskService->getNextTask(false);
        $this->assertSame($task, $result);
    }
}
