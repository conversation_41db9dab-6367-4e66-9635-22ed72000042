<?php

declare(strict_types=1);

namespace App\Tests\Unit\Service\Diploma;

use App\Service\Annoucement\Admin\AnnouncementConfigurationsService;
use App\Service\DateFormatter\DateFormatterService;
use App\Service\Diploma\DiplomaFactory;
use App\Service\Diploma\Strategy\DhlStrategy;
use App\Service\Diploma\Strategy\EasylearningStrategy;
use App\Service\Diploma\Strategy\FundaeStrategy;
use App\Service\Diploma\Strategy\HobetuzStrategy;
use App\Service\Diploma\Strategy\NovomaticStrategy;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class DiplomaFactoryTest extends TestCase
{
    private EntityManagerInterface|MockObject $entityManager;
    private SettingsService|MockObject $settingsService;
    private AnnouncementConfigurationsService|MockObject $announcementConfigurationsService;
    private DateFormatterService|MockObject $dateFormatterService;
    private DiplomaFactory $diplomaFactory;

    protected function setUp(): void
    {
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->settingsService = $this->createMock(SettingsService::class);
        $this->announcementConfigurationsService = $this->createMock(AnnouncementConfigurationsService::class);
        $this->dateFormatterService = $this->createMock(DateFormatterService::class);

        $this->diplomaFactory = new DiplomaFactory(
            $this->entityManager,
            $this->settingsService,
            $this->announcementConfigurationsService,
            $this->dateFormatterService
        );
    }

    /**
     * Test that all expected strategies are properly registered.
     *
     * @dataProvider strategyProvider
     */
    public function testGetStrategyReturnsCorrectInstance(string $strategyName, string $expectedClass): void
    {
        // Act
        $strategy = $this->diplomaFactory->getStrategy($strategyName);

        // Assert
        $this->assertInstanceOf($expectedClass, $strategy, "Strategy '$strategyName' should return instance of $expectedClass");
    }

    public static function strategyProvider(): \Generator
    {
        yield 'Fundae strategy' => ['Fundae', FundaeStrategy::class];
        yield 'Easylearning strategy' => ['Easylearning', EasylearningStrategy::class];
        yield 'Hobetuz strategy' => ['Hobetuz', HobetuzStrategy::class];
        yield 'Dhl strategy' => ['Dhl', DhlStrategy::class];
        yield 'Novomatic strategy' => ['Novomatic', NovomaticStrategy::class];
    }

    /**
     * Test that invalid strategy names throw appropriate exceptions.
     *
     * @dataProvider invalidStrategyProvider
     */
    public function testGetStrategyWithInvalidInputThrowsException(
        mixed $strategyName,
        string $expectedException,
        ?string $expectedMessage = null
    ): void {
        // Assert
        $this->expectException($expectedException);
        if ($expectedMessage) {
            $this->expectExceptionMessage($expectedMessage);
        }

        // Act
        $this->diplomaFactory->getStrategy($strategyName);
    }

    public static function invalidStrategyProvider(): \Generator
    {
        yield 'Unknown strategy' => [
            'strategyName' => 'UnknownStrategy',
            'expectedException' => \InvalidArgumentException::class,
            'expectedMessage' => 'Unknown diploma strategy: UnknownStrategy',
        ];

        yield 'Empty strategy name' => [
            'strategyName' => '',
            'expectedException' => \InvalidArgumentException::class,
            'expectedMessage' => 'Unknown diploma strategy: ',
        ];

        yield 'Null strategy name' => [
            'strategyName' => null,
            'expectedException' => \TypeError::class,
            'expectedMessage' => null,
        ];
    }

    /**
     * Test that strategies are not singleton (different calls return different instances).
     */
    public function testStrategiesAreNotSingleton(): void
    {
        // Act
        $fundaeStrategy = $this->diplomaFactory->getStrategy('Fundae');

        // Verify that different calls return different instances (not singleton)
        $anotherFundaeStrategy = $this->diplomaFactory->getStrategy('Fundae');
        $this->assertNotSame($fundaeStrategy, $anotherFundaeStrategy, 'Should create new instances each time');
    }

    /**
     * Test case sensitivity of strategy names.
     */
    public function testStrategyNamesCaseSensitivity(): void
    {
        // Test that exact case works
        $strategy1 = $this->diplomaFactory->getStrategy('Novomatic');
        $this->assertInstanceOf(NovomaticStrategy::class, $strategy1);

        // Test that different case throws exception
        $this->expectException(\InvalidArgumentException::class);
        $this->diplomaFactory->getStrategy('novomatic'); // lowercase should fail
    }

    /**
     * Test that all strategies implement the DiplomaInterface.
     */
    public function testAllStrategiesImplementDiplomaInterface(): void
    {
        $strategies = ['Fundae', 'Easylearning', 'Hobetuz', 'Dhl', 'Novomatic'];

        foreach ($strategies as $strategyName) {
            $strategy = $this->diplomaFactory->getStrategy($strategyName);

            $this->assertTrue(
                method_exists($strategy, 'getContentCourseDiploma'),
                "Strategy $strategyName should implement getContentCourseDiploma method"
            );

            $this->assertTrue(
                method_exists($strategy, 'getContentAnnouncementDiploma'),
                "Strategy $strategyName should implement getContentAnnouncementDiploma method"
            );
        }
    }

    /**
     * Test factory constructor with all dependencies.
     */
    public function testFactoryConstructorWithDependencies(): void
    {
        // Arrange & Act
        $factory = new DiplomaFactory(
            $this->entityManager,
            $this->settingsService,
            $this->announcementConfigurationsService,
            $this->dateFormatterService
        );

        // Assert
        $this->assertInstanceOf(DiplomaFactory::class, $factory);

        // Test that it can create strategies
        $strategy = $factory->getStrategy('Novomatic');
        $this->assertInstanceOf(NovomaticStrategy::class, $strategy);
    }
}
