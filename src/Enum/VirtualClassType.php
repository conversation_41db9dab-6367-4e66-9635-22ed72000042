<?php

namespace App\Enum;

final class VirtualClassType
{
    public const TYPE_ZOOM = 'ZOOM';
    public const TYPE_CLIKMEETING = 'CLICKMEETING';
    public const TYPE_JITSI = 'JITSI';
    public const TYPE_PLUGNMEET = 'PLUGNMEET';

    public const TYPE_ZOOM_ID = 1;
    public const TYPE_CLIKMEETING_ID = 2;
    public const TYPE_JITSI_ID = 3;
    public const TYPE_PLUGNMEET_ID = 4;

    public static function getTypes(): array
    {
        return [
            self::TYPE_ZOOM_ID => [
                'id' => 1,
                'variable' => 'zoomMeetingService'
            ],
        
              self::TYPE_CLIKMEETING_ID => [
                'id' => 2,
                'variable' => 'clickMeetingService',
            ],
                //TODO: Add the other types
            /*
            self::TYPE_JITSI => [
                'id' => 3,
                'variable' => 'jitsiService',
            ], */
        
              self::TYPE_PLUGNMEET_ID => [
                'id' => 4,
                'variable' => 'plugnmeetService',
            ],
        ];
    }
}
