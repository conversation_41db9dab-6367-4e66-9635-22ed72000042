<?php

declare(strict_types=1);

namespace App\Enum;

final class ChapterContent
{
    // General constants for content types.
    public const QUESTIONS_LIST_TEMPLATE = 'admin\question\list.html.twig';

    // Scorm
    public const SCORM_TYPE = 1;
    public const SCORM_CODE = 'SCORM';
    public const SCORM_ICON = 'scorm.svg';
    public const SCORM_DETAIL_TEMPLATE = 'admin\scorm\detail.html.twig';
    public const SCORM_PLAYER_URL = '/scorm/';

    // Content
    public const CONTENT_TYPE = 2;
    public const CONTENT_CODE = 'CONTENT';
    public const CONTENT_ICON = 'contents.svg';
    public const CONTENT_LIST_TEMPLATE = 'admin\content\list.html.twig';
    public const CONTENT_PLAYER_URL = '/contents/';

    // PDF
    public const PDF_TYPE = 8;
    public const PDF_CODE = 'PDF';
    public const PDF_ICON = 'pdf.svg';
    public const PDF_URL = '/pdf/';
    public const PDF_DETAIL_TEMPLATE = 'admin\pdf\detail.html.twig';

    // Video
    public const VIDEO_TYPE = 9;
    public const VIDEO_CODE = 'VIDEO';
    public const VIDEO_ICON = 'video.svg';
    public const VIDEO_URL = '/video/';
    public const VIDEO_DETAIL_TEMPLATE = 'admin\video\detail.html.twig';

    // Slider
    public const SLIDER_TYPE = 10;
    public const SLIDER_ICON = 'slider.svg';
    public const SLIDER_PLAYER_URL = '/slider/';
    public const SLIDER_MINIMUM_IMAGE = 12;
    public const SLIDER_LIST_TEMPLATE = 'admin\slider\list.html.twig';

    // VCMS
    public const VCMS_TYPE = 22;
    public const VCMS_CODE = 'VCMS';
    public const VCMS_ICON = 'vcms.svg';
    public const VCMS_URL_VIEW = '/vcms/visor/%s';
    public const VCMS_TEMPLATE = 'admin\vcms\detail.html.twig';
    public const VCMS_URL_EDIT = '/vcms/project-manager/%s';

    // Roleplay
    public const ROLEPLAY_TYPE = 23;
    public const ROLE_PLAY_CODE = 'ROLE_PLAY';
    public const ROLEPLAY_ICON = 'roleplay.svg';
    public const ROLEPLAY_URL_VIEW = '/roleplay/visor/%s';
    public const ROLEPLAY_TEMPLATE = 'admin\roleplay\detail.html.twig';
    public const ROLEPLAY_URL_EDIT = '/roleplay/project-manager/%s';

    public const PPT_TYPE = 24;
    public const PPT_CODE = 'PPT';
    public const PPT_ICON = 'ppt.svg';
    public const PPT_URL = '/ppt/';
    public const PPT_DETAIL_TEMPLATE = 'admin\ppt\detail.html.twig';

    public const LTI_TYPE = 25;
    public const LTI_CODE = 'LTI';
    public const LTI_URL = '/lti/';
    public const LTI_ICON = 'lti.svg';
    public const LTI_DETAIL_TEMPLATE = 'admin\lti_chapter\detail.html.twig';
    public const LTI_PLAYER_URL = '/lti/%userId/%chapterId';
    public const DEFAULT_PORCENTAJE_FOR_COMPLETE = 1;
}
