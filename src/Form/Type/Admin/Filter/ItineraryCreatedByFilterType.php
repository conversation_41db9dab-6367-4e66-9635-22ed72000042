<?php

namespace App\Form\Type\Admin\Filter;

use App\Entity\Itinerary;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Form\Filter\Type\ChoiceFilterType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\OptionsResolver\OptionsResolver;

class ItineraryCreatedByFilterType extends AbstractType
{
    private EntityManagerInterface $em;

    public function __construct(EntityManagerInterface $em)
    {
        $this->em = $em;
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'value_type_options' => [
                'multiple' => true,
                'choices' => array_flip($this->em->getRepository(Itinerary::class)->findUsersItineraryCreators())
            ]
        ]);
    }

    public function getParent(): string
    {
        return ChoiceFilterType::class;
    }
}
