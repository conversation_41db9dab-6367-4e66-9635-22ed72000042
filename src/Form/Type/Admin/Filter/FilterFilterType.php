<?php


namespace App\Form\Type\Admin\Filter;

use App\Entity\Filter;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Form\Filter\Type\ChoiceFilterType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\OptionsResolver\OptionsResolver;

class FilterFilterType extends AbstractType
{
    protected EntityManagerInterface $em;

    /**
     * @param EntityManagerInterface $entityManager
     */
    public function __construct (EntityManagerInterface $entityManager)
    {
        $this->em        = $entityManager;
    }

    public function configureOptions (OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'value_type_options'      => [
                'multiple' => true,
                'choices' => $this->em->getRepository(Filter::class)->getList(),
            ],
        ]);
    }


    public function getParent (): string
    {
        return ChoiceFilterType::class;
    }
}
