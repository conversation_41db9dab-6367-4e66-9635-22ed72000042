<?php

namespace App\Form\Type\Admin\Filter;

use App\Entity\Center;
use App\Entity\ProfessionalCategory;
use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Form\Filter\Type\ComparisonFilterType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Intl\Locales;
use Symfony\Component\OptionsResolver\OptionsResolver;

class CourseProfessionalCategoryFilterType extends AbstractType
{
    protected EntityManagerInterface $em;


    /**
     * @param EntityManagerInterface $entityManager
     */
    public function __construct (EntityManagerInterface $entityManager)
    {
        $this->em        = $entityManager;
    }


    public function configureOptions (OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'comparison_type_options' => ['type' => 'entity'], // second field with some predefined values, for 'type' => 'entity' you'll get 'is same', 'is not same' choices
            'value_type'              => ChoiceType::class,
            'value_type_options'      => [
                'choices' => array_flip($this->em->getRepository(ProfessionalCategory::class)->getList()),
            ],
        ]);
    }


    public function getParent (): string
    {
        return ComparisonFilterType::class;
    }

}
