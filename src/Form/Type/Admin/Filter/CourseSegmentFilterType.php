<?php

namespace App\Form\Type\Admin\Filter;

use App\Entity\Center;
use App\Entity\CourseSegment;
use App\Entity\ProfessionalCategory;
use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Form\Filter\Type\ChoiceFilterType;
use EasyCorp\Bundle\EasyAdminBundle\Form\Filter\Type\ComparisonFilterType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Intl\Locales;
use Symfony\Component\OptionsResolver\OptionsResolver;

class CourseSegmentFilterType extends AbstractType
{
    protected EntityManagerInterface $em;


    /**
     * @param EntityManagerInterface $entityManager
     */
    public function __construct (EntityManagerInterface $entityManager)
    {
        $this->em        = $entityManager;
    }


    public function configureOptions (OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'value_type_options'      => [
                'multiple' => true,
                'choices' => array_flip($this->em->getRepository(CourseSegment::class)->getList()),
            ],
        ]);
    }


    public function getParent (): string
    {
        return ChoiceFilterType::class;
    }

}
