<?php

declare(strict_types=1);

namespace App\V2\Application\CommandHandler;

use App\V2\Application\Command\PostLtiRegistrationCommand;
use App\V2\Domain\LTI\Exceptions\LtiRegistrationNotFoundException;
use App\V2\Domain\LTI\Exceptions\PostLtiRegistrationException;
use App\V2\Domain\LTI\LtiRegistration;
use App\V2\Domain\LTI\LtiRegistrationCriteria;
use App\V2\Domain\LTI\LtiRegistrationRepository;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Uuid\UuidGenerator;

readonly class PostLtiRegistrationCommandHandler
{
    public function __construct(
        private LtiRegistrationRepository $ltiRegistrationRepository,
        private UuidGenerator $uuidGenerator,
    ) {
    }

    /**
     * @throws InfrastructureException
     * @throws PostLtiRegistrationException
     */
    public function handle(PostLtiRegistrationCommand $command): void
    {
        try {
            $this->ensureClientIdIsUnique($command->getClientId());
            throw PostLtiRegistrationException::clientIdMustBeUnique($command->getClientId());
        } catch (LtiRegistrationNotFoundException) {
        }

        $registration = new LtiRegistration(
            id: $this->uuidGenerator->generate(),
            name: $command->getName(),
            clientId: $command->getClientId(),
        );

        $this->ltiRegistrationRepository->put($registration);
    }

    /**
     * @throws LtiRegistrationNotFoundException
     * @throws InfrastructureException
     */
    private function ensureClientIdIsUnique(string $clientId): void
    {
        $this->ltiRegistrationRepository->findOneBy(
            LtiRegistrationCriteria::createEmpty()->filterByClientId($clientId),
        );
    }
}
