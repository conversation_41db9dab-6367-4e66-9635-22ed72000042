<?php

declare(strict_types=1);

namespace App\V2\Application\Hydrator\LTI;

use App\V2\Application\Hydrator\HydratorCollection;
use App\V2\Domain\LTI\LtiRegistrationCollection;
use App\V2\Domain\LTI\LtiRegistrationHydrationCriteria;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Hydrator\HydrationCriteria;
use App\V2\Domain\Shared\Hydrator\HydratorException;

class LtiRegistrationHydratorCollection extends HydratorCollection
{
    public function hydrate(Collection $collection, HydrationCriteria $criteria): void
    {
        if (
            !$collection instanceof LtiRegistrationCollection
            || !$criteria instanceof LtiRegistrationHydrationCriteria
        ) {
            throw new HydratorException();
        }

        parent::hydrate($collection, $criteria);
    }
}
