<?php

declare(strict_types=1);

namespace App\V2\Application\Hydrator\Course\Creator;

use App\Entity\User;
use App\V2\Application\Hydrator\Hydrator;
use App\V2\Application\Hydrator\HydratorPriority;
use App\V2\Domain\Course\Creator\CourseCreator;
use App\V2\Domain\Course\Creator\CourseCreatorCollection;
use App\V2\Domain\Course\Creator\CourseCreatorHydrationCriteria;
use App\V2\Domain\Course\Creator\Creator;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Email\Email;
use App\V2\Domain\Shared\Email\InvalidEmailException;
use App\V2\Domain\Shared\Hydrator\HydrationCriteria;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Id\IdCollection;
use App\V2\Domain\User\UserCriteria;
use App\V2\Domain\User\UserRepository;

class CourseCreatorUserHydrator implements Hydrator
{
    private const HydratorPriority PRIORITY = HydratorPriority::First;

    public function __construct(
        private readonly UserRepository $userRepository,
    ) {
    }

    public function getPriority(): HydratorPriority
    {
        return self::PRIORITY;
    }

    public function supports(HydrationCriteria $criteria): bool
    {
        return $criteria instanceof CourseCreatorHydrationCriteria && $criteria->needsUser();
    }

    /**
     * @throws CollectionException
     * @throws CriteriaException
     * @throws InvalidEmailException
     */
    public function hydrate(Collection $collection, HydrationCriteria $criteria): void
    {
        if (!$collection instanceof CourseCreatorCollection) {
            return;
        }

        if ($collection->isEmpty()) {
            return;
        }
        $ids = array_reduce(
            $collection->all(),
            function (array $carry, CourseCreator $courseCreator): array {
                $carry[] = new Id($courseCreator->getUserId());

                return $carry;
            },
            []
        );

        $ids = array_unique($ids);

        $users = $this->userRepository->findBy(
            UserCriteria::createByIds(new IdCollection($ids))
        );

        if ($users->isEmpty()) {
            return;
        }

        $courseCreatorByUserId = [];

        foreach ($collection->all() as $courseCreator) {
            $courseCreatorByUserId[$courseCreator->getUserId()][] = $courseCreator;
        }

        /** @var User $user */
        foreach ($users->all() as $user) {
            /** @var CourseCreator $courseCreator */
            foreach ($courseCreatorByUserId[$user->getId()] as $courseCreator) {
                $courseCreator->setCreator($this->fromUserToCreator($user));
            }
        }
    }

    /**
     * @throws InvalidEmailException
     */
    private function fromUserToCreator(User $user): Creator
    {
        return new Creator(
            id: new Id($user->getId()),
            email: new Email($user->getEmail()),
            firstName: $user->getFirstName(),
            lastName: $user->getLastName(),
        );
    }
}
