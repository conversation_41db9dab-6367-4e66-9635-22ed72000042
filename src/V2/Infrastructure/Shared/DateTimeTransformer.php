<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Shared;

class DateTimeTransformer
{
    public const string DATE_TIME_FORMAT = 'Y-m-d H:i:s';

    public static function toOutput(\DateTimeInterface $dateTime): string
    {
        return $dateTime->format(self::DATE_TIME_FORMAT);
    }

    public static function fromInput(string $dateTime): \DateTimeImmutable
    {
        return \DateTimeImmutable::createFromFormat(self::DATE_TIME_FORMAT, $dateTime);
    }

    public static function fromDateTime(\DateTimeInterface $dateTime): \DateTimeImmutable
    {
        return \DateTimeImmutable::createFromFormat(self::DATE_TIME_FORMAT, $dateTime->format(self::DATE_TIME_FORMAT));
    }
}
