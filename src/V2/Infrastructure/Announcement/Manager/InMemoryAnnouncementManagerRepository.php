<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Announcement\Manager;

use App\V2\Domain\Announcement\Manager\AnnouncementManager;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerCollection;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerCriteria;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerNotFoundException;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerRepository;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerRepositoryException;
use App\V2\Domain\Shared\Collection\CollectionException;

class InMemoryAnnouncementManagerRepository implements AnnouncementManagerRepository
{
    private AnnouncementManagerCollection $announcementManagers;

    /**
     * @throws CollectionException
     */
    public function __construct()
    {
        $this->announcementManagers = new AnnouncementManagerCollection([]);
    }

    #[\Override]
    public function insert(AnnouncementManager $announcementManager): void
    {
        try {
            $this->findOneBy(
                AnnouncementManagerCriteria::createEmpty()
                    ->filterByUserId($announcementManager->getUserId()->value())
                    ->filterByAnnouncementId($announcementManager->getAnnouncementId()->value())
            );

            throw AnnouncementManagerRepositoryException::duplicateAnnouncementManager($announcementManager);
        } catch (AnnouncementManagerNotFoundException) {
            $this->announcementManagers->append(clone $announcementManager);
        }
    }

    #[\Override]
    public function findOneBy(AnnouncementManagerCriteria $criteria): AnnouncementManager
    {
        $collection = $this->filterByCriteria($criteria);

        if ($collection->isEmpty()) {
            throw new AnnouncementManagerNotFoundException();
        }

        return $collection->first();
    }

    #[\Override]
    public function findBy(AnnouncementManagerCriteria $criteria): AnnouncementManagerCollection
    {
        return $this->filterByCriteria($criteria);
    }

    #[\Override]
    public function delete(AnnouncementManager $announcementManager): void
    {
        $this->announcementManagers = $this->announcementManagers->filter(
            fn (AnnouncementManager $manager) => $manager->getUserId()->value() !== $announcementManager->getUserId()->value()
                || $manager->getAnnouncementId()->value() !== $announcementManager->getAnnouncementId()->value()
        );
    }

    /**
     * @throws CollectionException
     */
    private function filterByCriteria(AnnouncementManagerCriteria $criteria): AnnouncementManagerCollection
    {
        $collection = $this->announcementManagers->filter(
            fn (AnnouncementManager $announcementManager) => (null === $criteria->getUserId() || $announcementManager->getUserId()->value() === $criteria->getUserId())
                && (null === $criteria->getAnnouncementId() || $announcementManager->getAnnouncementId()->value() === $criteria->getAnnouncementId())
        );

        return $collection->map(
            fn (AnnouncementManager $announcementManager) => new AnnouncementManager(
                userId: $announcementManager->getUserId(),
                announcementId: $announcementManager->getAnnouncementId()
            )
        );
    }
}
