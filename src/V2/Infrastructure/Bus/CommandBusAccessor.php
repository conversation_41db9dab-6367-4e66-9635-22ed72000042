<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Bus;

use App\V2\Domain\Bus\Command;
use App\V2\Domain\Bus\CommandBus;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;

abstract class CommandBusAccessor extends AbstractController
{
    public function __construct(
        private readonly CommandBus $commandBus,
    ) {
    }

    protected function execute(Command $command): mixed
    {
        return $this->commandBus->execute($command);
    }
}
