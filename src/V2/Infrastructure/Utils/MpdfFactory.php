<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Utils;

use Mpdf\Config\ConfigVariables;
use Mpdf\Config\FontVariables;
use Mpdf\Mpdf;
use Mpdf\MpdfException;
use Mpdf\Output\Destination;
use Symfony\Component\HttpFoundation\Response;

readonly class MpdfFactory
{
    public function __construct(private string $cacheDir = '/tmp')
    {
    }

    public function getDefaultConstructorParams(): array
    {
        return [
            'mode' => '',
            'format' => 'A4',
            'default_font_size' => 0,
            'default_font' => '',
            'margin_left' => 15,
            'margin_right' => 15,
            'margin_top' => 16,
            'margin_bottom' => 16,
            'margin_header' => 9,
            'margin_footer' => 9,
            'orientation' => 'P',
        ];
    }

    public function getDefaultConfigVariables(): array
    {
        $configObject = new ConfigVariables();

        return $configObject->getDefaults();
    }

    public function getDefaultFontVariables(): array
    {
        $fontObject = new FontVariables();

        return $fontObject->getDefaults();
    }

    /**
     * @throws MpdfException
     */
    public function createMpdfObject($mpdfArgs = []): Mpdf
    {
        $defaultOptions = array_merge(
            $this->getDefaultConstructorParams(),
            $this->getDefaultConfigVariables(),
            $this->getDefaultFontVariables(),
            [
                'mode' => 'utf-8',
                'format' => 'A4',
                'tempDir' => $this->cacheDir,
            ]
        );

        $argOptions = array_merge($defaultOptions, $mpdfArgs);

        return new Mpdf($argOptions);
    }

    /**
     * @throws MpdfException
     */
    public function createInlineResponse(
        Mpdf $mPdf,
        ?string $filename = null,
        ?int $status = 200,
        ?array $headers = [],
    ): Response {
        $content = $mPdf->Output('', Destination::STRING_RETURN);

        $defaultHeaders = [
            'Content-Type' => 'application/pdf',
            'Cache-Control' => 'public, must-revalidate, max-age=0',
            'Pragma' => 'public',
            'Expires' => 'Sat, 26 Jul 1997 05:00:00 GMT',
            'Last-Modified' => gmdate('D, d M Y H:i:s') . ' GMT',
        ];
        if (!\is_null($filename)) {
            $defaultHeaders['Content-disposition'] = \sprintf('inline; filename="%s"', $filename);
        } else {
            $defaultHeaders['Content-disposition'] = \sprintf('inline; filename="%s"', 'file.pdf');
        }

        $headers = array_merge($defaultHeaders, $headers);

        return new Response($content, $status, $headers);
    }

    /**
     * @throws MpdfException
     */
    public function createDownloadResponse(
        Mpdf $mPdf,
        string $filename,
        ?int $status = 200,
        ?array $headers = [],
    ): Response {
        $content = $mPdf->Output('', Destination::STRING_RETURN);

        $defaultHeaders = [
            'Content-Type' => 'application/pdf',
            'Content-Description' => 'File Transfer',
            'Content-Transfer-Encoding' => 'binary',
            'Cache-Control' => 'public, must-revalidate, max-age=0',
            'Pragma' => 'public',
            'Expires' => 'Sat, 26 Jul 1997 05:00:00 GMT',
            'Last-Modified' => gmdate('D, d M Y H:i:s') . ' GMT',
            'Content-disposition' => \sprintf('attachment; filename="%s"', $filename),
        ];

        $headers = array_merge($headers, $defaultHeaders);

        $headers = array_merge($defaultHeaders, $headers);

        return new Response($content, $status, $headers);
    }
}
