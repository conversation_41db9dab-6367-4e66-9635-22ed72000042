<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Security;

use App\Entity\RefreshToken;
use App\V2\Domain\Security\Exceptions\InvalidTokenException;
use App\V2\Domain\Security\Exceptions\RefreshTokenNotFoundException;
use App\V2\Domain\Security\Exceptions\TokenInterfaceException;
use App\V2\Domain\Security\RefreshTokenCriteria;
use App\V2\Domain\Security\RefreshTokenRepository;
use App\V2\Domain\Security\TokenInterface;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\User\UserCriteria;
use App\V2\Domain\User\UserRepository;
use Lexik\Bundle\JWTAuthenticationBundle\Exception\JWTDecodeFailureException;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTManager;
use Symfony\Component\HttpFoundation\Cookie;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\User\UserInterface;

readonly class LexitJwtToken implements TokenInterface
{
    public function __construct(
        private JWTManager $jwtManager,
        private RefreshTokenRepository $refreshTokenRepository,
        private UserRepository $userRepository,
        private int $jwtTokenTtl,
    ) {
    }

    #[\Override]
    public function getJwtToken(UserInterface $user, array $extra = []): string
    {
        $payload = array_merge(
            [
                'email' => $user->getEmail(),
                'user' => [
                    'first_name' => $user->getFirstName(),
                    'last_name' => $user->getLastName(),
                ],
            ],
            $extra,
        );

        return $this->jwtManager->createFromPayload($user, $payload);
    }

    #[\Override]
    public function getRefreshToken(UserInterface $user): string
    {
        while (true) {
            $token = bin2hex(random_bytes(64));
            try {
                $this->refreshTokenRepository->findOneBy(
                    RefreshTokenCriteria::createEmpty()->filterByToken($token)
                );
            } catch (RefreshTokenNotFoundException) {
                break;
            }
        }

        $refreshToken = new RefreshToken();
        $refreshToken->setRefreshToken($token);
        $refreshToken->setUsername($user->getUserIdentifier());

        $validTime = new \DateTime();
        // Required to check if is positive or negative increments: https://github.com/php/php-src/issues/9950
        if ($this->jwtTokenTtl > 0) {
            $validTime->modify('+' . ($this->jwtTokenTtl * 2) . ' seconds');
        } else {
            $validTime->modify(($this->jwtTokenTtl * 2) . ' seconds');
        }

        $refreshToken->setValid($validTime);

        $this->refreshTokenRepository->put($refreshToken);

        return $token;
    }

    #[\Override]
    public function getUser(string $token): UserInterface
    {
        try {
            $payload = $this->jwtManager->parse($token); // Lexit parser verify if the token is valid
        } catch (JWTDecodeFailureException $e) {
            throw TokenInterfaceException::fromPrevious($e);
        }

        return $this->userRepository->findOneBy(
            UserCriteria::createEmpty()
            ->filterByEmail($payload['email'])// Filtering in another MR, waiting for the change
        );
    }

    #[\Override]
    public function validateRefreshToken(string $token): void
    {
        $refreshToken = $this->refreshTokenRepository->findOneBy(
            RefreshTokenCriteria::createEmpty()->filterByToken($token)
        );

        if (null === $refreshToken->getValid() || $refreshToken->getValid() < new \DateTimeImmutable()) {
            throw new InvalidTokenException();
        }

        // Refresh tokens must be of single use
        $this->refreshTokenRepository->delete($refreshToken);
    }

    /**
     * @throws InfrastructureException
     * @throws \DateMalformedStringException
     */
    public function setTokensCookie(Response $response, UserInterface $user): void
    {
        $response->headers->setCookie(
            Cookie::create(
                name: 'BEARER',
                value: $this->getJwtToken($user),
                expire: (new \DateTimeImmutable())->modify($this->jwtTokenTtl . ' seconds'),
            )
        );

        $response->headers->setCookie(
            Cookie::create(
                name: 'refresh_token',
                value: $this->getRefreshToken($user),
                expire: (new \DateTimeImmutable())->modify(($this->jwtTokenTtl * 2) . ' seconds'),
            )
        );
    }
}
