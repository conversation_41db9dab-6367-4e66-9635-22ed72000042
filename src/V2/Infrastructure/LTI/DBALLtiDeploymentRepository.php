<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\LTI;

use App\V2\Domain\LTI\Exceptions\LtiDeploymentNotFoundException;
use App\V2\Domain\LTI\Exceptions\LtiException;
use App\V2\Domain\LTI\LtiDeployment;
use App\V2\Domain\LTI\LtiDeploymentCollection;
use App\V2\Domain\LTI\LtiDeploymentCriteria;
use App\V2\Domain\LTI\LtiDeploymentRepository;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Infrastructure\Persistence\DBAL\CommonCriteriaBuilder;
use Doctrine\DBAL\ArrayParameterType;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception as DBALException;
use Doctrine\DBAL\Query\QueryBuilder;
use Doctrine\ORM\EntityManagerInterface;

class DBALLtiDeploymentRepository implements LtiDeploymentRepository
{
    private Connection $connection;

    public function __construct(
        EntityManagerInterface $em,
        private readonly string $ltiDeploymentTableName,
    ) {
        $this->connection = $em->getConnection();
    }

    #[\Override]
    public function put(LtiDeployment $deployment): void
    {
        try {
            $result = $this->findOneBy(
                LtiDeploymentCriteria::createEmpty()
                    ->filterByRegistrationId($deployment->getRegistrationId())
                    ->filterByDeploymentId($deployment->getDeploymentId())
            );

            if (!$result->getId()->equals($deployment->getId())) {
                throw LtiException::deploymentIdMustBeUniqueInRegistrationContext();
            }
        } catch (LtiDeploymentNotFoundException) {
        }

        try {
            $this->findOneBy(
                LtiDeploymentCriteria::createById($deployment->getId()),
            );

            $this->update($deployment);
        } catch (LtiDeploymentNotFoundException) {
            $this->insert($deployment);
        }
    }

    /**
     * @throws InfrastructureException
     */
    private function insert(LtiDeployment $deployment): void
    {
        try {
            $this->connection->insert(
                table: $this->ltiDeploymentTableName,
                data: $this->fromDeploymentToArray($deployment),
            );
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    /**
     * @throws InfrastructureException
     */
    private function update(LtiDeployment $deployment): void
    {
        try {
            $data = $this->fromDeploymentToArray($deployment);
            unset($data['id']);

            $this->connection->update(
                table: $this->ltiDeploymentTableName,
                data: $data,
                criteria: ['id' => $deployment->getId()->value()],
            );
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    #[\Override]
    public function findOneBy(LtiDeploymentCriteria $criteria): LtiDeployment
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAssociative();

            if (false === $result) {
                throw new LtiDeploymentNotFoundException();
            }

            return $this->fromArrayToDeployment($result);
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    #[\Override]
    public function findBy(LtiDeploymentCriteria $criteria): LtiDeploymentCollection
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAllAssociative();

            return new LtiDeploymentCollection(
                array_map(
                    fn (array $values) => $this->fromArrayToDeployment($values),
                    $result,
                )
            );
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    #[\Override]
    public function delete(LtiDeployment $deployment): void
    {
        try {
            $this->connection->delete(
                table: $this->ltiDeploymentTableName,
                criteria: ['id' => $deployment->getId()->value()],
            );
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    /**
     * @throws InvalidUuidException
     */
    private function fromArrayToDeployment(array $values): LtiDeployment
    {
        return new LtiDeployment(
            id: new Uuid($values['id']),
            registrationId: new Uuid($values['registration_id']),
            name: $values['name'],
            deploymentId: $values['deployment_id'],
        );
    }

    public function fromDeploymentToArray(LtiDeployment $deployment): array
    {
        return [
            'id' => $deployment->getId()->value(),
            'registration_id' => $deployment->getRegistrationId()->value(),
            'name' => $deployment->getName(),
            'deployment_id' => $deployment->getDeploymentId(),
        ];
    }

    private function getQueryBuilderByCriteria(LtiDeploymentCriteria $criteria): QueryBuilder
    {
        $qb = $this->connection->createQueryBuilder()
            ->select('t.*')
            ->from($this->ltiDeploymentTableName, 't');

        if (null !== $criteria->getDeploymentId()) {
            $qb->andWhere('t.deployment_id = :deployment_id')
                ->setParameter('deployment_id', $criteria->getDeploymentId());
        }

        if (null !== $criteria->getRegistrationId()) {
            $qb->andWhere('t.registration_id = :registration_id')
                ->setParameter('registration_id', $criteria->getRegistrationId()->value());
        }

        if (null !== $criteria->getRegistrationIds() && !$criteria->getRegistrationIds()->isEmpty()) {
            $qb->andWhere('t.registration_id IN (:registration_ids)')
                ->setParameter('registration_ids', $criteria->getRegistrationIds()->all(), ArrayParameterType::STRING);
        }

        CommonCriteriaBuilder::filterByCommonCriteria($criteria, $qb, true);

        return $qb;
    }
}
