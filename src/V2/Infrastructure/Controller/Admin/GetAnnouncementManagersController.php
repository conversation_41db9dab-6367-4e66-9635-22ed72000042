<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Admin;

use App\Service\SettingsService;
use App\V2\Application\Query\Admin\GetAnnouncementManagers;
use App\V2\Domain\Bus\QueryBus;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\Sort;
use App\V2\Domain\Shared\Criteria\SortableField;
use App\V2\Domain\Shared\Criteria\SortCollection;
use App\V2\Domain\Shared\Criteria\SortDirection;
use App\V2\Infrastructure\Announcement\Manager\AnnouncementManagerCriteriaTransformer;
use App\V2\Infrastructure\Announcement\Manager\AnnouncementManagerTransformer;
use App\V2\Infrastructure\Bus\QueryBusAccessor;
use App\V2\Infrastructure\Response\ApiResponseContent;
use App\V2\Infrastructure\Validator\Id\IdValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetAnnouncementManagersController extends QueryBusAccessor
{
    public function __construct(
        QueryBus $queryBus,
        private readonly SettingsService $settingsService,
    ) {
        parent::__construct($queryBus);
    }

    /**
     * @throws ValidatorException
     * @throws CollectionException
     */
    public function __invoke(Request $request, int $announcementId): Response
    {
        // Check if the feature is enabled
        if (!$this->settingsService->get('app.announcement.managers.sharing')) {
            return new JsonResponse(
                data: ApiResponseContent::createFromMessage('Announcement manager sharing is disabled')->toArray(),
                status: Response::HTTP_FORBIDDEN,
            );
        }

        IdValidator::validateId($announcementId);

        $announcementManagerCriteria = AnnouncementManagerCriteriaTransformer::fromArray($request->query->all())
            ->filterByAnnouncementId($announcementId)
            ->sortBy(
                new SortCollection([
                    new Sort(
                        new SortableField('firstName'),
                        SortDirection::ASC
                    ),
                    new Sort(
                        new SortableField('lastName'),
                        SortDirection::ASC
                    ),
                ])
            )
        ;

        $announcementManagerCollection = $this->ask(
            new GetAnnouncementManagers(
                criteria: $announcementManagerCriteria,
                withManagers: true,
            )
        );

        return new JsonResponse(
            data: ApiResponseContent::createFromData(
                AnnouncementManagerTransformer::fromCollectionToArray($announcementManagerCollection)
            )->toArray(),
            status: Response::HTTP_OK,
        );
    }
}
