<?php

declare(strict_types=1);

namespace App\V2\Domain\Shared\Criteria;

use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Id\IdCollection;
use App\V2\Domain\Shared\Identifier;

/**
 * @extends Criteria<CriteriaWithId>
 *
 * @template T of CriteriaWithId
 */
abstract class CriteriaWithId extends Criteria implements CriteriaId
{
    private ?Id $id = null;
    private ?IdCollection $ids = null;

    protected function isEmpty(): bool
    {
        return null === $this->id && null === $this->ids;
    }

    /**
     * @throws CriteriaException
     */
    public static function createById(Identifier $id): static
    {
        return self::createEmpty()->filterById($id);
    }

    /**
     * @throws CriteriaException
     * @throws CollectionException
     */
    public static function createByIds(Collection $ids): static
    {
        return self::createEmpty()->filterByIds($ids);
    }

    /**
     * @throws CriteriaException
     */
    public function filterById(Identifier $id): static
    {
        if (!$id instanceof Id) {
            throw CriteriaException::invalidParamType(
                expected: Id::class,
                actual: \get_class($id),
            );
        }
        $this->id = $id;

        return $this;
    }

    /**
     * @throws CriteriaException
     * @throws CollectionException
     */
    public function filterByIds(Collection $ids): static
    {
        if (!$ids instanceof IdCollection) {
            throw CollectionException::invalidCollectionType(
                expectedType: IdCollection::class,
                actualType: \get_class($ids),
            );
        }

        if ($ids->isEmpty()) {
            throw CriteriaException::filterByEmptyIdsIsNotAllowed();
        }

        $this->ids = $ids;

        return $this;
    }

    public function getId(): ?Id
    {
        return $this->id;
    }

    public function getIds(): ?IdCollection
    {
        return $this->ids;
    }
}
