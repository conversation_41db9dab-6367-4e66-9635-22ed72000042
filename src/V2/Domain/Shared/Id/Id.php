<?php

declare(strict_types=1);

namespace App\V2\Domain\Shared\Id;

use App\V2\Domain\Shared\Identifier;

readonly class Id implements Identifier
{
    public function __construct(private int $value)
    {
    }

    public function value(): int
    {
        return $this->value;
    }

    public function equals(Id $id): bool
    {
        return $this->value === $id->value();
    }

    public function __toString(): string
    {
        return (string) $this->value;
    }
}
