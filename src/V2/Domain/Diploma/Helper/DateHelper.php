<?php

declare(strict_types=1);

namespace App\V2\Domain\Diploma\Helper;

/**
 * Helper class for date logic.
 * Centralizes the common pattern of determining which date to use based on priority.
 */
class DateHelper
{
    /**
     * Determines which date to use based on priority:
     * 1. String date (if provided)
     * 2. DateTime object (if available)
     * 3. Current date (fallback)
     *
     * @param string|null    $stringDate String date representation
     * @param \DateTime|null $dateTime   DateTime object
     *
     * @return \DateTime The date to use
     */
    public static function getDateToUse(?string $stringDate, ?\DateTime $dateTime): \DateTime
    {
        return match (true) {
            !empty($stringDate) => new \DateTime($stringDate),
            null !== $dateTime => $dateTime,
            default => new \DateTime(),
        };
    }
}
