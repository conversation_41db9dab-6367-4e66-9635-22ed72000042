<?php

declare(strict_types=1);

namespace App\V2\Domain\Purchase;

use App\V2\Domain\Shared\Entity\EntityWithId;
use App\V2\Domain\Shared\Financial\Money;
use App\V2\Domain\Shared\Resource\Resource;
use App\V2\Domain\Shared\Uuid\Uuid;

class PurchasableItem extends EntityWithId
{
    public function __construct(
        Uuid $id,
        private readonly string $name,
        private readonly string $description,
        private readonly Money $price,
        private readonly Resource $resource,
    ) {
        parent::__construct($id);
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function getPrice(): Money
    {
        return $this->price;
    }

    public function getResource(): Resource
    {
        return $this->resource;
    }
}
