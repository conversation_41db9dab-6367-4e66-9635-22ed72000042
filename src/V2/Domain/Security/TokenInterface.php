<?php

declare(strict_types=1);

namespace App\V2\Domain\Security;

use App\V2\Domain\Security\Exceptions\InvalidTokenException;
use App\V2\Domain\Security\Exceptions\RefreshTokenNotFoundException;
use App\V2\Domain\Security\Exceptions\TokenInterfaceException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\User\UserInterface;

interface TokenInterface
{
    /**
     * @throws InfrastructureException
     */
    public function getJwtToken(UserInterface $user, array $extra = []): string;

    /**
     * @throws InfrastructureException
     */
    public function getRefreshToken(UserInterface $user): string;

    /**
     * Try to get the user. If the token is invalid an exception will be thrown.
     *
     * @throws TokenInterfaceException
     * @throws InfrastructureException
     */
    public function getUser(string $token): UserInterface;

    /**
     * @throws TokenInterfaceException
     * @throws InfrastructureException
     * @throws InvalidTokenException
     * @throws RefreshTokenNotFoundException
     */
    public function validateRefreshToken(string $token): void;

    public function setTokensCookie(Response $response, UserInterface $user): void;
}
