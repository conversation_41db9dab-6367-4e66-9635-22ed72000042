<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250613203548 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE IF NOT EXISTS lti_registration (
                id VARCHAR(36) NOT NULL,
                name VARCHAR(255) NOT NULL,
                client_id VARCHAR(255) NOT NULL,
                CONSTRAINT PK_LTI_REGISTRATION_ID PRIMARY KEY(id),
                UNIQUE INDEX UNQ_LTI_REGISTRATION_CLIENT_ID (client_id)
            ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);

        $this->addSql(<<<'SQL'
            CREATE TABLE IF NOT EXISTS lti_platform (
                id VARCHAR(36) NOT NULL,
                registration_id VARCHAR(36) NOT NULL,
                name VARCHAR(255) NOT NULL,
                audience VARCHAR(255) NOT NULL,
                oidc_authentication_url TEXT NOT NULL,
                oauth2_access_token_url TEXT NOT NULL,
                jwks_url TEXT NOT NULL,
                CONSTRAINT PK_LTI_PLATFORM_ID PRIMARY KEY(id),
                CONSTRAINT FK_LTI_PLATFORM_REGISTRATION_ID FOREIGN KEY (registration_id)
                    REFERENCES `lti_registration` (id) ON DELETE CASCADE
            ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);

        $this->addSql(<<<'SQL'
            CREATE TABLE IF NOT EXISTS lti_tool_v2 (
                id VARCHAR(36) NOT NULL,
                registration_id VARCHAR(36) NOT NULL,
                name VARCHAR(255) NOT NULL,
                audience VARCHAR(255) NOT NULL,
                oidc_initiation_url TEXT NOT NULL,
                launch_url TEXT NOT NULL,
                deep_linking_url TEXT NOT NULL,
                jwks_url TEXT NOT NULL,
                CONSTRAINT PK_LTI_TOOL_ID PRIMARY KEY(id),
                CONSTRAINT FK_LTI_TOOL_REGISTRATION_ID FOREIGN KEY (registration_id)
                    REFERENCES `lti_registration` (id) ON DELETE CASCADE
            ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);

        $this->addSql(<<<'SQL'
            CREATE TABLE IF NOT EXISTS lti_deployment (
                id VARCHAR(36) NOT NULL,
                registration_id VARCHAR(36) NOT NULL,
                name VARCHAR(255) NOT NULL,
                deployment_id VARCHAR(255) NOT NULL,
                CONSTRAINT PK_LTI_DEPLOYMENT_ID PRIMARY KEY(id),
                CONSTRAINT FK_LTI_DEPLOYMENT_REGISTRATION_ID FOREIGN KEY (registration_id)
                    REFERENCES `lti_registration` (id) ON DELETE CASCADE,
                UNIQUE INDEX UNQ_LTI_DEPLOYMENT_DEPLOYMENT_ID (registration_id, deployment_id)
            ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            DROP TABLE lti_deployment
        SQL);

        $this->addSql(<<<'SQL'
            DROP TABLE lti_tool_v2
        SQL);

        $this->addSql(<<<'SQL'
            DROP TABLE lti_platform
        SQL);

        $this->addSql(<<<'SQL'
            DROP TABLE lti_registration
        SQL);
    }
}
