<?php

declare(strict_types=1);

namespace App\Event;

use App\Entity\Chapter;
use Symfony\Contracts\EventDispatcher\Event;

/**
 * Event dispatched when the content of a chapter is changed
 * This event is used to notify when a chapter's content has been modified,
 * particularly when a SCORM package is updated.
 */
class ChapterContentChangedEvent extends Event
{
    private Chapter $chapter;

    public function __construct(Chapter $chapter)
    {
        $this->chapter = $chapter;
    }

    public function getChapter(): Chapter
    {
        return $this->chapter;
    }
}
