<?php

namespace App\Saml;

use App\Entity\User;
use LightSaml\Model\Assertion\AuthnStatement;
use LightSaml\Model\Assertion\Conditions;
use LightSaml\Model\Assertion\Issuer;
use LightSaml\Model\Assertion\Subject;
use LightSaml\Model\XmlDSig\Signature;

class UserContainer
{
    private User $user;

    /// Private variables returned from response
    private ?Issuer $issuer = null;
    private ?Subject $subject = null;
    private ?Signature $signature = null;
    private ?Conditions $conditions = null;
    private ?AuthnStatement $authnStatement = null;
    private ?string $id = null;

    public function __construct(User $user)
    {
        $this->user = $user;
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(?string $id): self
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @param User $user
     * @return UserContainer
     */
    public function setUser(User $user): self
    {
        $this->user = $user;
        return $this;
    }

    /**
     * @return User
     */
    public function getUser(): User
    {
        return $this->user;
    }

    /**
     * @param Issuer $issuer
     * @return UserContainer
     */
    public function setIssuer(Issuer $issuer): self
    {
        $this->issuer = $issuer;
        return $this;
    }

    /**
     * @return Issuer
     */
    public function getIssuer(): Issuer
    {
        return $this->issuer;
    }

    /**
     * @param Subject $subject
     * @return UserContainer
     */
    public function setSubject(Subject $subject): self
    {
        $this->subject = $subject;
        return $this;
    }

    /**
     * @return Subject
     */
    public function getSubject(): ?Subject
    {
        return $this->subject;
    }

    /**
     * @param ?Signature $signature
     * @return UserContainer
     */
    public function setSignature(?Signature $signature): self
    {
        $this->signature = $signature;
        return $this;
    }

    /**
     * @return ?Signature
     */
    public function getSignature(): ?Signature
    {
        return $this->signature;
    }

    /**
     * @param Conditions $conditions
     * @return UserContainer
     */
    public function setConditions(Conditions $conditions): self
    {
        $this->conditions = $conditions;
        return $this;
    }

    /**
     * @return Conditions
     */
    public function getConditions(): Conditions
    {
        return $this->conditions;
    }

    /**
     * @param AuthnStatement $authnStatement
     * @return UserContainer
     */
    public function setAuthnStatement(AuthnStatement $authnStatement): self
    {
        $this->authnStatement = $authnStatement;
        return $this;
    }

    /**
     * @return AuthnStatement
     */
    public function getAuthnStatement(): AuthnStatement
    {
        return $this->authnStatement;
    }
}
