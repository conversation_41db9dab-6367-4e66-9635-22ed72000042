<?php

namespace App\Entity;

use App\Repository\HolesRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass=HolesRepository::class)
 */
class Holes
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"fillgaps"})
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=Fillgaps::class, inversedBy="holes")
     */
    private $fillgap;

    /**
     * @ORM\Column(type="integer")
     * @Groups({"fillgaps"})
     */
    private $hole;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"fillgaps"})
     */
    private $answer;

    /**
     * @ORM\Column(type="boolean")
     * @Groups({"fillgaps"})
     */
    private $correct;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getFillgap(): ?Fillgaps
    {
        return $this->fillgap;
    }

    public function setFillgap(?Fillgaps $fillgap): self
    {
        $this->fillgap = $fillgap;

        return $this;
    }

    public function getHole(): ?int
    {
        return $this->hole;
    }

    public function setHole(int $hole): self
    {
        $this->hole = $hole;

        return $this;
    }

    public function getAnswer(): ?string
    {
        return $this->answer;
    }

    public function setAnswer(string $answer): self
    {
        $this->answer = $answer;

        return $this;
    }

    public function isCorrect(): ?bool
    {
        return $this->correct;
    }

    public function setCorrect(bool $correct): self
    {
        $this->correct = $correct;

        return $this;
    }
}
