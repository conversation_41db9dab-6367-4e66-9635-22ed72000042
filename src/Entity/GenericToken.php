<?php

namespace App\Entity;

use App\Repository\GenericTokenRepository;
use App\Utils\TimeZoneConverter\UtcTimezoneInterface;
use App\Utils\TimeZoneConverter\UtcTimezoneTrait;
use Doctrine\ORM\Mapping as ORM;

/**
 * All DateTime values saved as UTC +00:00
 * @ORM\HasLifecycleCallbacks()
 * @ORM\Entity(repositoryClass=GenericTokenRepository::class)
 */
class GenericToken implements UtcTimezoneInterface
{
    use UtcTimezoneTrait;

    public const TYPE_ANNOUNCEMENT_SESSION_ENTRY_QR = 'announcement_session_qr_entry';
    public const TYPE_ANNOUNCEMENT_SESSION_EXIT_QR = 'announcement_session_qr_exit';

    use AtAndBy;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $token;

    /**
     * @ORM\Column(type="string", length=100)
     */
    private $type;

    /**
     * @ORM\Column(type="integer")
     */
    private $entityId;

    /**
     * @ORM\Column(type="json", nullable=true)
     */
    private $extra = [];

    /**
     * @ORM\Column(type="datetime_immutable", nullable=true)
     */
    private $validFrom;

    /**
     * @ORM\Column(type="datetime_immutable", nullable=true)
     */
    private $validUntil;

    /**
     * @ORM\Column(type="boolean")
     */
    private $revoked;

    public function __construct()
    {
        $this->revoked = false;
        $this->createdAt = new \DateTime('now', new \DateTimeZone('UTC'));
        $this->updatedAt = new \DateTime('now', new \DateTimeZone('UTC'));
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getToken(): ?string
    {
        return $this->token;
    }

    public function setToken(string $token): self
    {
        $this->token = $token;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getEntityId(): ?int
    {
        return $this->entityId;
    }

    public function setEntityId(int $entityId): self
    {
        $this->entityId = $entityId;

        return $this;
    }

    public function getExtra(): ?array
    {
        return $this->extra;
    }

    public function setExtra(?array $extra): self
    {
        $this->extra = $extra;

        return $this;
    }

    public function getValidFrom(): ?\DateTimeImmutable
    {
        return $this->validFrom;
    }

    public function setValidFrom(?\DateTimeImmutable $validFrom): self
    {
        $this->validFrom = $validFrom->setTimezone(new \DateTimeZone('UTC'));// Pass from any timezone to UTC

        return $this;
    }

    public function getValidUntil(): ?\DateTimeImmutable
    {
        return $this->validUntil;
    }

    public function setValidUntil(?\DateTimeImmutable $validUntil): self
    {
        $this->validUntil = $validUntil->setTimezone(new \DateTimeZone('UTC'));

        return $this;
    }

    public function isRevoked(): ?bool
    {
        return $this->revoked;
    }

    public function setRevoked(bool $revoked): self
    {
        $this->revoked = $revoked;

        return $this;
    }

    public function generateToken() {
        $data = [
            'id' => $this->getId(),
            'type' => $this->getType(),
            'entityId' => $this->getEntityId(),
            'createdAt' => $this->getCreatedAt()->format('c')
        ];

        $token = hash('sha256', json_encode($data));

        $this->setToken($token);
    }

    public function isValid($checkDate = true): bool
    {
        if ($this->isRevoked()) return false;

        if ($checkDate) {
            $currentDate = new \DateTime('now', new \DateTimeZone('UTC'));
            if (!empty($this->getValidFrom()) && $currentDate < $this->getValidFrom()) return false;
            if (!empty($this->getValidUntil()) && $currentDate > $this->getValidUntil()) return false;
        }

        return true;
    }

    public function isDateValid(): bool
    {
        $currentDate = (new \DateTime('now', new \DateTimeZone('UTC')));
        if (!empty($this->getValidFrom()) && $currentDate < $this->getValidFrom()) return false;
        if (!empty($this->getValidUntil()) && $currentDate > $this->getValidUntil()) return false;
        return true;
    }

    public function __toString()
    {
        return 'generic-token-' . $this->getId();
    }
}
