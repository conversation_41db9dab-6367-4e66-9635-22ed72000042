<?php

namespace App\Entity;

use App\Repository\ChatServerRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

/**
 * @ORM\Entity(repositoryClass=ChatServerRepository::class)
 * @UniqueEntity(fields={ "type", "entityId" })
 * @property $type The type of the server indicates the module: Course, Announcements, Itinerary, etc.
 */
class ChatServer
{
    /**
     * All direct messages will be handled by a single server. Direct message are considered all messages
     * not included in specific modules. Is communication User <-> User -> Channel with Users specified
     */
    public const TYPE_DIRECT = 'direct';
    public const TYPE_ANNOUNCEMENT = 'announcement';
    public const TYPE_COURSE = 'course';

    /**
     * Only one general server per application. Under this server will be multiple communications, with entityId = 0
     */
    public const TYPE_GENERAL = 'general';

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=45)
     */
    private ?string $type;

    /**
     * @ORM\Column(type="string", length=40)
     */
    private $entityId;

    /**
     * @ORM\Column(type="datetime_immutable")
     */
    private $createdAt;

    /**
     * @ORM\OneToMany(targetEntity=ChatChannel::class, mappedBy="serverId", orphanRemoval=true)
     */
    private $chatChannels;

    public function __construct()
    {
        $this->chatChannels = new ArrayCollection();
        $this->createdAt = new \DateTimeImmutable();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getEntityId(): ?string
    {
        return $this->entityId;
    }

    public function setEntityId(string $entityId): self
    {
        $this->entityId = $entityId;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeImmutable $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * @return Collection<int, ChatChannel>
     */
    public function getChatChannels(): Collection
    {
        return $this->chatChannels;
    }

    public function addChatChannel(ChatChannel $chatChannel): self
    {
        if (!$this->chatChannels->contains($chatChannel)) {
            $this->chatChannels[] = $chatChannel;
            $chatChannel->setServerId($this);
        }

        return $this;
    }

    public function removeChatChannel(ChatChannel $chatChannel): self
    {
        if ($this->chatChannels->removeElement($chatChannel)) {
            // set the owning side to null (unless already changed)
            if ($chatChannel->getServer() === $this) {
                $chatChannel->setServer(null);
            }
        }

        return $this;
    }
}
