<?php

namespace App\Admin\Traits;
use App\Entity\FilterCategory;
use App\Entity\Filter;
use App\Entity\User;

trait FilterCategoriesTrait{

    private function getFilterCategories()
    {
        $filterRepository = $this->em->getRepository(Filter::class);


        $filter_categories = array();

        if (!in_array("ROLE_ADMIN", $this->getUser()->getRoles())) {
            $user = $this->em->getRepository(User::class)->findOneBy(['email' => $this->getUser()->getUsername()]);
            $filtersManager = $user->getFilters();
            $filters_id = [];

            foreach ($filtersManager as $filter) {
                array_push($filters_id, $filter->getId());
            }

            if(!empty($filters_id)) {
                $filterRepository = $this->em->getRepository(Filter::class);
                $filtersCategory =  $filterRepository->getFilterCategoryManager($filters_id, $user->getLocale());

                foreach ($filtersCategory as $category) {
                    $filters = [];
                    //  $this->logger->error('nombre categoria' . $category['name']);
                    $filterUser = $filterRepository->getManagerFilters($user->getId(), $category['id']);

                    foreach ($filterUser as $fil) {
                        $filterTranslation =  $filterTranslation = $filter->translate($user->getLocale(), false);
                        array_push($filters, [
                            'id' => $fil->getId(),
                            'name' => $filterTranslation  && $filterTranslation->getName() ? $filterTranslation->getName() : $fil->getName()
                        ]);
                    }

                    usort($filters, function (array $elem1, $elem2) {
                        return $elem1['name'] <=> $elem2['name'];
                    });

                    array_push($filter_categories, [
                        'id' => $category['id'],
                        'name' => $category['name'] ,
                        'filters'  => $filters
                    ]);
                }
            }
        } else {
            foreach ($this->em->getRepository(FilterCategory::class)->getCategories() as $category) {
                $filters = [];
                foreach ($filterRepository->getFilterCategory($category->getId()) as $filter) {

                    $filterTranslation =   $filter->translate($this->getUser()->getLocale(), false);
                    $filters[] = [
                        'id' => $filter->getId(),
                        'name' => $filterTranslation  && $filterTranslation->getName() ? $filterTranslation->getName() : $filter->getName()
                    ];
                }

                usort($filters, function (array $elem1, $elem2) {
                    return $elem1['name'] <=> $elem2['name'];
                });

                $categoryTranslation = $category->translate($this->getUser()->getLocale(), false);

                $filter_categories[] = [
                    'id' => $category->getId(),
                    'name' => $categoryTranslation && $categoryTranslation->getName() ? $categoryTranslation->getName() : $category->getName(),
                    'filters' => $filters,
                ];
            }
        }

        return $filter_categories;
    }

}