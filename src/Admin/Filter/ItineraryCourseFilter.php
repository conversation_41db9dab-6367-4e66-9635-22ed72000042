<?php

namespace App\Admin\Filter;

use App\Entity\Course;
use App\Entity\Itinerary;
use App\Entity\ItineraryCourse;
use App\Form\Type\Admin\Filter\ItineraryCourseFilterType;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\QueryBuilder;
use EasyCorp\Bundle\EasyAdminBundle\Contracts\Filter\FilterInterface;
use EasyCorp\Bundle\EasyAdminBundle\Dto\EntityDto;
use EasyCorp\Bundle\EasyAdminBundle\Dto\FieldDto;
use EasyCorp\Bundle\EasyAdminBundle\Dto\FilterDataDto;
use EasyCorp\Bundle\EasyAdminBundle\Filter\FilterTrait;
use Psr\Log\LoggerInterface;

/**
 * Itineraries that contains a specific course
 */
class ItineraryCourseFilter implements FilterInterface
{
    use FilterTrait;

    public static function new (string $propertyName, $label): self {
        return (new self())
            ->setFilterFqcn(__CLASS__)
            ->setProperty($propertyName)
            ->setLabel($label)
            ->setFormType(ItineraryCourseFilterType::class);
    }


    public function apply(QueryBuilder $queryBuilder, FilterDataDto $filterDataDto, ?FieldDto $fieldDto, EntityDto $entityDto): void
    {
        if (in_array($filterDataDto->getComparison(), ['IN', 'NOT IN'])) {
            $queryBuilder->join(ItineraryCourse::class, 'ic', \Doctrine\ORM\Query\Expr\Join::WITH, 'entity = ic.itinerary');
            if ($filterDataDto->getComparison() == 'IN') {
                $queryBuilder->andWhere($queryBuilder->expr()->in('ic.course', $filterDataDto->getValue()));
            } else {
                $queryBuilder->andWhere($queryBuilder->expr()->notIn('ic.course', $filterDataDto->getValue()));
            }
        }
    }
}
