<?php

declare(strict_types=1);

namespace App\Security\Integrations;

/**
 * Basic definition for integration declaration.
 */
interface IntegrationMappingInterface
{
    /**
     * Entity the mapping is related to:
     * Null if the mapping is generic
     *
     * @return mixed|null
     */
    public function getEntity();

    /**
     * Identifier used in the entity. Valid only if Entity is not null.
     */
    public function getIdentifier(): ?string;

    /**
     * Get the type of the mapping.
     */
    public function getType(): string;

    /**
     * Get all mappings with the following format.
     *
     * @json {"localField": "remoteField"}
     */
    public function getMapping(): array;

    /**
     * Get group this mapping is allowed to be used. Null by default
     * If no auth endpoint is found, a mapping with auth declaration in the same group must be declared.
     */
    public function getGroup(): ?int;
}
