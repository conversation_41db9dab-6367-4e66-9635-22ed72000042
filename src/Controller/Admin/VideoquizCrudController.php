<?php

declare(strict_types=1);

namespace App\Controller\Admin;

use App\Admin\Traits\DeleteFilePathTrait;
use App\Admin\Traits\SerializerTrait;
use App\Entity\AnswersVideoQuiz;
use App\Entity\Chapter;
use App\Entity\Videopreguntas;
use App\Entity\Videoquiz;
use App\Service\SettingsService;
use App\Utils\UploadedBase64File;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;
use Vimeo\Vimeo;

class VideoquizCrudController extends AbstractCrudController
{
    use DeleteFilePathTrait;
    use SerializerTrait;

    private $em;
    private $requestStack;
    private $logger;
    private $context;
    protected $translator;
    protected $adminUrlGenerator;
    protected SettingsService $settings;

    public function __construct(
        EntityManagerInterface $em,
        RequestStack $requestStack,
        LoggerInterface $logger,
        AdminContextProvider $context,
        TranslatorInterface $translator,
        AdminUrlGenerator $adminUrlGenerator,
        SettingsService $settings
    ) {
        $this->em = $em;
        $this->requestStack = $requestStack;
        $this->logger = $logger;
        $this->context = $context;
        $this->translator = $translator;
        $this->adminUrlGenerator = $adminUrlGenerator;
        $this->settings = $settings;
    }

    public static function getEntityFqcn(): string
    {
        return Videoquiz::class;
    }

    /**
     * @Route("/admin/videoquiz/create", name="admin_videoquiz_create",methods={"GET","POST"})
     */
    public function createVideoQuiz(Request $request)
    {
        try {
            $fileVideo = $request->files->get('video');
            $idChapter = $request->get('idChapter');

            $client = $this->dataClientVimeo();
            $response_video = '';
            $user_id = '';
            $project_id = '';

            if ($fileVideo) {
                $response_video = $client->upload($fileVideo, [
                    'name' => $fileVideo->getClientOriginalName(),
                    'privacy' => [
                        'embed' => 'whitelist',
                    ],
                ]);

                $linkVimeo = $client->request($response_video . '?fields=link');
                $user_id = $this->settings->get('app.userIdVimeo');
                $project_id = $this->settings->get('app.projectIdVideoQuiz'); // Es el identificador de la carpeta
                $video_id = substr($response_video, 7); // Id del video que se esta subiendo

                $client->request("/users/$user_id/projects/$project_id/videos/$video_id", [], 'PUT');
            }

            if ($response_video) {
                $this->addVideoQuiz($request, $linkVimeo);
            }

            $videoQuiz = $this->em->getRepository(Videoquiz::class)->findOneBy(['chapter' => $idChapter]);

            $response = [
                'error' => false,
                'status' => Response::HTTP_OK,
                'data' => [
                    'routeChapter' => $this->urlChapter($idChapter),
                    'videoQuiz' => $videoQuiz,
                ],
            ];
        } catch (\Exception $e) {
            $response = [
                'error' => true,
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'data' => $e->getMessage(),
            ];
        }

        return $this->sendResponse($response, ['groups' => ['videoquiz']]);
    }

    public function addVideoQuiz($request, $linkVimeo)
    {
        $idChapter = $request->get('idChapter');
        $chapter = $this->em->getRepository(Chapter::class)->find($idChapter);
        $title = $request->get('title');
        $questions = json_decode($request->get('questions'), true);
        $durationVideo = (int) $request->get('durationVideo');

        $videoquiz = new Videoquiz();
        $videoquiz->setChapter($chapter);
        $videoquiz->setTitle($title);
        $videoquiz->setUrl($linkVimeo['body']['link']);
        $videoquiz->setVideoDuration($durationVideo);

        $this->em->persist($videoquiz);

        foreach ($questions as $question) {
            $videoQuestion = new Videopreguntas();
            $videoQuestion->setVideoquiz($videoquiz);
            $videoQuestion->setCurrenttime(\intval($question['seconds']));
            $videoQuestion->setTexto($question['question']);
            $videoQuestion->setRespuestas('vacio');

            if (null != $question['preview'] && '' != $question['preview'] && '/assets/common/add_image_file.svg' != $question['preview']) {
                $image = new UploadedBase64File($question['preview'], $question['id'] . '.png');
                $videoQuestion->setImage('');
                $videoQuestion->setImageFile($image);
            }

            $this->em->persist($videoQuestion);

            foreach ($question['answers'] as $answer) {
                $answerQuestion = new AnswersVideoQuiz();
                $answerQuestion->setQuestion($videoQuestion);
                $answerQuestion->setAnswer($answer['answer']);
                $answerQuestion->setIsCorrect($answer['correct']);
                $this->em->persist($answerQuestion);
            }
        }

        $this->em->flush();

        $response = [
            'success' => true,
        ];

        return $response;
    }

    private function urlChapter($chapterId)
    {
        return $this->adminUrlGenerator
            ->unsetAll()
            ->setController(ChapterCrudController::class)
            ->setAction('edit')
            ->setEntityId($chapterId)
            ->generateUrl();
    }

    private function dataClientVimeo()
    {
        $client_id = $this->settings->get('app.clientIdVimeo');
        $client_secret = $this->settings->get('app.clientSecretVimeo');
        $access_token = $this->settings->get('app.accessTokenVimeo');

        return new Vimeo($client_id, $client_secret, $access_token);
    }

    /**
     * @Route("/admin/videoquiz/{id}", name="admin_videoquiz", methods={"GET"})
     */
    public function getVideoQuiz(Chapter $chapter)
    {
        try {
            $videoQuiz = $this->em->getRepository(Videoquiz::class)->findOneBy(['chapter' => $chapter->getId()]);

            $response = [
                'error' => false,
                'status' => Response::HTTP_OK,
                'data' => [
                    'videoQuiz' => $videoQuiz,
                    'routeChapter' => $this->urlChapter($chapter->getId()),
                ],
            ];
        } catch (\Exception $e) {
            $response = [
                'error' => true,
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'data' => $e->getMessage(),
            ];
        }

        return $this->sendResponse($response, ['groups' => ['videoquiz']]);
    }

    // create function for update video quiz
    /**
     * @Route("/admin/videoquiz/update", name="admin_videoquiz_update", methods={"POST"})
     */
    public function updateVideoQuiz(Request $request)
    {
        try {
            $idVideoQuiz = $request->get('id');
            $idChapter = $request->get('idChapter');
            $chapter = $this->em->getRepository(Chapter::class)->find($idChapter);
            $title = $request->get('title');
            $questions = json_decode($request->get('questions'), true);
            $questionDeleted = json_decode($request->get('questionsDeleted'), true);

            $videoquiz = $this->em->getRepository(Videoquiz::class)->find($idVideoQuiz);
            $videoquiz->setChapter($chapter);
            $videoquiz->setTitle($title);

            $this->em->persist($videoquiz);

            if (count($questionDeleted) > 0) {
                foreach ($questionDeleted as $question) {
                    $answerQuestion = $this->em->getRepository(AnswersVideoQuiz::class)->findBy(['question' => $question['id']]);
                    foreach ($answerQuestion as $answer) {
                        $this->em->remove($answer);
                    }
                    $videoQuestion = $this->em->getRepository(Videopreguntas::class)->findOneBy(['id' => $question['id'], 'videoquiz' => $idVideoQuiz]);
                    if ($videoQuestion) {
                        $this->em->remove($videoQuestion);
                    }
                }
            }

            $this->updateQuestionQuiz($questions, $videoquiz);
            $this->em->flush();

            $videoQuiz = $this->em->getRepository(Videoquiz::class)->findOneBy(['chapter' => $idChapter]);
            $response = [
                'error' => false,
                'status' => Response::HTTP_OK,
                'data' => [
                    'routeChapter' => $this->urlChapter($idChapter),
                    'videoQuiz' => $videoQuiz,
                ],
            ];
        } catch (\Exception $e) {
            $response = [
                'error' => true,
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'data' => [
                    'message' => $e->getMessage(),
                ],
            ];
        }

        return $this->sendResponse($response, ['groups' => ['videoquiz']]);
    }

    private function updateQuestionQuiz($questions, $videoquiz): array
    {
        foreach ($questions as $question) {
            $videoQuestion = $this->em->getRepository(Videopreguntas::class)->findOneBy(['id' => $question['id'], 'videoquiz' => $videoquiz->getId()]);
            if ($videoQuestion) {
                $videoQuestion->setTexto($question['question']);
                if (null != $question['image']) {
                    if (!\is_null($videoQuestion->getImage())) {
                        $this->deleteFile($this->settings->get('app.gameVideoquiz_uploads_path'), $videoQuestion->getImage());
                    }
                    $image = new UploadedBase64File($question['image'], $question['id'] . '.png');
                    $videoQuestion->setImage('');
                    $videoQuestion->setImageFile($image);
                }

                $this->em->persist($videoQuestion);

                foreach ($question['answers'] as $answer) {
                    $answerQuestion = $this->em->getRepository(AnswersVideoQuiz::class)->findOneBy(['id' => $answer['id'], 'question' => $question['id']]);

                    if ($answerQuestion) {
                        $answerQuestion->setAnswer($answer['answer']);
                        $answerQuestion->setIsCorrect($answer['correct']);
                        $this->em->persist($answerQuestion);
                    } else {
                        $answerQuestion = new AnswersVideoQuiz();
                        $answerQuestion->setQuestion($videoQuestion);
                        $answerQuestion->setAnswer($answer['answer']);
                        $answerQuestion->setIsCorrect($answer['correct']);
                        $this->em->persist($answerQuestion);
                    }
                }

                if (isset($question['answersDeleted']) && count($question['answersDeleted']) > 0) {
                    foreach ($question['answersDeleted'] as $answer) {
                        $answerQuestion = $this->em->getRepository(AnswersVideoQuiz::class)->findOneBy(['id' => $answer['id'], 'question' => $question['id']]);
                        $this->em->remove($answerQuestion);
                    }
                }
            } else {
                $videoQuestion = new Videopreguntas();
                $videoQuestion->setVideoquiz($videoquiz);
                $videoQuestion->setCurrenttime(\intval($question['seconds']));
                $videoQuestion->setTexto($question['question']);
                $videoQuestion->setRespuestas('vacio');
                if (null != $question['image']) {
                    $image = new UploadedBase64File($question['image'], $question['id'] . '.png');
                    $videoQuestion->setImage('');
                    $videoQuestion->setImageFile($image);
                }
                $this->em->persist($videoQuestion);

                foreach ($question['answers'] as $answer) {
                    $answerQuestion = new AnswersVideoQuiz();
                    $answerQuestion->setQuestion($videoQuestion);
                    $answerQuestion->setAnswer($answer['answer']);
                    $answerQuestion->setIsCorrect($answer['correct']);
                    $this->em->persist($answerQuestion);
                }
            }
        }

        return [$question, $videoQuestion];
    }

    /**
     * @Route("/admin/videoquiz/delete", name="admin_videoquiz_delete", methods={"POST"})
     */
    public function deleteVideoQuiz(Request $request)
    {
        try {
            $idVideoQuiz = $request->get('id');
            $videoQuiz = $this->em->getRepository(Videoquiz::class)->find($idVideoQuiz);
            $questions = $this->em->getRepository(Videopreguntas::class)->findBy(['videoquiz' => $idVideoQuiz]);

            $client = $this->dataClientVimeo();

            $user_id = $this->settings->get('app.userIdVimeo');
            $project_id = $this->settings->get('app.projectIdVideoQuiz'); // Es el identificador de la carpeta

            $identifierVideo = $videoQuiz->getIdentifierVideo();

            $client->request("/users/$user_id/projects/$project_id/videos/$identifierVideo", [], 'DELETE');

            foreach ($questions as $question) {
                $answers = $this->em->getRepository(AnswersVideoQuiz::class)->findBy(['question' => $question->getId()]);
                foreach ($answers as $answer) {
                    $this->em->remove($answer);
                }

                if (!\is_null($question->getImage())) {
                    $this->deleteFile($this->settings->get('app.gameVideoquiz_uploads_path'), $question->getImage());
                }
                $this->em->remove($question);
            }

            $this->em->remove($videoQuiz);
            $this->em->flush();

            $response = [
                'error' => false,
                'status' => Response::HTTP_OK,
                'data' => 'VideoQuiz eliminado correctamente',
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'Error to delete question: {' . $e->getMessage() . '}',
            ];
        }

        return $this->sendResponse($response);
    }
}
