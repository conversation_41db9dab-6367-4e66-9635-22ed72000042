<?php

declare(strict_types=1);

namespace App\Campus\Games;

use App\Campus\Service\Base\BaseService;
use App\Entity\Chapter;
use App\Enum\Games as GamesEnum;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Security;

class GameService extends BaseService
{
    protected RequestStack $requestStack;

    public function __construct(EntityManagerInterface $em, SettingsService $settings, RequestStack $requestStack, Security $security)
    {
        $this->requestStack = $requestStack;
        parent::__construct($em, $settings, $security);
    }

    // Function that calculates the percentage of points obtained in a game.
    // This percentage will be multiplied later by the maximum score of the game.
    // ** Does NOT return points directly. ** O_O
    public function calculateGamePoints(Chapter $chapter, $data)
    {
        $strategies = [
            GamesEnum::WHEEL_CODE => new Wheel($this->em),
            GamesEnum::QUIZ_CODE => new Quiz($this->em),
            GamesEnum::TRUE_OR_FALSE_CODE => new TrueOrFalse($this->em, $this->settings),
            GamesEnum::WHERE_DOES_IT_FIT_CODE => new WhereDoesItFit($this->em, $this->settings),
            GamesEnum::FILL_IN_THE_BLANKS_CODE => new FillInTheBlanks($this->em),
            GamesEnum::MATCH_CODE => new MemMatch($this->em, $this->settings, $this->requestStack),
            GamesEnum::HIGHER_OR_LOWER_CODE => new HigherOrLower($this->em),
            GamesEnum::RIDDLE_CODE => new Riddle($this->em, $this->settings),
            GamesEnum::SORT_LETTERS_CODE => new SortLetters($this->em),
            GamesEnum::ENIGMA_CODE => new Enigma($this->em),
            GamesEnum::WORD_SEARCH_CODE => new WordSearch($this->em),
            GamesEnum::VIDEO_QUIZ_CODE => new VideoQuiz($this->em, $this->settings),
            GamesEnum::PUZZLE_CODE => new Puzzle($this->em, $this->settings),
            GamesEnum::SECRET_WORD_CODE => new SecretWord($this->em),
            GamesEnum::DOUBLE_OR_NOTHING_CODE => new DoubleOrNothing($this->em),
            GamesEnum::LETTERS_WHEEL_CODE => new LettersWheel($this->em),
        ];
        $game = new GameContextStrategy();
        $game->setStrategy($strategies[$chapter->getType()->getCode()] ?? null, $data, $chapter);

        return $game->calculateGamePoints();
    }
}
