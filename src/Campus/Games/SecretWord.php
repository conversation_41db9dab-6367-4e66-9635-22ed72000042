<?php

declare(strict_types=1);

namespace App\Campus\Games;

use App\Entity\Chapter;
use App\Entity\Question;
use App\Enum\Games as EnumGameFormula;
use App\Enum\Games as GamesEnum;

class SecretWord extends Game
{
    public function getQuestions($userCourseChapter): array
    {
        $repository = $this->em->getRepository(Question::class);
        $quizQuestions = $repository->findBy([
            'chapter' => $userCourseChapter->getChapter()->getId(),
        ]);

        $questions = $this->getFormattedQuestions($quizQuestions);

        return [
            'questions' => $questions,
        ];
    }

    public function check($userCourseChapter, $answers): array
    {
        $repository = $this->em->getRepository(Question::class);
        $question = $repository->find($answers->questionId);
        $answer = $question->getAnswers()[0];
        $expectedAnswer = $answer->getAnswer();
        $attempts = $answers->attempts;
        $corrects = 0;
        $incorrects = 0;

        if (empty($attempts) || 0 === \count($attempts)) {
            $incorrects = \strlen($expectedAnswer);
        } else {
            foreach ($attempts as $attempt) {
                if (str_contains(strtolower($expectedAnswer), $attempt->letter)) {
                    ++$corrects;
                } else {
                    ++$incorrects;
                }
            }
        }

        $letters = array_map(function ($attempt) {
            return $attempt->letter;
        }, $attempts);

        $word = implode('', $letters);

        return [
            'result' => [
                // 'correct' => 0 === $incorrects,
                'value' => $word,
                'corrects' => $corrects,
                'incorrects' => $incorrects,
            ],
        ];
    }

    private function getFormattedQuestions($data): array
    {
        $questions = [];
        foreach ($data as $question) {
            $time = $this->calculateReadingAverageTime($question->getQuestion());
            $answers = $this->getFormattedAnswers($question->getAnswers());

            $questions[] = [
                'id' => $question->getId(),
                'question' => $question->getQuestion(),
                'imageUrl' => $question->getImageUrl(),
                'random' => $question->getRandom(),
                // 'time' => $time + self::AVERAGE_TIME_TO_ANSWER,
                'answers' => $answers,
                'time' => $question->getTime() + GamesEnum::SECRET_WORD_AVERAGE_TIME_TO_ANSWER,
                // todo - 'clues' => [1,2,...] - not loaded yet
            ];
        }

        return $questions;
    }

    private function getFormattedAnswers($quizAnswers): array
    {
        $answers = [];
        foreach ($quizAnswers as $answer) {
            $answers[] = [
                'id' => $answer->getId(),
                'answer' => $answer->getAnswer(),
            ];
        }

        return $answers;
    }

    public function calculateGamePoints($data, $args = null)
    {
        if (!$args instanceof Chapter) {
            return null;
        }

        if (
            !isset($data['answers']) || !$data['answers'] > 0
            || !isset($data['timeTotal'])
        ) {
            return 0;
        }

        $chapterType = $args->getType();
        $answers = $data['answers'];
        $nAnswers = \count($answers);
        $rightAnswers = 0;
        $incorrect = 0;
        $time = 0;
        $maxTime = $data['timeTotal'];
        $nAttempts = \array_key_exists('attempts', $data) && \is_array($data['attempts'])
            ? \count($data['attempts']) : 1;


        if($nAnswers > 0){
            foreach ($answers as $answer) {
                if (isset($answer['correct']) && $answer['correct']) {
                    ++$rightAnswers;
                }
                $incorrect += $answer['incorrects'];
                $time += $answer['time'];
            }
        }

        $completionPercentage = $nAnswers > 0 ? ($rightAnswers / $nAnswers) : 0;
        if ($completionPercentage < $chapterType->getPercentageCompleted()) {
            return 0;
        }

        $remainingTime = ($maxTime - $time) > 0 ? ($maxTime - $time) : 1;
        $basePercentage = EnumGameFormula::BASE_HALF
            + (
                EnumGameFormula::BASE_HALF * (
                    ((EnumGameFormula::SECRET_WORD_LETTERS * $nAnswers) - $incorrect)
                    / (EnumGameFormula::SECRET_WORD_LETTERS * $nAnswers)
                ) * ($remainingTime / ($maxTime * $nAnswers))
            );
        $adjustedPercentage = pow($basePercentage, EnumGameFormula::E_L_COEF);
        $attemptsCorrectionPercentage = $adjustedPercentage * pow(EnumGameFormula::ATTEMPTS_COEF, $nAttempts - 1);

        return $attemptsCorrectionPercentage;
    }
}
