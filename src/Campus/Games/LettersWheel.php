<?php

declare(strict_types=1);

namespace App\Campus\Games;

use App\Entity\Chapter;
use App\Entity\RouletteWord as RouletteWordEntity;
use App\Entity\TimeGame;
use App\Enum\Games as EnumGameFormula;
use App\Enum\Games as GamesEnum;

class LettersWheel extends Game
{
    public function getQuestions($userCourseChapter): array
    {
        $repository = $this->em->getRepository(RouletteWordEntity::class);
        $letters = $repository->findBy([
            'chapter' => $userCourseChapter->getChapter()->getId(),
        ]);

        $timeGame = $this->em->getRepository(TimeGame::class)->findOneBy(['chapter' => $userCourseChapter->getChapter()->getId()]);
        $time = $timeGame ? $timeGame->getTime() : 30;

        $questions = $this->getFormattedQuestions($letters);
        $timeTotal = $this->getTotalTime($questions);

        return [
            'questions' => $questions,
            'time' => $timeTotal + $time,
        ];
    }

    public function check($userCourseChapter, $answers): array
    {
        $repository = $this->em->getRepository(RouletteWordEntity::class);
        $letters = $repository->findOneBy([
            'id' => $answers->questionId,
        ]);

        $isCorrect = $this->isCorrect($answers, $letters->getWord());

        return [
            'correct' => $isCorrect,
        ];
    }

    private function getFormattedQuestions($letters): array
    {
        $questions = [];
        foreach ($letters as $letter) {
            $time = $this->calculateReadingAverageTime($letter->getQuestion());
            $questions[] = [
                'id' => $letter->getId(),
                'letter' => $letter->getLetter(),
                'question' => $letter->getQuestion(),
                'type' => $letter->getType(),
                'time' => $time + GamesEnum::LETTERS_WHEEL_AVERAGE_TIME_TO_ANSWER,
            ];
        }

        return $questions;
    }

    private function getTotalTime($questions)
    {
        $time = 0;
        foreach ($questions as $letter) {
            $time += $letter['time'];
        }

        return $time;
    }

    private function isCorrect($answer, $expectedWord): bool
    {
        if (!isset($answer) || !isset($answer->value) || \is_null($answer->value)) {
            return false;
        }

        return mb_strtolower($answer->value, 'UTF-8') === mb_strtolower($expectedWord, 'UTF-8');
    }

    public function calculateGamePoints($data, $args = null)
    {
        if (!$args instanceof Chapter) {
            return null;
        }

        if (!isset($data['answers']) || !$data['answers'] > 0
            || !isset($data['timeTotal'])) {
            return 0;
        }

        $chapterType = $args->getType();
        $answers = $data['answers'];
        $percentageForComplete = $chapterType->getPercentageCompleted();
        $nAnswers = \count($answers);
        $rightAnswers = 0;
        $time = 0;
        $maxTime = $data['timeTotal'];
        $needCorrectQuestions = round($nAnswers * $percentageForComplete, 1);
        $nAttempts = \array_key_exists('attempts', $data) && \is_array($data['attempts']) ? \count($data['attempts']) : 1;

        if($nAnswers > 0){
            foreach ($answers as $answer) {
                if (isset($answer['correct']) && $answer['correct']) {
                    ++$rightAnswers;
                }
                $time += $answer['time'];
            }
        }

        $completionPercentage = $nAnswers > 0 ? ($rightAnswers / $nAnswers) : 0;
        if ($completionPercentage < $chapterType->getPercentageCompleted()) {
            return 0;
        }

        if (0 == $maxTime) {
            return 0;
        }

        $remainingTime = ($maxTime - $time) > 0 ? ($maxTime - $time) : 1;
        $basePercentage = EnumGameFormula::BASE_HALF + (EnumGameFormula::BASE_QUARTER * (($rightAnswers - $needCorrectQuestions) / ($nAnswers - $needCorrectQuestions))) + (EnumGameFormula::BASE_QUARTER * ($remainingTime / $maxTime));
        $adjustedPercentage = pow($basePercentage, EnumGameFormula::E_L_COEF);
        $attemptsCorrectionPercentage = $adjustedPercentage * pow(EnumGameFormula::ATTEMPTS_COEF, $nAttempts - 1);

        return $attemptsCorrectionPercentage;
    }
}
