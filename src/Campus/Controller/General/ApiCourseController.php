<?php

declare(strict_types=1);

namespace App\Campus\Controller\General;

use App\Entity\Announcement;
use App\Entity\AnnouncementUser;
use App\Entity\Course;
use App\Entity\TypeCourse;
use App\Entity\User;
use App\Repository\AnnouncementRepository;
use App\Repository\CourseRepository;
use App\Repository\NpsRepository;
use App\Repository\UserRepository;
use App\Service\Api\TeleformationDetailService;
use App\Service\Course\GlobalFilter;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\Annotations as Rest;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTTokenManagerInterface;
use Lexik\Bundle\JWTAuthenticationBundle\TokenExtractor\TokenExtractorInterface;
use Psr\Log\LoggerInterface;
use Swagger\Annotations as SWG;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * Class ApiController.
 *
 * @Route("/api")
 */
class ApiCourseController extends ApiBaseController
{
    private TeleformationDetailService $TeleformationDetailService;
    private TokenExtractorInterface $tokenExtractor;
    private JWTTokenManagerInterface $jwtManager;
    private EntityManagerInterface $em;
    private NpsRepository $npsRepository;

    /**
     * ApiController constructor.
     */
    public function __construct(
        LoggerInterface $logger,
        UserRepository $userRepository,
        CourseRepository $courseRepository,
        AnnouncementRepository $announcementRepository,
        TranslatorInterface $translator,
        TeleformationDetailService $TeleformationDetailService,
        TokenExtractorInterface $tokenExtractor,
        JWTTokenManagerInterface $jwtManager,
        SettingsService $settingsService,
        EntityManagerInterface $em,
        NpsRepository $npsRepository
    ) {
        parent::__construct($logger, $userRepository, $courseRepository, $announcementRepository, $translator, $settingsService, $em);
        $this->TeleformationDetailService = $TeleformationDetailService;
        $this->tokenExtractor = $tokenExtractor;
        $this->jwtManager = $jwtManager;
        $this->em = $em;
        $this->npsRepository = $npsRepository;
    }

    /**
     * @Rest\Get("/courses/{id}", name="api_information_course")
     *
     * @SWG\Response(
     *     response=200,
     *     description="Detail of the course"
     * )
     * @SWG\Response(
     *     response=404,
     *     description="Course not found"
     * )
     * @SWG\Response(
     *     response=401,
     *     description="The user has no access to this course"
     * )
     * @SWG\Response(
     *     response=500,
     *     description="Error"
     * )
     *
     * @SWG\Tag(name="Courses")
     */
    public function course(Request $request, Course $course)
    {
        $isNew = $request->get('new', false);

        $this->validateTokenCourseId($request, $course);

        try {
            $code = Response::HTTP_OK;
            $error = false;

            if (!$this->checkCourseAccess($course)) {
                $code = Response::HTTP_UNAUTHORIZED;
                $error = true;
                $message = 'The user has no access to this course';
            } elseif (!$this->checkAccessAnnouncement($request)) {
                $code = Response::HTTP_NOT_FOUND;
                $error = false;
                $message = 'Announcement not found';
            } else {
                $user = $this->getUser();
                $dataCourse = $this->TeleformationDetailService->buildCourseResponse($course, $user);
            }
        } catch (\Exception $e) {
            $errorMessage = \sprintf('Error on line  %d of %s: %s', $e->getLine(), $e->getFile(), $e->getMessage());

            return $this->sendResponse(['error' => true, 'status' => 500, 'message' => $errorMessage]);
        }

        $response = [
            'status' => $code,
            'error' => $error,
            'data' => Response::HTTP_OK == $code ?
                $dataCourse : $message,
        ];

        $this->logger->debug('Course: ' . $course->getName());

        $context = [
            'groups' => ['detail', 'valoration', 'material', 'fileable', 'user_area'],
        ];
        if ($isNew) {
            $context['datetime_format'] = 'c';
        }

        return $this->sendResponse($response, $context);
    }

    private function checkAccessAnnouncement(Request $request)
    {
        $token = $this->tokenExtractor->extract($request);
        $payload = $this->jwtManager->parse($token);

        if ($payload && !empty($payload['announcement'])) {
            return true;
        }

        $idAnnouncement = $request->get('idAnnouncement', null);
        if (!$idAnnouncement) {
            return true;
        }

        $announcement = $idAnnouncement ? $this->em->getRepository(Announcement::class)->find($idAnnouncement) : null;

        if (!$announcement) {
            return false; // Return false if the announcement does not exist
        }

        $announcementUser = $this->em->getRepository(AnnouncementUser::class)->findOneBy([
            'announcement' => $announcement,
            'user' => $this->getUser(),
        ]);

        return null !== $announcementUser;
    }

    private function validateTokenCourseId(Request $request, Course $course)
    {
        /**
         * Make sure if the token have a courseId parameter in payload, if exists, validate that the course defined
         * is the current course that has been called.
         */
        $token = $this->tokenExtractor->extract($request);
        $payload = $this->jwtManager->parse($token);

        if ($payload && !empty($payload['course']) && $payload['course'] !== $course->getId()) {
            return $this->sendResponse([
                'status' => Response::HTTP_UNAUTHORIZED,
                'error' => true,
                'data' => 'Course ID defined in the token. Not allowed to see other courses',
            ]);
        }
    }

    /**
     * @Rest\Get("/search/{name}", name="globalSearchCourseList")
     */
    public function getCourseList(Request $request): Response
    {
        $globalFilter = new GlobalFilter($this->em);
        $id = $this->getUser()->getId();
        $name = $request->get('name', '');
        $data = $globalFilter->getFilterData($id, $name);

        return $this->sendResponse([
            'status' => 200,
            'error' => false,
            'data' => $data,
        ]);
    }

    /**
     * @Rest\Get("/course/{id}/opinions", name="course-opinions")
     */
    public function getCourseOpinions(Request $request, Course $course): Response
    {
        try {
            $queryParameters = $this->normalizeQueryParameters($request, $course);

            $opinionsQuery = $this->npsRepository->getOpinionsByCourse($queryParameters);
            $totalItems = $this->npsRepository->countOpinionsCourse($queryParameters);

            return $this->sendResponse([
                'status' => 200,
                'error' => false,
                'data' => $this->transformOpinions($opinionsQuery),
                'total-items' => (int) $totalItems,
            ]);
        } catch (\Exception $e) {
            $errorMessage = \sprintf('Error on line  %d of %s: %s', $e->getLine(), $e->getFile(), $e->getMessage());

            return $this->sendResponse(['error' => true, 'status' => 500, 'message' => $errorMessage]);
        }
    }

    private function transformOpinions(array $opinionsQuery)
    {
        $isShowOnlyRatings = $this->settings->get('app.survey.show_only_ratings');
        $avatarPath = '/' . $this->settings->get('app.avatar_uploads_path') . '/';
        $opinions = [];
        foreach ($opinionsQuery as $opinion) {
            // $date = DateTime::createFromFormat('Y-m-d H:i:s.u', $opinion['createdAt']);
            $opinions[] = [
                'name' => $opinion['firstName'],
                'image' => $opinion['avatar'] ? $avatarPath . $opinion['avatar'] : null,
                'date' => isset($opinion['createdAt']) ? $opinion['createdAt']->format('Y-m-d\TH:i:s.v\Z') : null,
                'text' => $isShowOnlyRatings ? null : $opinion['text'],
                'rating' => $opinion['rating'] && $opinion['rating'] > 0 ? \floatval($opinion['rating']) / 2 : null,
            ];
        }

        return $opinions;
    }

    private function normalizeQueryParameters(Request $request, Course $course): array
    {
        $isEmptyComment = $this->settings->get('app.survey.hide.empty_comment');
        $queryParameters = [
            'page' => (int) $request->get('page', 1),
            'pageSize' => (int) $request->get('pageSize', 20),
            'courseId' => $course->getId(),
            'typeCodeCourse' => $course->getTypeCourse() ? $course->getTypeCourse()->getCode() : TypeCourse::CODE_ONLINE,
            'isEmptyComment' => $isEmptyComment,
            'announcementId' => $request->get('announcementId') ? (int) $request->get('announcementId') : null,
        ];

        return $queryParameters;
    }
}
