<?php

namespace App\Campus\Controller\General;

use SWG\Parameter;
use App\Entity\User;
use SWG\RequestBody;
use App\Entity\Course;
use App\Entity\Announcement;
use Psr\Log\LoggerInterface;
use App\Enum\EmailNotificacion;
use Swagger\Annotations as SWG;
use App\Entity\UserNotification;
use App\Entity\EmailNotification;
use App\Repository\UserRepository;
use App\Admin\Traits\LanguagesTrait;
use App\Repository\CourseRepository;
use App\Repository\UserCourseRepository;
use Doctrine\ORM\EntityManagerInterface;
use App\Campus\Controller\General\ApiBaseController;
use App\Service\Notification\EmailNotificationService;
use App\Repository\AnnouncementRepository;
use App\Admin\Traits\EmailNotificationTrait;
use Nelmio\ApiDocBundle\Annotation\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\HttpFoundation\Response;
use App\Repository\EmailNotificationRepository;
use App\Service\Geolocation\GeolocationService;
use App\Service\SettingsService;
use DateTime;
use DateTimeImmutable;
use Symfony\Component\Routing\Annotation\Route;
use Vich\UploaderBundle\Handler\DownloadHandler;
use FOS\RestBundle\Controller\Annotations as Rest;
use IntlDateFormatter;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * Class ApiController
 * @package App\Controller
 *
 * @Route("/api")
 */
class  ApiEmailNotificationController extends ApiBaseController implements EmailNotificacion
{
	use LanguagesTrait;
	use EmailNotificationTrait;

	private $em;
	private $mailer;
	private GeolocationService $geolocationService;

	/**
	 * ApiController constructor.
	 *
	 * @param $logger
	 */
	public function __construct(LoggerInterface $logger, UserRepository $userRepository, CourseRepository $courseRepository, AnnouncementRepository $announcementRepository, EntityManagerInterface $em, TranslatorInterface $translator, MailerInterface $mailer, SettingsService	 $settingsService, GeolocationService $geolocationService)
	{
		parent::__construct($logger, $userRepository, $courseRepository, $announcementRepository, $translator, $settingsService);
		$this->em = $em;
		$this->mailer = $mailer;
		$this->geolocationService = $geolocationService;
	}

	/**
	 * Show list pending notifications by user
	 *
	 * @Rest\Get("/email-notifications", name="api_email_notification_list")
	 * @Security(name="Bearer")
	 * @SWG\Tag(name="EmailNotification")
	 * @SWG\Response(
	 * 		response=200,
	 *    description="Ok response",
	 *    	@SWG\Schema(
	 *        @SWG\Items(
	 *          @SWG\Property(
	 *             property="status",
	 *             type="integer",
	 *           	example=200
	 *          ),
	 *          @SWG\Property(
	 *             property="error",
	 *             type="boolean",
	 *           	example=false
	 *           ),
	 *           @SWG\Property(
	 *               property="data",
	 *               type="array",
	 *               @SWG\Items(
	 *               	@SWG\Property(
	 *               		property="id",
	 *               		type="integer",
	 *               		description="Notification id",
	 *               		example=15
	 *           			),
	 *           			@SWG\Property(
	 *               		property="type",
	 *               		type="string",
	 *               		description="Internal type name from this notifications",
	 *               		example="itinerary"
	 *           			),
	 *           			@SWG\Property(
	 *               		property="sent",
	 *               		type="boolean",
	 *               		example=false
	 *           			),
	 *           			@SWG\Property(
	 *               		property="createdAt",
	 *               		type="datetime",
	 *               		example="09-05-2023 15:36"
	 *           			),
	 *           			@SWG\Property(
	 *               		property="title",
	 *               		type="string",
	 *               		example="Estimado/a PISOS AME | Camaristas."
	 *           			),
	 *           			@SWG\Property(
	 *               		property="message",
	 *               		type="string",
	 *               		example="Tu itinerario formativo personalizado ya está disponible"
	 *           			)
	 *           	 )
	 *          )
	 *			 )
	 *    )
	 * )
	 * @SWG\Parameter(
	 * 		name="lang",
	 *    description="Language to show notifications (2 letters format)",
	 *    type="string",
	 * 		required=false,
	 *    in="query"
	 *  )
	 * @SWG\Parameter(
	 * 		name="seeLast30",
	 *    description="View only notifications from the last 30 days. Submit any value to use this filter. In order not to use it, send empty or not send it",
	 *    type="string",
	 * 		required=false,
	 *    in="query"
	 *  )
	 *
	 * @param Request $request
	 *
	 * @return Response
	 */
	public function list(Request $request, EmailNotificationService $emailNotificationService): Response
    {
        try {
            $notifications = $emailNotificationService->listUserEmailNotifications($this->getUser(), $request);
            return $this->sendResponse([
                'status' => 200,
                'error' => false,
                'data' =>  ['notifications' => $notifications],
            ]);
        } catch (\Exception $e) {
            $errorMessage = sprintf('Error on line  %d of %s: %s', $e->getLine(), $e->getFile(), $e->getMessage());
            return $this->sendResponse(['error' => true, 'status' => 500, 'message' => $errorMessage]);
        }

//		try {
//			if ($request->get('lang')) {
//				$lang = $request->get('lang');
//			} else {
//				$lang = $this->getUser()->getLocale();
//			}
//
//			$emailNotificationRepository = $this->em->getRepository(EmailNotification::class);
//			$notifications = $emailNotificationRepository->listAllPending($this->getUser()->getId(), $request);
//
//			if (!empty($notifications)) {
//				//$notifications = [];
//				$notifications = $this->addNotificationInfo($notifications, $lang);
//			}
//
//			foreach ($notifications as &$valor) {
//				if (in_array($valor['type'], self::EMAIL_NOTIFICATIONS)) {
//					$attributes = is_null($valor['attributes']) ? [] :  $valor['attributes'];
//
//					if (empty($valor['message']) && empty($attributes)) {
//						$valor['title'] = $this->translator->trans($valor['translationTitle'], [], 'emailNotification', $lang);
//						$valor['message'] = $this->translator->trans($valor['translationText'], [], 'emailNotification', $lang);
//					} else if (empty($valor['message']) && !empty($attributes)) {
//						$attributesTrans = [];
//						if (sizeof($valor['attributes']) > 0) {
//							$attributesTrans = current($valor['attributes']);
//						}
//
//
//						$valor['title'] = $this->translator->trans($valor['translationTitle'], [], 'emailNotification', $lang);
//						$valor['message'] = $this->translator->trans($valor['translationText'], $attributesTrans, 'emailNotification', $lang);
//					}
//					unset($valor['attributes']);
//				}
//			}
//
//			$code = Response::HTTP_OK;
//			$error = false;
//			$message = [];
//			$timezoneUser = $this->geolocationService->getTimeZoneConnection();
//			foreach ($notifications as $item) {
//				//$createdAt = new DateTime($item['createdAt']);
//				$createdAt = $this->geolocationService->convertTimeZone($item['createdAt'], 'Europe/Madrid', $timezoneUser);
//				$newDate = new DateTime($createdAt);
//
//				// Formatear la fecha
//				$locale = 'en'; // Puedes obtener el idioma del usuario desde tu aplicación
//				$formatter = new IntlDateFormatter($locale, IntlDateFormatter::SHORT, IntlDateFormatter::SHORT);
//
//				// Formatear la fecha
//				$formattedDate = $formatter->format($newDate);
//
//
//				array_push($message, [
//					'id' 				=> $item['id'],
//					'type' 			=> $item['type'],
//					'sent'			=> $item['sent'],
//					'createdAt' => $item['createdAt'],
//					'title' 		=> $item['title'],
//					'message' 	=> $item['message'],
//					'date' => $formattedDate
//				]);
//			}
//		} catch (\Exception $e) {
//			$errorMessage = sprintf('Error on line  %d of %s: %s', $e->getLine(), $e->getFile(), $e->getMessage());
//			return $this->sendResponse(['error' => true, 'status' => 500, 'message' => $errorMessage]);
//		}
//
//		return $this->sendResponse([
//			'status' => $code,
//			'error' => $error,
//			'data' =>  ['notifications' => $message],
//
//
//		]);
	}

	/**
	 * Update status notifications
	 *
	 * @Rest\Patch("/email-notifications", name="api_email_notification_update")
	 * @Security(name="Bearer")
	 * @SWG\Tag(name="EmailNotification")
	 * @SWG\Response(
	 *     response=200,
	 *     description="Ok response"
	 * )
	 * @SWG\Parameter(
	 * 		name="data",
	 *    description="Array notifications with new status",
	 * 		required=true,
	 * 		type="string",
	 *    in="body",
	 *        @SWG\Schema(
	 *           @SWG\Items(
	 *              @SWG\Property(
	 *                  property="data",
	 *                  type="array",
	 *                  @SWG\Items(
	 *                  	@SWG\Property(
	 *                  		property="id",
	 *                  		type="integer",
	 *                  		description="Notification id",
	 *                  		example=15
	 *              			),
	 *              			@SWG\Property(
	 *                  		property="read",
	 *                  		type="boolean",
	 *                  		description="New status: Readed (true Or 1) Or Pending (false Or 0)",
	 *                  		example=0
	 *              			)
	 *              	  )
	 *             )
	 *	        )
	 *       )
	 * )
	 *
	 * @param $id
	 * @param Request $request
	 *
	 * @return Response
	 */
	public function update(Request $request)
	{
		try {
			$data = json_decode($request->getContent(), true);

			foreach ($data as $item) {
				if ($item['read'] === true) {
					$read = 1;
				} elseif ($item['read'] === false) {
					$read = 0;
				} else {
					$read = $item['read'];
				}

				$emailNotification = $this->em->getRepository(EmailNotification::class)->find($item['id']);
				$emailNotification->setSent($read);
				$this->em->persist($emailNotification);
				$this->em->flush();

				$emailNotificationRepository = $this->em->getRepository(EmailNotification::class);
				//$emailNotificationRepository->markSentItems([['id' => $item['id']]], $read);
			}

			$code = Response::HTTP_OK;
			$error = false;
			$message = 'OK';
		} catch (\Exception $e) {
			$code = Response::HTTP_INTERNAL_SERVER_ERROR;
			$error = true;
			$message = 'An error has occurred trying to get - Error: {' . $e->getMessage() . '}';
		}

		return $this->sendResponse([
			'status' => $code,
			'error' => $error,
			'data' => $message
		]);
	}

	/**
	 * Delete (soft) notifications
	 *
	 * @Rest\Post("/email-notifications/delete", name="api_email_notification_delete")
	 * @Security(name="Bearer")
	 * @SWG\Tag(name="EmailNotification")
	 * @SWG\Response(
	 *     response=200,
	 *     description="Ok response"
	 * )
	 * @SWG\Parameter(
	 * 		name="data",
	 *    description="ID Array notifications to delete",
	 * 		required=true,
	 * 		type="string",
	 *    in="body",
	 *        @SWG\Schema(
	 *           @SWG\Items(
	 *              @SWG\Property(
	 *                  property="data",
	 *                  type="array",
	 *                  @SWG\Items(
	 *                  	@SWG\Property(
	 *                  		property="id",
	 *                  		type="integer",
	 *                  		description="Notification id",
	 *                  		example=15
	 *              			)
	 *              	  )
	 *             )
	 *	        )
	 *       )
	 * )
	 *
	 * @param $id
	 * @param Request $request
	 *
	 * @return Response
	 */
	public function delete(Request $request)
	{
		try {
			$data = json_decode($request->getContent(), true);
			$emailNotificationRepository = $this->em->getRepository(EmailNotification::class);

			$arrNumber = [];
			foreach ($data as $item) {
				array_push($arrNumber, $item['id']);
			}

			$emailNotificationRepository->markDeletedItems($arrNumber);

			$code = Response::HTTP_OK;
			$error = false;
			$message = 'OK';
		} catch (\Exception $e) {
			$code = Response::HTTP_INTERNAL_SERVER_ERROR;
			$error = true;
			$message = 'An error has occurred trying to get - Error: {' . $e->getMessage() . '}';
		}

		return $this->sendResponse([
			'status' => $code,
			'error' => $error,
			'data' => $message
		]);
	}


	/**
	 * Enabled or disabled notification
	 *
	 * @Rest\Put("/email-notifications", name="api_email_notification_admin")
	 * @Security(name="Bearer")
	 * @SWG\Tag(name="EmailNotification")
	 * @SWG\Response(
	 *     response=200,
	 *     description="Ok response"
	 * )
	 * @SWG\Parameter(
	 * 		name="active",
	 *    description="Send true or 1 to Enabled / Send false or 0 to disabled",
	 *    type="string",
	 * 		required=false,
	 *    in="query"
	 *  )
	 *
	 * @param Request $request
	 *
	 * @return Response
	 */
	public function admin(Request $request)
	{
		try {
			if ($request->get('active') === 'true') {
				$action = 1;
			} elseif ($request->get('active') === 'false') {
				$action = 0;
			} else {
				$action = $request->get('active');
			}

			if (is_null($action)) {
				throw new \Exception('param. active is required');
			}

			$userNotificationRepository = $this->em->getRepository(UserNotification::class);
			$data = $userNotificationRepository->findOneBy(['user' => $this->getUser()]);

			if (!is_null($data)) {
				$data
					->setIsActive($action)
					->setUpdatedAt(new \DateTime());

				$this->em->persist($data);
			} else {
				$userNotification = new UserNotification();
				$userNotification
					->setUser($this->getUser())
					->setIsActive($action)
					->setCreatedAt(new \DateTime())
					->setUpdatedAt(new \DateTime());
				$this->em->persist($userNotification);
			}

			$this->em->flush();

			$code = Response::HTTP_OK;
			$error = false;
			$message = 'OK';
		} catch (\Exception $e) {
			$code = Response::HTTP_INTERNAL_SERVER_ERROR;
			$error = true;
			$message = 'An error has occurred trying to get - Error: {' . $e->getMessage() . '}';
		}

		return $this->sendResponse([
			'status' => $code,
			'error' => $error,
			'data' => $message
		]);
	}

	/**
	 * Add notification
	 *
	 * @Rest\Post("/email-notifications", name="api_email_notification_add")
	 * @Security(name="Bearer")
	 * @SWG\Tag(name="EmailNotification")
	 * @SWG\Response(
	 *     response=200,
	 *     description="Ok response"
	 * )
	 *
	 * @param Request $request
	 *
	 * @return Response
	 */
	public function add(Request $request, EmailNotificationService $emailNotificationService)
	{
		try {
			$data = json_decode($request->getContent(), true);
			$attributes = !empty($data['attributes']) ? json_encode($data['attributes'], true) : null;

			$output = $emailNotificationService->insertNotification(
				$data['title'],
				$data['message'],
				$data['type'],
				empty($data['userId']) ? $this->getUser()->getId() : $data['userId'],
				$attributes
			);

			if ($output['status'] != 200) {
				throw new \Exception($output['message']);
			}

			$code = Response::HTTP_OK;
			$error = false;
			$message = 'OK';
		} catch (\Exception $e) {
			$code = Response::HTTP_INTERNAL_SERVER_ERROR;
			$error = true;
			$message = 'An error has occurred trying to get - Error: {' . $e->getMessage() . '}';
		}

		return $this->sendResponse([
			'status' => $code,
			'error' => $error,
			'data' => $message
		]);
	}
}
