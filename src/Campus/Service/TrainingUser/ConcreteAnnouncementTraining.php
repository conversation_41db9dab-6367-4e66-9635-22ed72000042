<?php

namespace App\Campus\Service\TrainingUser;

use App\Campus\Service\User\TokenExtractorService;
use App\Entity\Announcement;
use App\Entity\AnnouncementUser;
use App\Entity\CourseSection;
use App\Entity\TypeCourse;
use App\Entity\User;
use App\Enum\AnnouncementState;
use App\Repository\AnnouncementRepository;
use App\Service\Annoucement\Admin\AnnouncementConfigurationsService;
use App\Service\Annoucement\Admin\AnnouncementUserService;
use App\Service\Api\ApiCourseService;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Core\Security;

class ConcreteAnnouncementTraining extends AbstractTrainingStrategy
{
    private AnnouncementRepository $announcementRepository;
    private AnnouncementConfigurationsService $announcementConfigurationsService;
    private AnnouncementUserService $announcementUserService;
    private TokenExtractorService $tokenExtractorService;



    public function __construct(EntityManagerInterface $em, SettingsService $settings, ApiCourseService $apiCourseService, Security $security, AnnouncementRepository $announcementRepository, AnnouncementConfigurationsService $announcementConfigurationsService, AnnouncementUserService $announcementUserService, TokenExtractorService $tokenExtractorService)
    {
        parent::__construct($em, $settings, $apiCourseService, $security);
        $this->announcementRepository = $announcementRepository;
        $this->announcementConfigurationsService = $announcementConfigurationsService;
        $this->announcementUserService = $announcementUserService;
        $this->tokenExtractorService = $tokenExtractorService;
    }


    /**
     * Obtain the training by announcement
     *
     * @return array
     */
    public function getTraining(CourseSection $courseSection): array
    {
        $payloadToken = $this->tokenExtractorService->getPayloadToken();

        if (!$payloadToken) {
            return $this->obtainAnnouncementByTypeCourse();
        }

        return $this->obtainAnnouncementUserByPayload($payloadToken);
    }


    /**
     * Obtain announcements by type course.
     *
     * @return array
     */
    private function obtainAnnouncementByTypeCourse(): array
    {
        $typesCourses = $this->em->getRepository(TypeCourse::class)->findBy(['active' => true]);

        $announcements = [];
        foreach ($typesCourses as $typeCourse) {
            $courses = $this->getCoursesByType($typeCourse->getId());
            if (!empty($courses['courses'])) {
                $announcements[] = $courses;
            }
        }

        return $announcements;
    }

    /**
     * Obtain announcement user by payload token.
     *
     * @param array $payloadToken
     * @return array
     */
    private function obtainAnnouncementUserByPayload(array $payloadToken): array
    {
        if (empty($payloadToken['announcement'])) {
            return $this->obtainAnnouncementByTypeCourse();
        }

        $announcement = $this->em->getRepository(Announcement::class)->find($payloadToken['announcement']);
        if (!$announcement) {
            return $this->obtainAnnouncementByTypeCourse();
        }

        $course = $announcement->getCourse();
        $courseToSend = $this->apiCourseService->courseToSendAnnouncement($this->getUser(), $announcement);
        $typeCourse = $course->getTypeCourse();
        $translated = $typeCourse->translate($this->getUser()->getLocaleCampus(), false);

        return [[
            "id" => $typeCourse->getId(),
            "name" => $translated->getName() ?? $typeCourse->getName(),
            "type" => "ANNOUNCEMENT",
            "description" => $translated->getDescription() ?? $typeCourse->getDescription(),
            "featured" => true,
            "courses" => [$courseToSend]
        ]];
    }

    private function getCoursesByType($type)
    {
        $user = $this->getUser();
        $announcements = $this->announcementRepository->findAnnouncementByUser($this->getUser());
        $typeCourse = $this->em->getRepository(TypeCourse::class)->find($type);


        $translated = $typeCourse->translate($user->getLocaleCampus(), false) ?? null;
        $name = $translated ? $translated->getName() : null;

        return [
            "id" => $typeCourse->getId(),
            "name" => $name ?? $typeCourse->getName(),
            "type" => "ANNOUNCEMENT",
            "courses" => $this->processAnnouncementCourses($announcements, $user, $type) ? $this->processAnnouncementCourses($announcements, $user, $type) : []

        ];
    }

    private function processAnnouncementCourses(array $announcements, User $user, $idTypeCourse)
    {

        $typeCourse = $this->em->getRepository(TypeCourse::class)->findOneBy(['id' => $idTypeCourse, 'active' => true]);
        $groupedCourses = [];

        foreach ($announcements as $announcement) {
            $typeCourseId = $this->getTypeCourseIdFromAnnouncement($announcement);

            if (!$this->hasAccessContentAfterFinish($announcement, $user)) {
                continue;
            }

            if ($typeCourse && $typeCourse->getId() == $typeCourseId) {
                $announcementToSend = $this->apiCourseService->courseToSendAnnouncement($user, $announcement);
                $groupedCourses[] = $announcementToSend;
            }
        }

        return $groupedCourses;
    }

    private function hasAccessContentAfterFinish(Announcement $announcement, $user)
    {
        $hasAccessContentAfterFinish = $this->announcementConfigurationsService->hasAccessContentAfterFinish($announcement);
        $stateAnnouncement = $this->stateAnnouncement($announcement);
        $announcementUser = $this->em->getRepository(AnnouncementUser::class)->findOneBy(['announcement' => $announcement, 'user' => $user]);
        $hasSurvey = $announcementUser->isAproved() && !$this->announcementUserService->hasSurveyCompleted($announcementUser);
        return $hasAccessContentAfterFinish || $stateAnnouncement != AnnouncementState::STATE_FINISHED || $hasSurvey;
    }

    private function stateAnnouncement(Announcement $announcement)
    {
        $now = new \DateTime();
        $startAt = $announcement->getStartAt();
        $finishAt = $announcement->getFinishAt();

        if ($startAt > $now) {
            return AnnouncementState::STATE_NOT_STARTED;
        } elseif ($finishAt < $now) {
            return AnnouncementState::STATE_FINISHED;
        } else {
            return AnnouncementState::STATE_IN_PROGRESS;
        }
    }

    private function getTypeCourseIdFromAnnouncement($announcement)
    {
        $course = $announcement->getCourse();
        return $course ? $course->getTypeCourse()->getId() : null;
    }
}
