<?php

namespace App\Campus\Service\TrainingUser;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Core\Security;
use App\Campus\Service\User\TokenExtractorService;
use App\Entity\CourseSection;
use App\Service\Api\ApiCourseService;
use App\Service\SettingsService;

abstract class AbstractTrainingStrategy implements StrategyInterfaceTraining
{
    protected EntityManagerInterface $em;
    protected SettingsService $settings;
    protected ApiCourseService $apiCourseService;
    protected Security $security; 

    public function __construct(EntityManagerInterface $em, SettingsService $settings, ApiCourseService $apiCourseService, Security $security)
    {
        $this->em = $em;
        $this->settings = $settings;
        $this->apiCourseService = $apiCourseService;
        $this->security = $security;
    }

    public function getUser()
    {
        return $this->security->getUser();
    }

    abstract public function getTraining(CourseSection $courseSection): array;
}
