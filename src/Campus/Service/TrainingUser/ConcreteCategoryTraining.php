<?php

declare(strict_types=1);

namespace App\Campus\Service\TrainingUser;

use App\Entity\CourseCategory;
use App\Entity\CourseSection;
use App\Entity\User;
use App\Service\Api\ApiCourseService;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Core\Security;

class ConcreteCategoryTraining extends AbstractTrainingStrategy
{
    public function __construct(EntityManagerInterface $em, SettingsService $settings, ApiCourseService $apiCourseService, Security $security)
    {
        parent::__construct($em, $settings, $apiCourseService, $security);
    }

    public function getTraining(CourseSection $courseSection): array
    {
        $user = $this->getUser();

        $openCourses = [
            'categories' => [],
            'courses' => [],
        ];

        $categories = $this->getCategories($courseSection);

        foreach ($categories as $category) {
            $categoryData = $this->createCategoryData($category, $user);

            $this->processCoursesForCategory($user, $category, $categoryData, $courseSection);

            $this->addCategoryToOpenCourses($openCourses, $categoryData);
        }

        $this->convertCategoryCoursesToArray($openCourses);

        return $openCourses['categories'];
    }

    private function convertCategoryCoursesToArray(&$openCourses): void
    {
        foreach ($openCourses['categories'] as $key => $category) {
            $courses = array_values($category['courses']);
            $openCourses['categories'][$key]['courses'] = $courses;
        }
    }

    private function getCategories(?CourseSection $courseSection = null)
    {
        if ($courseSection && $courseSection->isIsManualSelection()) {
            $categories = $courseSection->getCategories();
        } else {
            $categories = $this->em->getRepository(CourseCategory::class)->findBy([], ['sort' => 'ASC']);
        }

        return $categories;
    }

    private function createCategoryData($category, $user): array
    {
        $slug = $this->getCategoryName($category, $user->getLocaleCampus());
        $slug = $category->getNormalize($slug);
        $categoryName = $this->getCategoryName($category, $user->getLocaleCampus());

        return [
            'id' => $category->getId(),
            'name' => $categoryName,
            'type' => 'CATEGORY',
            'courses' => [],
        ];
    }

    private function getCategoryName($category, $locale): string
    {
        return ($category->getTranslations()->get($locale)) ?
            $category->getTranslations()->get($locale)->getName() :
            $category->getName();
    }

    private function processCoursesForCategory(User $user, $category, &$categoryData, CourseSection $courseSection): void
    {
        $courses = $this->apiCourseService->getSectionCourses($user, $category, $courseSection);

        if (!empty($courses)) {
            foreach ($courses as $course) {
                $courseToSend = $this->apiCourseService->courseToSend($user, $course);
                $categoryData['courses'][$course->getId()] = $courseToSend;
            }
        }
    }

    private function addCategoryToOpenCourses(&$openCourses, $categoryData): void
    {
        if (!empty($categoryData['courses'])) {
            array_push($openCourses['categories'], $categoryData);
        }
    }
}
