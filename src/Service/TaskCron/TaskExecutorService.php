<?php

declare(strict_types=1);

namespace App\Service\TaskCron;

use App\Entity\Chapter;
use App\Entity\Course;
use App\Entity\Export;
use App\Entity\Filter;
use App\Entity\FilterCategory;
use App\Entity\Itinerary;
use App\Entity\Nps;
use App\Entity\NpsQuestion;
use App\Entity\Task;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;
use App\Repository\AnnouncementRepository;
use App\Service\Announcement\AnnouncementExtraService;
use App\Service\Itinerary\General\ItineraryCourseReports;
use App\Service\Nps\NpsExtraAnswerUserService;
use App\Service\SettingsService;
use App\Service\SlotManagerService;
use App\Service\Task\TaskService;
use App\Service\TemplatedEmail\TemplatedEmailService;
use App\Service\User\Authentication\StarTeam;
use App\StatsAndExcel\Enum\StatsExportEnum;
use App\StatsAndExcel\Enum\StatsFiltersEnum;
use App\StatsAndExcel\Services\StatsService;
use App\Utils\ExportExtraFieldsHelper;
use Doctrine\ORM\EntityManagerInterface;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Exception;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpKernel\KernelInterface;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\Translation\TranslatorInterface;

class TaskExecutorService
{
    private $em;
    private $starTeam;
    private $logger;
    private $settings;
    private $appKernel;
    private $translator;
    private TaskService $taskService;
    private SlotManagerService $slotManagerService;

    private $statsService;
    private $announcementRepository;
    private $announcementExtraService;
    private ItineraryCourseReports $itineraryCourseReports;
    private NpsExtraAnswerUserService $NpsExtraAnswerUserService;
    protected Security $security;
    private TemplatedEmailService $templatedEmailService;

    public function __construct(
        EntityManagerInterface $em,
        StarTeam $starTeam,
        LoggerInterface $logger,
        SettingsService $settings,
        KernelInterface $appKernel,
        TranslatorInterface $translator,
        StatsService $statsService,
        AnnouncementRepository $announcementRepository,
        AnnouncementExtraService $announcementExtraService,
        ItineraryCourseReports $itineraryCourseReports,
        NpsExtraAnswerUserService $NpsExtraAnswerUserService,
        Security $security,
        TemplatedEmailService $templatedEmailService,
        TaskService $taskService,
        SlotManagerService $slotManagerService
    ) {
        $this->em = $em;
        $this->starTeam = $starTeam;
        $this->logger = $logger;
        $this->settings = $settings;
        $this->appKernel = $appKernel;
        $this->translator = $translator;

        $this->statsService = $statsService;
        $this->announcementRepository = $announcementRepository;
        $this->announcementExtraService = $announcementExtraService;
        $this->itineraryCourseReports = $itineraryCourseReports;
        $this->NpsExtraAnswerUserService = $NpsExtraAnswerUserService;
        $this->security = $security;
        $this->templatedEmailService = $templatedEmailService;
        $this->taskService = $taskService;
        $this->slotManagerService = $slotManagerService;
    }

    /**
     * @throws \Throwable
     */
    public function execute(Task $task): void
    {
        try {
            $task->setStartedAt(new \DateTimeImmutable());
            $task->setStatus(Task::TASK_INPROGRESS);
            $this->em->flush();

            // Get the method to execute from the task property
            $this->{$task->getTaskFunction()}($task);
            $task->setFinishedAt(new \DateTimeImmutable());
            $task->setStatus(Task::TASK_SUCCESS);
        } catch (\Throwable $e) {
            $task->setStatus(Task::TASK_FAILURE);
            $this->em->flush();

            $this->templatedEmailService->sendErrorNotification(
                'Task',
                $e,
                $task->getId()
            );

            throw $e;
        } finally {
            $this->em->flush();
        }
    }

    private function getEffectiveTaskType(Task $task): ?string
    {
        $type = $task->getType();

        if (empty($type) && isset($task->getParams()['type'])) {
            $type = $task->getParams()['type'];

            $task->setType($type);
            $this->em->persist($task);
            $this->em->flush();
        }

        return $type;
    }

    /**
     * @throws Exception
     */
    private function exportFile(Task $task)
    {
        $taskType = $this->getEffectiveTaskType($task);

        if ('stats-export' === $taskType) {
            $this->statsData($task);
        }
        if ('course-stats-export' === $taskType) {
            $this->courseStatsData($task);
        }
        if ('user-stats-export' === $taskType) {
            $this->userStatsData($task);
        }
        if ('itinerary-user-export' === $taskType) {
            $this->userItineraryData($task);
        }
        if ('course-catalog-export' === $taskType) {
            $this->courseCatalogExport($task);
        }
        if ('nps-data-export' === $taskType) {
            $this->npsExcelData($task);
        }
        if ('user-announcement-export' === $taskType) {
            $this->createExcelAnnouncement($task);
        }
        if ('itinerary-all-stats' === $taskType) {
            $this->createExcelItinerariesAllStats($task);
        }
    }

    private function exportStatsFile(Task $task)
    {
        $taskType = $this->getEffectiveTaskType($task);

        if ('announcement-participants-export' === $taskType) {
            $this->createExcelAnnouncementParticipants($task);
        }
    }

    private function statsData(Task $task)
    {
        $exportRepository = $this->em->getRepository(Export::class);
        $exportFile = $exportRepository->findOneBy(['task_id' => $task->getId(), 'deletedAt' => null, 'finished_at' => null]);

        if ($exportFile) {
            $user = $this->em->getRepository(User::class)->find($exportFile->getCreatedBy());
            $languageDefault = $this->settings->get('app.defaultLanguage');
            $locale = (null != $user->getLocaleCampus()) ? $user->getLocaleCampus() : $languageDefault;
            $userCourseRepository = $this->em->getRepository(UserCourse::class);
            $userDataItems = $userCourseRepository->findExport($exportFile->getMeta(), $user);
            $npsRepository = $this->em->getRepository(Nps::class);
            $settingsExtraFields = json_decode($this->settings->getSetting('app.user.extra_fields'), true) ?? [];

            /* EXCEL EXPORT GENERATION */

            $filename = '{' . StatsExportEnum::FILENAME . '}.xlsx';

            $filterCategory = $this->em->getRepository(FilterCategory::class)->findAll();

            $extraFieldsHeaders = ExportExtraFieldsHelper::getExportHeaders($settingsExtraFields, $locale);

            $dataItems = $this->statsService->setupExportDataItems($userDataItems, $npsRepository, $filterCategory, $extraFieldsHeaders, $locale, $settingsExtraFields);

            $exportedFile = $this->statsService->generateExcelSheet($exportFile->getMeta(), $dataItems, $filename, null, StatsExportEnum::MODE_DEFAULT, $extraFieldsHeaders);

            $this->saveFile($exportRepository, $exportFile, $this->statsService->getSpreadsheet());
        }
    }

    private function courseStatsData(Task $task)
    {
        $exportRepository = $this->em->getRepository(Export::class);
        $exportFile = $exportRepository->findOneBy(['task_id' => $task->getId(), 'deletedAt' => null, 'finished_at' => null]);

        if (!$exportFile) {
            throw new \RuntimeException(\sprintf('No valid Export found for task %d', $task->getId()));
        }

        $userCourseRepository = $this->em->getRepository(UserCourse::class);
        $userCourseChapterRepository = $this->em->getRepository(UserCourseChapter::class);
        $user = $this->em->getRepository(User::class)->find($exportFile->getCreatedBy());
        $courseId = $exportFile->getMeta()['courseID'] ?? null;

        $courseStartedIntime = $exportFile->getMeta()['courseStartedIntime'] ?? '';
        $courseStartedIntime = ('on' == $courseStartedIntime) ? 1 : '';

        $courseFinishedIntime = $exportFile->getMeta()['courseFinishedIntime'] ?? '';
        $courseFinishedIntime = ('on' == $courseFinishedIntime) ? 1 : '';

        $userCourseData = $userCourseRepository->exportCourseInfo([
            'dateFrom' => $exportFile->getMeta()['startDate'] ?? '',
            'dateTo' => $exportFile->getMeta()['endDate'] ?? '',
            'courseStartedIntime' => $courseStartedIntime,
            'courseFinishedIntime' => $courseFinishedIntime,
            'courseID' => $courseId,
            'customFilters' => $exportFile->getMeta()['customFilters'] ?? '',
        ], $user);

        $filterCategory = $this->em->getRepository(FilterCategory::class)->findAll();
        $categories = [];

        foreach ($filterCategory as $filCat) {
            array_push(
                $categories,
                $filCat->getName(),
            );
        }

        $headerExcel1 = ['fistName', 'lastName', 'email', 'active', 'code', 'CourseLocale'];
        $headerExcel1_gender = ['fistName', 'lastName', 'email', 'active', 'code', 'CourseLocale', 'gender'];
        $headerExcel2 = ['points', 'startedAt', 'finishedAt', 'timeSpent', 'announcementCreator'];

        if (!$this->settings->get('app.export.code')) {
            $headerExcel1 = array_diff($headerExcel1, ['code']);
            $headerExcel1_gender = array_diff($headerExcel1_gender, ['code']);
        }

        if ($this->settings->get('app.export.finishedChapters')) {
            $headerExcel2[] = 'finishedChapters';
        }

        $headExcel = $this->settings->get('app.export.gender_excel')
            ? array_merge($headerExcel1_gender, $categories, $headerExcel2) :
            array_merge($headerExcel1, $categories, $headerExcel2);

        list($spreadsheet, $sheet) = $this->newSpreadsheet('Course Data - Export', $headExcel);

        $row = 1;
        foreach ($userCourseData as $data) {
            $userId = $data['id'];
            if (\is_null($userId)) {
                continue;
            }
            $userFind = $this->em->getRepository(User::class)->find($userId);

            foreach ($filterCategory as $filCat) {
                $filtersUser = $this->em->getRepository(Filter::class)->fetchFiltersUsers($userFind->getId(), $filCat->getId());
                $data['filtro_' . $filCat->getId()] = '' != $filtersUser ? $filtersUser['name'] : '';
            }

            $data['points_'] = $data['points'] ?? 0;
            $data['startedAt_'] = $data['startedAt'];
            $data['finishedAt_'] = $data['finishedAt'];
            $data['timeSpent_'] = gmdate('H:i:s', $data['timeSpent']);
            $data['announcementCreator_'] = $data['announcementCreator'];
            $data['active'] = $data['active'] ? 'Yes' : 'No';

            if (!$this->settings->get('app.export.gender_excel')) {
                unset($data['gender']);
            }
            if (!$this->settings->get('app.export.code')) {
                unset($data['code']);
            }

            if ($this->settings->get('app.export.finishedChapters')) {
                $courseToFinish = $this->em->find(Course::class, $data['courseId']);
                if (!\is_null($courseToFinish)) {
                    $data['finishedChapters'] = $userCourseChapterRepository->countByUserAndCourse($userFind, $courseToFinish, true);
                } else {
                    $data['finishedChapters'] = '0';
                }
            }

            unset($data['courseId']);
            unset($data['id']);
            unset($data['points']);
            unset($data['startedAt']);
            unset($data['finishedAt']);
            unset($data['timeSpent']);
            unset($data['announcementCreator']);

            $sheet->fromArray([$data], null, 'A' . ++$row);
        }

        foreach (range('A', 'T') as $columnID) {
            $sheet->getColumnDimension($columnID)->setAutoSize(true);
        }

        $conditions = [
            StatsFiltersEnum::START_DATE => $exportFile->getMeta()['startDate'] ?? '',
            StatsFiltersEnum::END_DATE => $exportFile->getMeta()['endDate'] ?? '',
            'courseStartedIntime' => $courseStartedIntime,
            'courseFinishedIntime' => $courseFinishedIntime,
        ];

        $customFilters = $exportFile->getMeta()['customFilters'] ?? [];

        foreach ($customFilters as $key => $value) {
            $conditions[$key] = $value;
        }

        $spreadsheet->createSheet();
        $spreadsheet->setActiveSheetIndex(1);
        $this->statsService->generateExcelConfigSheet($spreadsheet, 'Stats Config', $conditions);

        $this->saveFile($exportRepository, $exportFile, $spreadsheet);
    }

    /**
     * @throws Exception
     */
    private function userStatsData(Task $task)
    {
        $exportRepository = $this->em->getRepository(Export::class);
        $exportFile = $exportRepository->findOneBy(['task_id' => $task->getId(), 'deletedAt' => null, 'finished_at' => null]);

        if ($exportFile) {
            $user = $this->em->getRepository(User::class)->find($exportFile->getCreatedBy());
            $languageDefault = $this->settings->get('app.defaultLanguage');
            $locale = (null != $user->getLocaleCampus()) ? $user->getLocaleCampus() : $languageDefault;

            $userCourseChapterRepository = $this->em->getRepository(UserCourseChapter::class);
            $userRepository = $this->em->getRepository(User::class);

            $userDataItems = $userRepository->getStatsUser([
                'startDate' => $exportFile->getMeta()['startDate'] ?? '',
                'endDate' => $exportFile->getMeta()['endDate'] ?? '',
                'customFilters' => $exportFile->getMeta()['customFilters'] ?? '',
            ], $user);

            $filterCategory = $this->em->getRepository(FilterCategory::class)->findAll();
            $categories = [];

            foreach ($filterCategory as $filCat) {
                $categories[] = $filCat->getName();
            }

            $headers = ['User id', 'Nombre', 'Apellidos', 'Email', 'Active', 'code', 'Puntos'];
            $autoFilterStartColumn = 'H';
            $autoFilterEndColumn = null;

            if (!$this->settings->get('app.export.code')) {
                $headers = array_diff($headers, ['code']);
            }

            if ($this->settings->get('app.export.gender_excel')) {
                $autoFilterStartColumn = 'I';
                $headers[] = 'Género';
            }
            $headers = array_merge($headers, ['Meta', 'Tiempo en la plataforma'], $categories);

            if (\count($categories) > 0) {
                $alphabet = range('A', 'Z');
                $autoFilterEndColumn = $alphabet[\count($headers) - 1];
            }
            $headers = array_merge($headers, ['Cursos Finalizados', 'Cursos empezados']);

            $settingsExtraFields = json_decode($this->settings->getSetting('app.user.extra_fields'), true) ?? [];
            $extraFieldHeader = ExportExtraFieldsHelper::getExportHeaders($settingsExtraFields, $locale);
            $headers = array_merge($headers, array_values($extraFieldHeader));

            /**
             * @var Spreadsheet $spreadsheet
             * @var Worksheet   $sheet
             */
            list($spreadsheet, $sheet) = $this->newSpreadsheet('Stats Data - Export', $headers);

            if (!empty($autoFilterStartColumn) && !empty($autoFilterEndColumn)) {
                $sheet->setAutoFilter("{$autoFilterStartColumn}1:{$autoFilterEndColumn}1");
            }

            $lastColumn = $sheet->getHighestColumn(1);
            $lastColumnIndex = Coordinate::columnIndexFromString($lastColumn);

            $sheet->getStyle("A1:{$lastColumn}1")
                ->getFont()
                ->setBold(true);

            for ($col = 1; $col <= $lastColumnIndex; ++$col) {
                $columnLetter = Coordinate::stringFromColumnIndex($col);
                $sheet->getColumnDimension($columnLetter)->setAutoSize(true);
            }

            $row = 1;
            $userCourseRepository = $this->em->getRepository(UserCourse::class);

            foreach ($userDataItems as $userDataItem) {
                $userId = $userDataItem['id'];
                $userFind = $userRepository->find($userId);
                $finishedCourses = $userCourseRepository->countByUser($userFind, true);
                $startedCourses = $userCourseRepository->countByUser($userFind, true) + $userCourseRepository->countByUser($userFind, false);

                foreach ($filterCategory as $filCat) {
                    $filtersUser = $this->em->getRepository(Filter::class)->fetchFiltersUsers($userFind->getId(), $filCat->getId());
                    $userDataItem['filtro_' . $filCat->getId()] = '' != $filtersUser ? $filtersUser['name'] : '';
                }

                if (!$this->settings->get('app.export.code')) {
                    unset($userDataItem['code']);
                }
                $userDataItem['active'] = $userDataItem['active'] ? 'Yes' : 'No';
                $userDataItem['meta'] = json_encode($userDataItem['meta']);
                $userDataItem['total'] = $this->timeUserInPlatform($userDataItem['total']);
                $userDataItem['coursesFinished'] = (int) $finishedCourses;
                $userDataItem['coursesStarted'] = (int) $startedCourses;
                if (!$this->settings->get('app.export.gender_excel')) {
                    unset($userDataItem['gender']);
                }

                $userDataItemMeta = json_decode($userDataItem['meta'], true);

                try {
                    $userDataItem = array_merge(
                        $userDataItem,
                        ExportExtraFieldsHelper::getExportExtraFieldsValues(
                            $settingsExtraFields,
                            $userDataItemMeta['extraFields'] ?? [],
                            $extraFieldHeader,
                            $locale
                        )
                    );
                } catch (\UnexpectedValueException $e) {
                    // Silent pass. This is a non-critical error.
                    $this->logger->error($e->getMessage(), $userId);
                }

                $sheet->fromArray([$userDataItem], null, 'A' . ++$row);
            }

            $this->saveFile($exportRepository, $exportFile, $spreadsheet);
        }
    }

    private function npsExcelData(Task $task)
    {
        $exportRepository = $this->em->getRepository(Export::class);
        $exportFile = $exportRepository->findOneBy(['task_id' => $task->getId(), 'deletedAt' => null, 'finished_at' => null]);

        $spreadsheet = $this->excelReport();
        $this->saveFile($exportRepository, $exportFile, $spreadsheet);
    }

    private function userItineraryData(Task $task): void
    {
        $exportRepository = $this->em->getRepository(Export::class);
        $exportFile = $exportRepository->findOneBy(['task_id' => $task->getId(), 'deletedAt' => null, 'finished_at' => null]);
        $user = $this->em->getRepository(User::class)->find($exportFile->getCreatedBy());
        if (!$exportFile) {
            return;
        }

        $itineraryId = $exportFile->getMeta()['itinerary-id'];
        $itineraryRepository = $this->em->getRepository(Itinerary::class);
        $itinerary = $itineraryRepository->find($itineraryId);
        if (!$itinerary) {
            return;
        }
        $spreadsheet = $itineraryRepository->generateExcelFile(
            itinerary: $itinerary,
            createdAt: $exportFile->getCreatedAt(),
            manager: $user && $user->isManager() ? $user : null
        );
        $this->saveFile($exportRepository, $exportFile, $spreadsheet);
    }

    /**
     * @throws Exception
     */
    private function courseCatalogExport(Task $task)
    {
        $exportRepository = $this->em->getRepository(Export::class);
        $exportFile = $exportRepository->findOneBy(['task_id' => $task->getId(), 'deletedAt' => null, 'finished_at' => null]);
        if (!$exportFile) {
            return;
        }

        $spreadsheet = $this->createSpreadsheet();

        $courseData = $this->em->getRepository(Course::class)->getCoursesData();
        $spreadsheet->setActiveSheetIndex(0);
        $this->summaryPage($spreadsheet, $courseData);
        $spreadsheet->createSheet();
        $spreadsheet->setActiveSheetIndex(1);
        $this->coursesPage($spreadsheet, $courseData);
        $spreadsheet->createSheet();
        $spreadsheet->setActiveSheetIndex(2);
        $this->chaptersPage($spreadsheet);

        $spreadsheet->createSheet();
        $spreadsheet->setActiveSheetIndex(3);
        $this->statsService->generateExcelConfigSheet($spreadsheet, 'Course Catalog Export');
        $spreadsheet->setActiveSheetIndex(0);
        $spreadsheet->getActiveSheet()->setSheetState(Worksheet::SHEETSTATE_VERYHIDDEN);
        $spreadsheet->setActiveSheetIndex(1);

        $this->saveFile($exportRepository, $exportFile, $spreadsheet);
    }

    private function intToHMS($num): string
    {
        $seconds = $num;
        $hours = (int) ($seconds / 3600);
        $seconds -= ($hours * 3600);

        $minutes = (int) ($seconds / 60);
        $seconds -= ($minutes * 60);

        $hours = \sprintf('%.0f', $hours);
        $minutes = \sprintf('%.0f', $minutes);
        $seconds = \sprintf('%.0f', $seconds);

        return str_pad((string) $hours, 2, '0', STR_PAD_LEFT) . ':' .
            str_pad((string) $minutes, 2, '0', STR_PAD_LEFT) . ':' .
            str_pad((string) $seconds, 2, '0', STR_PAD_LEFT);
    }

    private function summaryPage($spreadsheet, $courseData)
    {
        $currentSheet = $spreadsheet->getActiveSheet();
        $currentSheet->setTitle('Resumen');

        $currentSheet->getStyle('A1:A5')->getFont()->setBold(true);
        $currentSheet->getColumnDimension('A')->setAutoSize(true);
        $currentSheet->getColumnDimension('B')->setAutoSize(true);

        $headers = [
            'Cantidad de Cursos Activos',
            'Cantidad de Cursos Inactivos',
            'Cantidad de Contenidos de los cursos Activos',
            'Valoracion Media',
            'Cantidad de Horas de los cursos Activos',
        ];
        $values = [0, 0, 0, 0, 0];
        $totalAvg = 0;
        $counter = 0;

        foreach ($courseData as $course) {
            if ('YES' === $course['active']) {
                ++$values[0];
                $values[2] += $course['no_chapters'];
                $values[4] += $course['time_spent'];
            } else {
                ++$values[1];
            }

            if ($course['avg_rating'] > 0) {
                $totalAvg += $course['avg_rating'];
                ++$counter;
            }
        }

        $values[3] = $counter > 0 ? round($totalAvg / $counter, 2) : 0;
        $values[4] = $this->intToHMS($values[4]);

        foreach ($values as $index => $value) {
            $currentSheet->fromArray([
                'header' => $headers[$index],
                'value' => $value,
            ], null, 'A' . ($index + 1));
        }
        $currentSheet->getStyle('B')->getAlignment()->setHorizontal('right');
    }

    private function coursesPage($spreadsheet, $courseData)
    {
        $currentSheet = $spreadsheet->getActiveSheet();
        $currentSheet->setTitle('Catalogo Cursos');

        $langs = $this->settings->get('app.languages') ?? ['es', 'en', 'pt'];
        $headers = array_merge(
            ['Id', 'Nombre', 'Codigo', 'Categoria', 'N de Temporadas', 'N de Capitulos', 'Creador', 'Fecha de Creación', 'Activo', 'Open', 'Open Campus', 'Nuevo'],
            $langs,
            ['Valoracion Media', 'Tiempo Medio', 'Empezados', 'Terminados']
        );

        $currentSheet->fromArray($headers, null, 'A1');
        $this->addTableStyle($currentSheet);
        $currentSheet->setAutoFilter('H1:J1');

        foreach ($courseData as $index => $course) {
            $langData = [];
            foreach ($langs as $locale) {
                $langData[$locale] = (false === strpos($course['langs'], $locale)) ? 'NO' : 'YES';
            }

            $data = [
                [
                    'id' => $course['id'],
                    'name' => $course['name'],
                    'code' => $course['code'],
                    'category' => $course['category'],
                    'no_seasons' => $course['no_seasons'],
                    'no_chapters' => $course['no_chapters'],
                    'created_by' => $course['created_by'],
                    'created_at' => $course['created_at'],
                    'active' => $course['active'],
                    'open' => $course['open'],
                    'open_campus' => $course['open_campus'],
                    'is_new' => $course['is_new'],
                ], [
                    'avg_rating' => $course['avg_rating'],
                    'avg_time' => $this->intToHMS($course['avg_time']),
                    'started' => $course['started'],
                    'finished' => $course['finished'],
                ],
            ];

            $currentSheet->fromArray(array_merge($data[0], $langData, $data[1]), null, 'A' . ($index + 2));
        }
    }

    private function chaptersPage($spreadsheet)
    {
        $chaptersData = $this->em->getRepository(Chapter::class)->getChaptersData();
        $currentSheet = $spreadsheet->getActiveSheet();
        $currentSheet->setTitle('Catalogo Capitulos');
        $currentSheet->fromArray([
            'Id',
            'Nombre Curso',
            'Temporada',
            'Nombre Contenido',
            'Descripción Contenido',
            'Tipo de Pildora',
            'Categoria',
            'Idioma',
            'Tiempo Medio',
        ], null, 'A1');

        $this->addTableStyle($currentSheet);

        foreach ($chaptersData as $index => $chapter) {
            $chapterDescription = !empty($chapter['description']) ? $chapter['description'] : '';
            $currentSheet->fromArray([
                'id' => $chapter['id'],
                'course_name' => $chapter['course_name'],
                'season' => $chapter['season'],
                'content_name' => $chapter['content_name'],
                'description' => htmlspecialchars_decode(
                    html_entity_decode($chapterDescription, ENT_QUOTES, 'utf-8'),
                    ENT_QUOTES
                ),
                'type' => $chapter['type'],
                'category' => $chapter['category'],
                'locale' => $chapter['locale'],
                'avg_time' => $this->intToHMS($chapter['avg_time']),
            ], null, 'A' . ($index + 2));
        }
    }

    private function addTableStyle($currentSheet)
    {
        $lastColumn = $currentSheet->getHighestColumn(1);
        $currentSheet->getStyle("A1:{$lastColumn}1")->getFont()->setBold(true);

        // Convert column letter to numeric index
        $lastColumnIndex = Coordinate::columnIndexFromString($lastColumn);

        // Iterate through numeric index and convert back to column letter
        for ($col = 1; $col <= $lastColumnIndex; ++$col) {
            $columnId = Coordinate::stringFromColumnIndex($col);
            $currentSheet->getColumnDimension($columnId)->setAutoSize(true);
        }
    }

    private function newSpreadsheet($title, $headers): array
    {
        $spreadsheet = $this->createSpreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle($title);
        $sheet->fromArray($headers);

        return [$spreadsheet, $sheet];
    }

    private function saveFile($exportRepository, $exportFile, $spreadsheet, $preCalculateFormulas = true)
    {
        $pathFilename = "{$this->getTempPath()}{$exportFile->getId()}-{$exportFile->getCreatedAt()->format('YmdHis')}.xlsx";
        try {
            $writer = new Xlsx($spreadsheet);
            $writer->setPreCalculateFormulas($preCalculateFormulas);
            $writer->save($pathFilename);
            var_dump("New File Generated: $pathFilename");
            $exportRepository->markAsFinished($exportFile->getId());
        } catch (\PhpOffice\PhpSpreadsheet\Writer\Exception $e) {
            var_dump("Error Generating File: $pathFilename");
        }
    }

    protected function getTempPath(): string
    {
        $path = $this->appKernel->getProjectDir() . DIRECTORY_SEPARATOR . 'xlsx' . DIRECTORY_SEPARATOR;
        if (!is_dir($path)) {
            mkdir($path, 0777, true);
        }

        return $path;
    }

    private function timeUserInPlatform($time)
    {
        $horas = floor($time / 3600);
        $minutes = floor(($time - ($horas * 3600)) / 60);
        $seconds = $time - ($horas * 3600) - ($minutes * 60);
        $minutes = \sprintf('%.0f', $minutes);
        $seconds = \sprintf('%.0f', $seconds);

        return $horas . ':' . str_pad($minutes, 2, '0', STR_PAD_LEFT) . ':' . str_pad($seconds, 2, '0', STR_PAD_LEFT);
    }

    public function createExcelItinerariesAllStats(Task $task)
    {
        $exportRepository = $this->em->getRepository(Export::class);
        $exportFile = $exportRepository->findOneBy(['task_id' => $task->getId(), 'deletedAt' => null, 'finished_at' => null]);

        $spreadsheet = $this->itineraryCourseReports->generateInfoGeneralItinerary($task->getParams()['created_by']);
        $writer = new Xlsx($spreadsheet);

        $filename = $this->translator->trans('itinerary.label_in_plural', [], 'messages', 'es');
        $dateFile = new \DateTimeImmutable();
        $filename = $filename . '_' . $dateFile->format('dmY');
        $fileName = "{$filename}.xlsx";
        $tempFile = tempnam(sys_get_temp_dir(), $fileName);
        $writer->save($tempFile);

        $this->saveFile($exportRepository, $exportFile, $spreadsheet);
    }

    public function createExcelAnnouncement(Task $task)
    {
        $exportRepository = $this->em->getRepository(Export::class);
        $exportFile = $exportRepository->findOneBy(['task_id' => $task->getId(), 'deletedAt' => null, 'finished_at' => null]);

        $content = $exportFile->getMeta();

        $headExcel1 = [
            $this->translator->trans('excel.userAnnouncement.sheet1.colum1', [], 'messages', 'es'),
            $this->translator->trans('excel.userAnnouncement.sheet1.colum2', [], 'messages', 'es'),
            $this->translator->trans('excel.userAnnouncement.sheet1.colum2b', [], 'messages', 'es'),
            $this->translator->trans('excel.userAnnouncement.sheet1.colum3', [], 'messages', 'es'),
            $this->translator->trans('excel.userAnnouncement.sheet1.colum3b', [], 'messages', 'es'),
        ];
        $headExcel2 = [
            $this->translator->trans('excel.userAnnouncement.sheet2.colum1', [], 'messages', 'es'),
            $this->translator->trans('excel.userAnnouncement.sheet2.colum2', [], 'messages', 'es'),
            $this->translator->trans('excel.userAnnouncement.sheet2.colum3', [], 'messages', 'es'),
            $this->translator->trans('excel.userAnnouncement.sheet2.colum4', [], 'messages', 'es'),
            $this->translator->trans('excel.userAnnouncement.sheet2.colum5', [], 'messages', 'es'),
            $this->translator->trans('excel.userAnnouncement.sheet2.colum6', [], 'messages', 'es'),
            $this->translator->trans('excel.userAnnouncement.sheet2.colum7', [], 'messages', 'es'),
            $this->translator->trans('excel.userAnnouncement.sheet2.colum8', [], 'messages', 'es'),
            $this->translator->trans('excel.userAnnouncement.sheet2.colum9', [], 'messages', 'es'),
            $this->translator->trans('excel.userAnnouncement.sheet2.colum10', [], 'messages', 'es'),
            $this->translator->trans('excel.userAnnouncement.sheet2.colum11', [], 'messages', 'es'),
        ];
        $headExcel3 = [
            $this->translator->trans('excel.userAnnouncement.sheet3.colum1', [], 'messages', 'es'),
            $this->translator->trans('excel.userAnnouncement.sheet3.colum2', [], 'messages', 'es'),
            $this->translator->trans('excel.userAnnouncement.sheet3.colum3', [], 'messages', 'es'),
            $this->translator->trans('excel.userAnnouncement.sheet3.colum4', [], 'messages', 'es'),
            $this->translator->trans('excel.userAnnouncement.sheet3.colum5', [], 'messages', 'es'),
            $this->translator->trans('excel.userAnnouncement.sheet3.colum6', [], 'messages', 'es'),
        ];

        // first sheet ======================
        $spreadsheet = $this->createSpreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        // $sheet->setTitle($this->translator->trans('excel.userAnnouncement.sheet1.title', [], 'messages', 'es'));
        $writer = new Xlsx($spreadsheet);
        $sheet->fromArray([$headExcel1]);

        foreach (range('A', 'C') as $columnID) {
            $sheet->getColumnDimension($columnID)->setAutoSize(true);
        }

        // add values first sheet
        $row = 1;
        $row1Sheet1 = \count($this->em->getRepository(Itinerary::class)->totalItineraryForExcel($content));
        $totalCourses = 0;
        $totalUsers = 0;
        $uniqueUsers = [];
        $totalUniqueUsers = 0;

        // add values seconds sheet and 2 last column from first sheet
        $data = $this->em->getRepository(Itinerary::class)->detailItineraryForExcelAnnouncement($content);
        foreach ($data as &$valor) {
            $itineraryObject = $this->em->getRepository(Itinerary::class)->find($valor['id']);
            $totalCourses += $valor['cursos_asignados'];

            $result = $this->em->getRepository(Itinerary::class)->getUsersPaginated(
                $itineraryObject,
                []
            );

            $resultFilter = $this->em->getRepository(Itinerary::class)->getUsersByItineraryFilters(
                $itineraryObject
            );
            if (\is_null($resultFilter)) {
                $resultFilter = [];
            }

            foreach ($result as $item) {
                if ($item['completed'] == $valor['cursos_asignados']) {
                    ++$valor['personas_completo_itineraro'];
                }

                if (($item['completed'] > 0 && $item['completed'] < $valor['cursos_asignados']) || $item['started'] > 0) {
                    ++$valor['personas_en_proceso'];
                }
            }

            // delete duplicated users
            $finalFilterArray = [];
            foreach ($resultFilter as $item) {
                $find = false;
                foreach ($result as $subItem) {
                    if ($item['id'] == $subItem['id']) {
                        $find = true;
                    }
                }
                if (false === $find) {
                    array_push($finalFilterArray, $item);
                }
            }
            // register unique users
            foreach ($resultFilter as $item) {
                $uniqueUsers[$item['id']] = $item;
            }
            foreach ($result as $item) {
                $uniqueUsers[$item['id']] = $item;
            }

            foreach ($finalFilterArray as $item) {
                if ($item['completed'] == $valor['cursos_asignados']) {
                    ++$valor['personas_completo_itineraro'];
                }

                if (($item['completed'] > 0 && $item['completed'] < $valor['cursos_asignados']) || $item['started'] > 0) {
                    ++$valor['personas_en_proceso'];
                }
            }
            $valor['personas_sin_empezar'] = (\count($result) + \count($finalFilterArray)) -
                ($valor['personas_completo_itineraro'] + $valor['personas_en_proceso']);

            $valor['personas_asignadas'] = (\count($result) + \count($finalFilterArray));
            $totalUsers += $valor['personas_asignadas'];

            $valor['total_tiempo'] = round(\floatval($this->em->getRepository(Itinerary::class)->getTimeSpentInCourses(
                $itineraryObject
            )['total_time']), 0);

            $personas_asignadas = 0 == $valor['personas_asignadas'] ? 1 : $valor['personas_asignadas'];
            $valor['tiempo_medio_persona'] = round(
                $valor['total_tiempo'] /
                $personas_asignadas
            );

            $valor['total_tiempo'] = $this->timeUserInTheCourse($valor['total_tiempo']);
            $valor['tiempo_medio_persona'] = $this->timeUserInTheCourse($valor['tiempo_medio_persona']);
        }

        $results = $this->em->getRepository(Itinerary::class)->getCoursesCountFromItineraries();

        $cursosTotales = $results['totales'];
        $cursosUnicos = $results['unicos'];

        $totalUniqueUsers = \count($uniqueUsers);

        // write first sheet
        $dataSheet1 = [
            $row1Sheet1,
            $totalUsers,
            $totalUniqueUsers,
            $cursosTotales,
            $cursosUnicos,
        ];
        $sheet->fromArray([$dataSheet1], null, 'A' . ++$row);

        // create and write second sheet
        $sheet = $spreadsheet->createSheet(2);
        $sheet = $spreadsheet->setActiveSheetIndex(1);
        // $sheet->setTitle($this->translator->trans('excel.userAnnouncement.sheet2.title', [], 'messages', 'es'));
        $sheet->fromArray([$headExcel2]);

        foreach (range('A', 'K') as $columnID) {
            $sheet->getColumnDimension($columnID)->setAutoSize(true);
        }

        $row = 1;
        foreach ($data as $item) {
            $sheet->fromArray([$item], null, 'A' . ++$row);
        }

        // add sheet  ======================
        $sheet = $spreadsheet->createSheet(3);
        $sheet = $spreadsheet->setActiveSheetIndex(2);
        // $sheet->setTitle($this->translator->trans('excel.userAnnouncement.sheet3.title', [], 'messages', 'es'));
        $sheet->fromArray([$headExcel3]);

        // Data sheet #3 =====================
        $dataSheet3 = $this->em->getRepository(Itinerary::class)->detailCoursesForExcelAnnouncement($content);
        foreach ($dataSheet3 as &$valor) {
            $itineraryObject = $this->em->getRepository(Itinerary::class)->find($valor['id']);

            $result = $this->em->getRepository(Itinerary::class)->getUsersPaginatedByCourse(
                itinerary: $itineraryObject,
                courseId: $valor['courseId']
            );

            $resultFilter = $this->em->getRepository(Itinerary::class)->getUsersByItineraryFiltersByCourse(
                itinerary: $itineraryObject,
                coursesId: $valor['courseId']
            );
            if (\is_null($resultFilter)) {
                $resultFilter = [];
            }

            // delete duplicated users
            $finalFilterArray = [];
            foreach ($resultFilter as $item) {
                $find = false;
                foreach ($result as $subItem) {
                    if ($item['id'] == $subItem['id']) {
                        $find = true;
                    }
                }
                if (false === $find) {
                    array_push($finalFilterArray, $item);
                }
            }

            foreach ($result as $item) {
                if (0 !== $item['completed']) {
                    ++$valor['personas_completo_itineraro'];
                }

                if (0 !== $item['started']) {
                    ++$valor['personas_en_proceso'];
                }
            }

            foreach ($finalFilterArray as $item) {
                if (0 !== $item['completed']) {
                    ++$valor['personas_completo_itineraro'];
                }

                if (0 !== $item['started']) {
                    ++$valor['personas_en_proceso'];
                }
            }
            $valor['personas_sin_empezar'] = abs((\count($result) + \count($finalFilterArray)) -
                ($valor['personas_completo_itineraro'] + $valor['personas_en_proceso']));
        }

        $row = 1;
        unset($dataSheet3['courseId']);
        foreach ($dataSheet3 as $item) {
            $sheet->fromArray(
                [
                    'id' => $item['id'],
                    'name' => $item['name'],
                    'curso' => $item['curso'],
                    'personas_completo_itineraro' => $item['personas_completo_itineraro'],
                    'personas_en_proceso' => $item['personas_en_proceso'],
                    'personas_sin_empezar' => $item['personas_sin_empezar'],
                ],
                null,
                'A' . ++$row
            );
        }
        foreach (range('A', 'K') as $columnID) {
            $sheet->getColumnDimension($columnID)->setAutoSize(true);
        }

        // write file
        $filename = '';
        $fileName = "{$filename}.xlsx";
        $tempFile = tempnam(sys_get_temp_dir(), $fileName);
        $writer->save($tempFile);

        $this->saveFile($exportRepository, $exportFile, $spreadsheet);
    }

    /**
     * @throws Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function createExcelAnnouncementParticipants(Task $task)
    {
        $export = $task->getExport();

        if (null !== $export) {
            $conditions = $export->getMeta();
        } else {
            $conditions = [];
        }

        $utcTimeZone = new \DateTimeZone('UTC');
        $startDate = isset($conditions['startDate']) ? \DateTime::createFromFormat('Y-m-d', $conditions['startDate'], $utcTimeZone) : null;
        $endDate = isset($conditions['endDate']) ? \DateTime::createFromFormat('Y-m-d', $conditions['endDate'], $utcTimeZone) : null;
        $status = isset($conditions['status']) ? $conditions['status'] : null;
        $lang = isset($conditions['lang']) ? (string) $conditions['lang'] : 'en';
        $extra = isset($conditions['extra']) ? $conditions['extra'] : null;

        $userId = $export?->getCreatedBy();
        $user = $userId ? $this->em->getRepository(User::class)->find($userId) : null;

        $exportRepository = $this->em->getRepository(Export::class);
        $exportFile = $exportRepository->findOneBy(['task_id' => $task->getId(), 'deletedAt' => null, 'finished_at' => null]);

        $spreadsheet = $this->createSpreadsheet();

        $headerStyle = [
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => [
                    'rgb' => 'ADD8E6',
                ],
            ],
            'font' => [
                'bold' => true,
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_LEFT,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
        ];

        $dataStyle = [
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_LEFT,
            ],
        ];
        $formacionesData = $this->announcementRepository->getAnnouncementParticipants(startDate: $startDate, endDate: $endDate, status: $status, lang: $lang, extra: $extra, user: $user);
        $participantesData = $this->announcementRepository->getAnnouncementParticipantsDetail(startDate: $startDate, endDate: $endDate, status: $status, lang: $lang, extra: $extra, user: $user);

        $formacionesSheet = $spreadsheet->getActiveSheet();
        $formacionesSheet->setTitle('Listado Formaciones');

        $headExcelFormaciones = [
            $this->translator->trans('report.excel.headers.announcement.cod_form', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.announcement.category', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.announcement.name', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.announcement.language', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.announcement.timezone', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.announcement.location', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.announcement.start_date', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.announcement.end_date', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.announcement.hours', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.announcement.course_type', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.announcement.session_type', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.announcement.ext_int', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.announcement.obligatory', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.announcement.tutor', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.announcement.tutor_email', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.announcement.local_cost', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.announcement.euro_cost', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.announcement.society', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.announcement.status', [], 'reports', $lang),
        ];

        $formulaHeaders = [
            $this->translator->trans('report.excel.headers.announcement.total_hours', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.announcement.men', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.announcement.women', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.announcement.gender_desc', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.announcement.total', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.announcement.pb', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.announcement.mi', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.announcement.di', [], 'reports', $lang),
        ];

        $allowedExtras = $this->announcementExtraService->getActiveExtraData($lang);

        $extraColumnCount = \is_array($allowedExtras) ? \count($allowedExtras) : 0;
        $lastBasicColumnIndex = Coordinate::columnIndexFromString('S') + $extraColumnCount;
        $lastColumnIndex = Coordinate::columnIndexFromString('AA') + $extraColumnCount;
        $lastColumnLetter = Coordinate::stringFromColumnIndex($lastColumnIndex);

        foreach ($allowedExtras as $extra) {
            $headExcelFormaciones[] = $extra['name'];
        }

        $headExcelFormaciones = array_merge($headExcelFormaciones, $formulaHeaders);

        $formacionesSheet->fromArray($headExcelFormaciones, null, 'A1');
        $formacionesSheet->setAutoFilter($formacionesSheet->calculateWorksheetDimension());

        $formacionesSheet->fromArray($formacionesData, null, 'A2');

        $formacionesSheet->getStyle("A1:{$lastColumnLetter}1")->applyFromArray($headerStyle);

        $formacionesSheet->getStyle("A2:{$lastColumnLetter}" . (\count($formacionesData) + 1))->applyFromArray($dataStyle);

        $formacionesSheet->getStyle('I2:I' . (\count($formacionesData) + 1))->getNumberFormat()->setFormatCode('0.00');

        for ($col = 1; $col <= $lastColumnIndex; ++$col) {
            $columnID = Coordinate::stringFromColumnIndex($col);
            $formacionesSheet->getColumnDimension($columnID)->setAutoSize(true);
        }

        $row = 2;
        $formulaColumns = [
            'T' => $lastBasicColumnIndex + 1,
            'U' => $lastBasicColumnIndex + 2,
            'V' => $lastBasicColumnIndex + 3,
            'W' => $lastBasicColumnIndex + 4,
            'X' => $lastBasicColumnIndex + 5,
            'Y' => $lastBasicColumnIndex + 6,
            'Z' => $lastBasicColumnIndex + 7,
            'AA' => $lastBasicColumnIndex + 8,
        ];

        $formacionesSheet->setCellValue(Coordinate::stringFromColumnIndex($formulaColumns['T']) . $row, "=X$row*I$row");
        $formacionesSheet->setCellValue(Coordinate::stringFromColumnIndex($formulaColumns['U']) . $row, "=SUMPRODUCT(('Participantes'!A:A=A$row)*('Participantes'!I:I=\"M\"))");
        $formacionesSheet->setCellValue(Coordinate::stringFromColumnIndex($formulaColumns['V']) . $row, "=SUMPRODUCT(('Participantes'!A:A=A$row)*('Participantes'!I:I=\"F\"))");
        $formacionesSheet->setCellValue(Coordinate::stringFromColumnIndex($formulaColumns['W']) . $row, "=SUMPRODUCT(('Participantes'!A:A=A$row)*('Participantes'!I:I<>\"F\")*('Participantes'!I:I<>\"M\"))");
        $formacionesSheet->setCellValue(Coordinate::stringFromColumnIndex($formulaColumns['X']) . $row, "=U$row + V$row + W$row");
        $formacionesSheet->setCellValue(Coordinate::stringFromColumnIndex($formulaColumns['Y']) . $row, "=SUMPRODUCT(('Participantes'!A:A=A$row)*('Participantes'!N:N=\"Personal Base\"))");
        $formacionesSheet->setCellValue(Coordinate::stringFromColumnIndex($formulaColumns['Z']) . $row, "=SUMPRODUCT(('Participantes'!A:A=A$row)*('Participantes'!N:N=\"Manager\"))");
        $formacionesSheet->setCellValue(Coordinate::stringFromColumnIndex($formulaColumns['AA']) . $row, "=SUMPRODUCT(('Participantes'!A:A=A$row)*('Participantes'!N:N=\"Senior Manager\"))");

        $participantesSheet = $spreadsheet->createSheet();
        $participantesSheet->setTitle('Participantes');

        $headExcelParticipantes = [
            $this->translator->trans('report.excel.headers.participants.course_code', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.participants.employee_code', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.participants.first_name', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.participants.last_name1', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.participants.last_name2', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.participants.email', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.participants.birth_date', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.participants.attendance', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.participants.gender', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.participants.workplace', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.participants.society', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.participants.department', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.participants.hours', [], 'reports', $lang),
            $this->translator->trans('report.excel.headers.participants.collective', [], 'reports', $lang),
        ];

        $participantesSheet->fromArray($headExcelParticipantes, null, 'A1');
        $participantesSheet->setAutoFilter($participantesSheet->calculateWorksheetDimension());

        $participantesSheet->fromArray($participantesData, null, 'A2');

        $participantesSheet->getStyle('A1:N1')->applyFromArray($headerStyle);

        $participantesSheet->getStyle('A2:N' . (\count($participantesData) + 1))->applyFromArray($dataStyle);

        $participantesSheet->getStyle('M2:M' . (\count($participantesData) + 1))->getNumberFormat()->setFormatCode('0.00');

        foreach (range('A', 'N') as $columnID) {
            $participantesSheet->getColumnDimension($columnID)->setAutoSize(true);
        }

        try {
            $this->saveFile($exportRepository, $exportFile, $spreadsheet, false);
        } catch (\Exception $e) {
            $this->logger->error('Error creating Excel file: ' . $e->getMessage());
        }
    }

    private function timeUserInTheCourse($time)
    {
        $horas = floor($time / 3600);
        $minutos = floor(($time - ($horas * 3600)) / 60);
        $segundos = $time - ($horas * 3600) - ($minutos * 60);

        return $horas . ':' . $minutos . ':' . $segundos;
    }

    private function excelReport(): Spreadsheet
    {
        $npsRepository = $this->em->getRepository(Nps::class);
        $results = $npsRepository->excelReport();
        $surveyData = $this->NpsExtraAnswerUserService->addExtraToItems($results);

        $header = ['NPS ID', 'NPS Date', 'User Id', 'User Name', 'Email', 'Course Id', 'Course Name',
            'Course Code', 'Announcement Id', 'NPS Type', 'Question', 'NPS Value'];

        $headerExtras = $this->headersExcelReport($surveyData);

        $headers = array_merge($header, $headerExtras);

        $spreadsheet = $this->createSpreadsheet();
        $activeSheet = $spreadsheet->getActiveSheet();
        $activeSheet->fromArray($headers, null, 'A1');
        $maxColumnLetter = $activeSheet->getHighestColumn(1);
        foreach (range('A', 'J') as $columnId) {
            $activeSheet->getColumnDimension($columnId)->setAutoSize(true);
        }

        $activeSheet->getStyle("A1:{$maxColumnLetter}1")
            ->getFont()
            ->setBold(true);
        $activeSheet->freezePane('A2');

        $row = 2;
        foreach ($surveyData as $result) {
            $dataArray = $this->getDataExcelReport($result, $headerExtras);
            $activeSheet->fromArray($dataArray, null, "A$row");
            ++$row;
        }

        return $spreadsheet;
    }

    private function headersExcelReport($results): array
    {
        $extraHeaders = [];
        foreach ($results as $result) {
            if (isset($result['extra'])) {
                foreach ($result['extra'] as $key => $extra) {
                    $nameColumn = 'Extra-question-' . ($key + 1);
                    $this->addColumHeader($nameColumn, $extraHeaders);
                    if (isset($extra['answers'])) {
                        if (\is_array($extra['answers'])) {
                            foreach (array_keys($extra['answers']) as $row) {
                                $nameColumnAnswers = $nameColumn . '-Answer-' . ($row + 1);
                                $this->addColumHeader($nameColumnAnswers, $extraHeaders);
                            }
                        } else {
                            $nameColumnAnswers = $nameColumn . '-Answer-1';
                            $this->addColumHeader($nameColumnAnswers, $extraHeaders);
                        }
                    }
                }
            }
        }

        if (\count($extraHeaders) > 0) {
            sort($extraHeaders);
        }

        return $extraHeaders;
    }

    private function addColumHeader($nameColumn, &$extraHeaders)
    {
        if (!\in_array($nameColumn, $extraHeaders)) {
            $extraHeaders[] = $nameColumn;
        }
    }

    private function getDataExcelReport($result, array $headersExtra): array
    {
        $dataArray = [
            'id' => $result['id'],
            'createdAt' => $result['createdAt'],
            'userId' => $result['userId'],
            'name' => $result['name'],
            'email' => $result['email'],
            'courseId' => $result['courseId'],
            'courseName' => $result['courseName'],
            'courseCode' => $result['courseCode'],
            'announcementId' => $result['announcementId'],
            'type' => $result['type'],
            'question' => $result['nameNPSQuestion'],
            'value' => (\strlen($result['value']) && '=' === $result['value'][0]) ? ' ' . $result['value'] : $result['value'],
        ];

        $dataArrayExtra = [];
        if (isset($result['extra'])) {
            $dataArrayExtra = $this->formatDataExtraForToExcel($headersExtra);
            foreach ($result['extra'] as $key => $extra) {
                $nameColumn = 'Extra-question-' . ($key + 1);
                $dataArrayExtra[$nameColumn] = $extra['question'];

                if (isset($extra['answers'])) {
                    if (\is_array($extra['answers'])) {
                        foreach ($extra['answers'] as $row => $answer) {
                            $nameColumnAnsWer = $nameColumn . '-Answer-' . ($row + 1);
                            $dataArrayExtra[$nameColumnAnsWer] = $answer['value'];
                        }
                    } else {
                        $nameColumnAnsWer = $nameColumn . '-Answer-1';
                        $dataArrayExtra[$nameColumnAnsWer] = $this->getValueNpsType($extra);
                    }
                }
            }
        }

        return array_merge($dataArray, $dataArrayExtra);
    }

    private function getValueNpsType(array $extra)
    {
        $valor = $extra['answers'];
        switch ($extra['type']) {
            case NpsQuestion::TYPE_SWITCH:
                $valor = $extra['answers'] ? 'Si' : 'No';
                break;
            case NpsQuestion::TYPE_NPS:
            case NpsQuestion::TYPE_TEXT:
                $valor = $extra['answers'] ?? '';
                break;
        }

        return $valor;
    }

    private function formatDataExtraForToExcel(array $headersExtra): array
    {
        $dataArray = [];
        foreach ($headersExtra as $header) {
            $dataArray[$header] = '';
        }

        return $dataArray;
    }

    private function createSpreadsheet(): Spreadsheet
    {
        return new Spreadsheet();
    }

    /**
     * @throws \Exception When StarTeam service fails
     */
    private function userMaintenance(Task $task): void
    {
        if (empty($task->getParams())) {
            $from = $to = $task->getCreatedAt()->format('Y-m-d');
        } else {
            $from = $task->getParams()['from'];
            $to = $task->getParams()['to'];
        }

        $this->starTeam->userMaintenance($from, $to);
    }

    /**
     * @throws \Throwable
     */
    public function getAvailableExecutionSlot(): ExecutionSlot
    {
        // Delegate the verification of available slots to SlotManagerService
        return $this->slotManagerService->getAvailableTaskExecutionSlot();
    }
}
