framework:
    secret: '%env(APP_SECRET)%'
    #csrf_protection: true
    #http_method_override: true

    translator: { fallbacks: ['%app.defaultLanguage%'] }

    # Enables session support. Note that the session will ONLY be started if you read or write from it.
    # Remove or comment this section to explicitly disable session support.
    session:
        handler_id: null
        cookie_secure: auto
        cookie_samesite: lax
        storage_factory_id: session.storage.factory.native
        gc_probability: null
    #esi: true
    #fragments: true
    php_errors:
        log: true

    ide: phpstorm
