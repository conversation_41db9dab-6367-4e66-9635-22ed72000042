parameters:
    app.version: '%env(EASYLEARNING_VERSION)%'
    app.saml: false
    app.multilingual: true
    app.defaultLanguage: 'es'
    app.setCourseLevel: false
    app.useSegment: false
    app.showDeactivatedCourses: false
    app.setCoursePoints: false
    app.courseDefaultPoints: 500
    app.courseInfoGeneral: false
    app.clientIdVimeo: '13cf8196ce2d72fd53193f50407e4b54fb2a2c77'
    app.clientSecretVimeo: 'EdJS8wDCJ4iixDX5ErIwnE6NMrYFe4qEe7tPIqwskeQWmdedceRWOsgcS4Mw9ZfpBIF5uG9lNKnLSBu8WigWp3EcVfoX+cXxlqv54qCbz5CjTYjXxopsPUA/taXAKpMf'
    app.accessTokenVimeo: '99e6bb127198b0b3982fb42369abe7e3'
    app.userIdVimeo: '99351134'
    app.projectIdVimeo: '15579633'
    app.projectIdResourceCourse: '15579391'
    app.projectIdTaskCourse: '15579531'
    app.projectIdVideoQuiz: '15579537'
    app.projectIdRoleplay: '17156756'
    app.froalaKey: "3AC6eF6D4D3D3A3C2C-22VKOG1FGULVKHXDXNDXc2a1Kd1SNdF3H3A8A5D4A3C3E3B2A15=="
    app.fromEmail: '<EMAIL>'
    app.fromName: 'Easylearning'
    app.fromCIF: '2342343Y'
    app.openCourse: true

    app.supportEmail:
        - '<EMAIL>'

    app.languages:
        - es
        - en
        - pt
        - fr
        - it
        - eu
        - ca
        - fi
        - de
        - pl
        - ro
        - uk

    app.languages.admin:
        - en
        - es

    app.adminDefaultLanguage: 'es'

    app.vimeoUploadSubdomain: 'fundae-easylearning.gestionetdev.com'
    app.vimeoUploadUrl: 'https://fundae-easylearning.gestionetdev.com'
    app.scormUploadSubdomain: 'https://fundae-easylearning.gestionetdev.com'
    app.subsidizer.active: true
    app.subsidizer.formativeActionTypes:
        INTERN: 0               # Interna
        EXTERN: 1               # Externa
        SESSION_CONGRESS: 2     # Jornadas, congresos
    app.subsidizer.format:
        ON_SITE: 0              # Presencial
        ON_SITE_VIDEO: 1        # Vídeo presencial – Herramienta y enlace
        ONLINE: 2               # Online
        MIXED: 3                # Mixta
        FROM_DISTANCE: 4        # A distancia
    app.subsidizer.entities:
        HOBETUZ: 0              # Hobetuz
        FUNDAE: 1               # Tripartita/Fundae
        OTHER_FINANCES: 2       # Otros financiadores
    app.user.useFilters: true
    app.user.useExtraFields: false
    app.user.editCode: true
    #user extra fields
    app.user.extrafields:
        gender:
            type: choice
            options:
                choices:
                    Female: F
                    Male: M
                row_attr:
                    class: "col-md-12"
        category:
            type: null
            options:
                attr:
                    data-ea-widget: ea-autocomplete
                row_attr:
                    class: "col-md-12"
        department:
            type: null
            options:
                attr:
                    data-ea-widget: ea-autocomplete
                row_attr:
                    class: "col-md-12"
        center:
            type: null
            options:
                attr:
                    data-ea-widget: ea-autocomplete
                row_attr:
                    class: "col-md-12"
        country:
            type: choice
            options:
                choices:
                    Brasil: BRA
                    Cabo Verde: CAB
                    Cuba: CUB
                    España: ESP
                    Estados Unidos: USA
                    Hungría: HUN
                    Jamaica: JAM
                    Marruecos: MAR
                    Mexico: MEX
                    Montenegro: MTN
                    Perú: PER
                    Portugal: POR
                    República Dominicana: DOM
                    Tunez: TUN
                attr:
                    data-widget: select2
                row_attr:
                    class: "col-md-12"
        division:
            type: choice
            options:
                choices:
                    EMEA: EMEA
                    AMES: AMES
                attr:
                    data-widget: select2
                row_attr:
                    class: "col-md-12"
#        birthdate:
#            type: date
#            options:
#                row_attr:
#                    class: "col-md-12"

    app.filter.category: true
    app.filter.departament: false
    app.filter.center: true
    app.filter.country: false
    app.filter.gender: false
    app.filter.division: false
    app.filter.divisions:
        EMEA: [CAB, ESP, HUN, MAR, MTN, POR, TUN]
        AMES: [BRA, CUB, USA, JAM, MEX, PER, DOM]
    app.opinions.platform: true
    app.free.user.registration: false
    app.free.user.registration-autovalidate: false
    app.email.administrator.reception: '<EMAIL>'

    app.help.course.video: false
    app.help.course.pdf: false
    app.help.announcement.video: false
    app.help.announcement.pdf: false
    app.help.nps.video: false
    app.help.nps.pdf: false
    app.help.npsquestion.video: false
    app.help.npsquestion.pdf: false
    app.help.user.video: false
    app.help.user.pdf: false
    app.help.course_category.video: false
    app.help.course_category.pdf: false
    app.help.course_level.video: false
    app.help.course_level.pdf: false

    app.emailing.batchSize: 100
    app.emailing.paginationItemsPerPage: 5

    app.user.filterfields:
        filter:
            type: null
            options:
                attr:
                    data-ea-widget: ea-autocomplete

    app.professional_categories.only_first_level: true

    # app.stats moved to stats.yaml

    app.use_itinerary: false
    app.roulette.time.letter: 10
    app.trueorfalse.time: 10
    app.adivinarmenormayor.time: 10
    app.categorized.time: 10
    app.course.new_tag.max_time: 1 # Number in months

    app.documentation.enabled: false
    app.news.enabled: true
    app.forum.enabled: false
    app.challenge.enabled: false
    app.sharefile.enabled: false
    app.course_section.enabled: true
    app.user_company.enabled: false

    app.course.survey: true
    app.course.tab.person: true
    app.course.tab.stats: true
    app.course.tab.opinions: true
    app.date.before.fundae: '2023-08-04'
    app.zip.day_available_until: 5

    app.url_server: "%env(LTI_URL_SERVER)%" #used for lti conf.