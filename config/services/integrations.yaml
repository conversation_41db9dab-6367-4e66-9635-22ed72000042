parameters:
  FALSE_VALUE: false
  integrations.registered_clients:
    ExampleClient:
      enabled: false           # [Required] true|false If true, the client will be executed in the command: IntegrationsCommand
      name: 'ExampleClient'   # [Optional] Unique name of the client implementation: Optional, if not set, the top key will be used as the name to find the client
      mapping_group: 1        # [Optional] Group of mappings the client will be working on. This is optional, the value can be read from other sources
      class: App\Security\Integrations\Clients\BaseClient   # [Optional] Fully qualified namespace and class, for future use
      params:                 # [Optional] Pass params to client
        param1: Param1Value
      filter_tags:            # Use tags when naming conflicts exists
        - Tag1
        - Tag2
    ChefBurger:
      enabled: "%env(bool:default:FALSE_VALUE:CHEF_BURGER_ENABLED)%"
      name: "ChefBurger"
      mapping_group: 1
      class: App\Security\Integrations\Clients\ChefBurgerClient
    LdapClient:
      enabled: false
      name: 'LdapClient'
    VicioSchool:
      enabled: "%env(bool:default:FALSE_VALUE:VICIO_SCHOOL_ENABLED)%"
      name: 'VicioSchool'
      mapping_group: 1
      params:
        API_KEY: "%env(API_PASS)%"
      filter_tags:
        - contracts
        - locations

  ## Cron specific configurations
  integrations.execute: "%env(bool:default:FALSE_VALUE:INTEGRATIONS_COMMAND_ENABLED)%"

  # Allowed entities in mapping
  integrations.entity.allowed:
    User: App\Entity\User
    UserExtra: App\Entity\UserExtra
    UserFieldsFundae: App\Entity\UserFieldsFundae

  # Allowed fields per entity
  integrations.entity.fields:
    User:
      - "email:string"
      - "firstName:string"
      - "lastName:string"
      - "code:string"
      - "avatarFile:File" # Check if is url or avatar content
      - "locale:string"
      - "registerKey:string"
      - "timezone:string"
    UserExtra:
      - "gender:string"
      - "birthdate:date"
    UserFieldsFundae:
      - "socialSecurityNumber:string"
      - "gender:string"
      - "emailWork:string"
      - "birthdate:date"  #YYYY-MM-DD
      - "incapacity:bool"
      - "victimOfTerrorism:bool"
      - "genderViolence:bool"
      - "dni:string"
      - "contributionAccount:string"
  integrations.entity.identifiers:
    User:
      - email
      - code
