services:
    App\Service\SlotManagerService:
        arguments:
            $em: '@doctrine.orm.entity_manager'
            $settings: '@App\Service\SettingsService'
            $logger: '@logger'

    App\Repository\TaskRepository:
        arguments:
            $registry: '@doctrine'
            $em: '@doctrine.orm.entity_manager'
            $slotManagerService: '@App\Service\SlotManagerService'
            $logger: '@logger'

    App\Repository\ZipFileTaskRepository:
        arguments:
            $registry: '@doctrine'
            $params: '@parameter_bag'
            $slotManagerService: '@App\Service\SlotManagerService'
            $em: '@doctrine.orm.entity_manager'
            $logger: '@logger'
