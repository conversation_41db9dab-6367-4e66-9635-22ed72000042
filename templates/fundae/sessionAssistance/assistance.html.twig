<style>
	* {
		font-size: 14px;
	}
	.text-center {
		text-align: center;
	}
	table.info,
	table.users {
		width: 100%;
		border-collapse: collapse;
		border: 2px solid #bfbfbf;
	}
	.td-top-border {
		border-top: 1px solid #212121;
	}
	.td-right-border {
		border-right: 1px solid #212121;
	}
	.td-bottom-border {
		border-bottom: 1px solid #212121;
	}
	.td-left-border {
		border-left: 1px solid #212121;
	}
	.td-border {
		border: 1px solid #212121;
	}

	.user-data td {
		line-height: 3;
	}
	table.info {
		line-height: 2;
		padding-left: 10px;
		padding-right: 10px;
		margin-top: 30px;
	}
	h2 {
		text-align: center;
		width: 100%;
		font-size: 18px !important;
	}
</style>
<h2>{{ 'fundae_assistance_template.action_type' | trans([], 'messages') }}</h2>
<table class="info">
	<tr>
		<td colspan="4">
			<strong>{{ 'fundae_assistance_template.action_type' | trans([], 'messages') }}:</strong>
			{{ announcement.actionType }}</td>
	</tr>
	<tr>
		<td colspan="2">
			<strong>{{ 'fundae_assistance_template.action_code' | trans([], 'messages') }}:</strong>
			{{ announcement.actionCode }}</td>
		<td colspan="2">
			<strong>{{ 'fundae_assistance_template.group' | trans([], 'messages') }}:</strong>
			{{ group.code }}</td>
	</tr>
	<tr>
		<td colspan="2">
			<strong>{{ 'fundae_assistance_template.start_at' | trans([], 'messages') }}:</strong>
			{{ announcement.startAt | date('d/m/Y') }}</td>
		<td>
			<strong>{{ 'fundae_assistance_template.finish_at' | trans([], 'messages') }}:</strong>
			{{ announcement.finishAt | date('d/m/Y') }}</td>
		<td></td>
	</tr>
	<tr>
		<td colspan="4">
			<strong>{{ 'fundae_assistance_template.main_formation_teacher' | trans([], 'messages') }}:</strong>
			{{ tutor.fullName }}</td>
	</tr>
	<tr>
		<td>
			<strong>{{ 'fundae_assistance_template.session_number' | trans([], 'messages') }}:</strong>
			{{ session.sessionNumber }}</td>
		<td>
			<strong>{{ 'fundae_assistance_template.date' | trans([], 'messages') }}:</strong>
			{{ session.startAt | date('d/m/Y') }}</td>
		<td>
			<strong>{{ 'fundae_assistance_template.morning_afternoon' | trans([], 'messages') }}:</strong>
			{{ session.finishAt | date('d/m/Y') }}</td>
		<td></td>
	</tr>
	<tr>
		<td colspan="2">
			<strong>HORARIO: DE
			</strong>
			{{ session.startAt | date('H:i')}}
			<strong>A</strong>
			{{ session.finishAt | date('H:i') }}</td>
		<td></td>
		<td></td>
	</tr>
	<tr>
		<td colspan="4"></td>
	</tr>
	<tr>
		<td colspan="2">
			<strong>{{ 'fundae_assistance_template.signed' | trans([], 'messages') }}:</strong>
		</td>
		<td colspan="2"></td>
	</tr>
	<tr>
		<td colspan="2">
			<strong>({{ 'fundae_assistance_template.info_signed_person' | trans([], 'messages') }})</strong>
		</td>
		<td colspan="2"></td>
	</tr>
</table>

<table class="users" style="width: 100%; margin-top: 15px;">
	<tr>
		<td colspan="3" class="td-border text-center">
			<strong>{{ 'fundae_assistance_template.assistance_data' | trans([], 'messages') }}</strong>
		</td>
		<td rowspan="2" class="td-border text-center" style="width: 200px;">
			<strong>{{ 'fundae_assistance_template.signatures' | trans([], 'messages') }}</strong>
		</td>
		<td rowspan="2" class="td-border text-center">
			<strong>{{ 'fundae_assistance_template.observations' | trans([], 'messages') }}</strong>
		</td>
	</tr>
	<tr>
		<td class="td-border text-center" style="width: 200px;">
			<strong>{{ 'user.configureFields.last_name' | trans([], 'messages') }}</strong>
		</td>
		<td class="td-border text-center" style="width: 200px;">
			<strong>{{ 'user.configureFields.first_name' | trans([], 'messages') }}</strong>
		</td>
		<td class="td-border text-center" style="width: 120px;">
			<strong>{{ mainIdentification.name }}</strong>
		</td>
	</tr>
	{% for index, u in users %}
		<tr class="user-data">
			<td class="td-border">{{ index + 1 }}
				{{ u['lastName'] }}</td>
			<td class="td-border">{{ u['firstName'] }}</td>
			<td class="td-border">{{ u['dni'] }}</td>
			<td class="td-border"></td>
			<td class="td-border"></td>
		</tr>
	{% endfor %}

    {% set totalUser = (users|length / 2) | round %}

	{%  for i in 0..totalUser %}
		<tr class="user-data">
			<td class="td-border" height="3rem"></td>
			<td class="td-border" height="3rem"></td>
			<td class="td-border" height="3rem"></td>
			<td class="td-border" height="3rem"></td>
			<td class="td-border" height="3rem"></td>
		</tr>
	{% endfor %}
</table>
