<div class="content-panel p-3 course-contents">
  <h2>{{ 'user.configureFields.course_content'|trans({}, 'messages') }}</h2>
  <table class="table datagrid with-rounded-top" width="100%">
    <thead class="thead-light">
      <tr>
        <th>
          <span>{{ 'user.configureFields.chapter'|trans({}, 'messages') }}</span>
        </th>
        <th>
          <span>{{ 'user.configureFields.content_type'|trans({}, 'messages') }}</span>
        </th>
        <th>
          <span>{{ 'user.configureFields.started_at'|trans({}, 'messages') }}</span>
        </th>
        <th>
          <span>{{ 'user.configureFields.finished_at'|trans({}, 'messages') }}</span>
        </th>
        <th>
          <span>{{ 'user.configureFields.time_spent'|trans({}, 'messages') }}</span>
        </th>
        <th class="text-center">
          <span>{{ 'user.configureFields.finished'|trans({}, 'messages') }}</span>
        </th>
      </tr>
    </thead>

    <tbody>
      {% for chapter in announcementUser.announcement.course.chapters %}
        <tr>
          <td>{{ chapter.position }}.
            {{ chapter.title }}</td>
          <td>{{ chapter.type.name }}</td>
          <td>
            {% if userChapters[chapter.id] is defined %}
              {{ userChapters[chapter.id].startedAt|date('d-m-Y H:i') }}
            {% else %}
              -
            {% endif %}
          </td>
          <td>
            {% if userChapters[chapter.id] is defined and userChapters[chapter.id].finishedAt is not null %}
              {{ userChapters[chapter.id].finishedAt|date('d-m-Y H:i') }}
            {% else %}
              -
            {% endif %}
          </td>
          <td>
            {% if userChapters[chapter.id] is defined %}
              {{ userChapters[chapter.id].timeSpent|reportTimeFormat }}
            {% else %}
              -
            {% endif %}
          </td>
          <td class="text-center">
            {% if userChapters[chapter.id] is defined and userChapters[chapter.id].finishedAt is not null %}
              {{ 'Yes'|trans({}, 'messages') }}
            {% else %}
              {{ 'no'|trans({}, 'messages') }}
            {% endif %}
          </td>
        </tr>
      {% endfor %}
    </tbody>
  </table>
</div>
