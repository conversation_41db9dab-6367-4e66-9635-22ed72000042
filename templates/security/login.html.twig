{% extends 'base.html.twig' %}

{% block title %}
  Log in!
{% endblock %}

{% block stylesheets %}
  {{ parent() }}
  {{ encore_entry_link_tags('app') }}
{% endblock %}

{% block body %}
  <div id="login" class="container h-100">
    <div class="row h-100 justify-content-center align-items-center">
      <form method="post" class="col-12 col-md-6 p-4 loginForm">
        {% if error %}
          <div class="alert alert-danger">{{ error.messageKey|trans(error.messageData, 'security') }}</div>
        {% endif %}

        {% if app.user %}
          <div class="mb-3">
            You are logged in as {{ app.user.username }}, <a href="{{ path('app_logout') }}">Logout</a>
          </div>
        {% endif %}

        <div class="logo mb-3"></div>
{#        <img src="{{ asset('assets/login/logo.svg') }}" class="mb-3" />#}

        <h1 class="h3 mb-3 font-weight-normal">{{ 'security.login_title'|trans({}, 'messages') }}</h1>
        <div class="form-group">
          <label for="inputEmail">Email</label>
          <input type="email" value="{{ last_username }}" name="email" id="inputEmail" class="form-control" required autofocus />
        </div>
        <div class="form-group">
          <label for="inputPassword">{{ 'security.password'|trans({}, 'messages') }}</label>
          <input type="password" name="password" id="inputPassword" class="form-control" required />
        </div>
        <input type="hidden" name="_csrf_token" value="{{ csrf_token('authenticate') }}" />

        {#
                Uncomment this section and add a remember_me option below your firewall to activate remember me functionality.
                See https://symfony.com/doc/current/security/remember_me.html
                #}

        <input type="hidden" name="_remember_me" value="1" />

        <div class="groupButton">
          <button class="button buttonPrimary" type="submit">{{ 'security.login_button_login'|trans({}, 'messages') }}</button>
          {% if saml %}
            <a class="button buttonPrimary" href="{{ path('saml_access_back') }}">SSO</a>
          {% endif %}

          {% if idp['ldap'] %}
            <a class="button buttonPrimary" href="{{ path('app_login_ldap') }}">LDAP</a>
          {% endif %}
          {% if idp['saml2'] %}
            <a class="button buttonPrimary" href="{{ path('saml2_sso') }}">SAML2</a>
          {% endif %}
          {% if idp['oauth2'] %}
            <a class="button buttonPrimary" href="{{ path('oauth2-connect') }}">OAuth2</a>
          {% endif %}
          {#
            <a class="button buttonWhite btn-block"  href="{{path('app_register')}}">
              {{ 'security.login_button_create_account'|trans({}, 'messages') }}
            </a>

            <a class="button buttonWhite btn-block"  href="{{path('app_forgot_password_request')}}">
            {{ 'security.login_question_password'|trans({}, 'messages') }}
            </a> #}
        </div>
      </form>
    </div>
  </div>
{% endblock %}
