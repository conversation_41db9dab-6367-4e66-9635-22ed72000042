{% extends 'template_email/layout.html.twig' %}

{% block main %}
  <div class="containerEmail" style="border: 1px solid #d7727d; padding: 20px; border-radius: 5px; color: #721c24; font-family: 'Source Sans Pro', sans-serif;">
    <h3 style="display: flex; align-items: center; gap: 10px;">
      <span style="font-size: 24px;">&#9888;</span>
      <span style="margin-left: 0.5rem; display: flex; margin-top: 0.25rem;">{{ 'email.zombie.title'|trans({}, 'messages', locale) }}</span>
    </h3>

    <p>
      <strong>{{ 'email.zombie.environment'|trans({}, 'messages', locale) }}:</strong> <strong style="color: #343a40;">{{ appName }} ({{ environment }})</strong>
    </p>

    <p>
      <strong>{{ 'email.zombie.context'|trans({}, 'messages', locale) }}:</strong> <strong style="color: #343a40;">{{ context }}</strong>
    </p>

    {% if idTask is defined and idTask is not null %}
    <p>
        <strong>{{ 'email.zombie.task_id'|trans({}, 'messages', locale) }}:</strong> <strong style="color: #343a40;">{{ idTask }}</strong>
    </p>
    <p>
      {{ 'email.zombie.marked_as'|trans({}, 'messages', locale) }} <strong style="color: #721c24;">{{ 'email.zombie.zombie_status'|trans({}, 'messages', locale) }}</strong> (status = -2) {{ 'email.zombie.timeout_reason'|trans({}, 'messages', locale) }}.
    </p>
    {% endif %}
    
    <div style="background-color: #f1b0b7; padding: 10px; border-left: 5px solid #721c24; margin-top: 10px; border-radius: 5px;">
      <p>{{ 'email.zombie.check_details'|trans({}, 'messages', locale) }}</p>
    </div>

    {% if contextInfo is defined and contextInfo|length > 0 %}
    <div style="background-color: #f8f9fa; padding: 10px; border-left: 5px solid #6c757d; margin-top: 10px; border-radius: 5px;">
      <h4>{{ 'email.zombie.additional_info'|trans({}, 'messages', locale) }}:</h4>
      {% for key, value in contextInfo %}
        <p><strong>{{ key }}:</strong> {{ value }}</p>
      {% endfor %}
    </div>
    {% endif %}

    <p class="content" style="margin-top: 20px;">
      {{ 'email.zombie.regards'|trans({}, 'messages', locale) }},<br/>
      <em>{{ 'email.zombie.team'|trans({'%appName%': appName}, 'messages', locale) }}</em>
    </p>
  </div>
{% endblock main %}
