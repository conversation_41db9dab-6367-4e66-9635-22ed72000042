{% block head_stylesheets %}
	{{ encore_entry_link_tags('saveLtiChapter') }}
{% endblock %}

{% block main %}
	<div class="d-flex flex-row justify-content-between align-content-center w-100 pb-3">
		<div class="content-header-title">
			{{ 'lti_chapter.title'|trans({}, 'messages', app.user.locale) }}
		</div>
		{% if not lti %}
			<button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modal-chapter-24">
				{{ 'lti_chapter.add'|trans({}, 'messages', app.user.locale) }}
			</button>
		{% else %}
			<button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modal-chapter-24" style="margin-left: 1rem">
				{{ 'lti_chapter.edit'|trans({}, 'messages', app.user.locale) }}
			</button>
		{% endif %}
	</div>

	<!-- Modal -->
	<div class="modal fade" id="modal-chapter-24" tabindex="-1" aria-labelledby="modal-chapter-9Label" aria-hidden="true" data-bs-backdrop="static">
		<div class="modal-dialog modal-dialog-centered" role="document">
			<div class="modal-content">
				<div class="modal-header">
					{% if lti %}
						<h5 class="modal-title" id="modal-chapter-24Label">{{ 'lti_chapter.edit'|trans({}, 'messages', app.user.locale) }}</h5>
					{% else %}
						<h5 class="modal-title" id="modal-chapter-24Label">{{ 'lti_chapter.add'|trans({}, 'messages', app.user.locale) }}</h5>
					{% endif %}
					<button type="button" class="close" data-bs-dismiss="modal" aria-label="Close" id="close-modal-chapter-24">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body" id="save-lti-chapter">
					<form id="form-save-lti-chapter">
						<input type="hidden" name="referrer" id="referrerInput" value="{{ app.request.uri }}"/>
						{% if lti %}
							<input type="hidden" name="id" value="{{ lti.id }}">
						{% endif %}
						{% if chapter %}
							<input type="hidden" name="chapterId" value="{{ chapter.id }}">
						{% endif %}
						<div class="form-group">
							<label>{{ 'lti_chapter.identifier'|trans({}, 'messages', app.user.locale) }}</label>
							{% if lti %}
								<input type="text" class="form-control" name="identifier" value="{{ lti.identifier }}">
							{% else %}
								<input type="text" class="form-control" name="identifier">
							{% endif %}
						</div>
						<div class="form-group">
							<label>{{ 'lti_chapter.idTool'|trans({}, 'messages', app.user.locale) }}</label>
							{% if lti %}
								<select name="idTool" class="form-control">
									{% for tool in ltiTools %}
										<option value="{{ tool.id }}" {{ tool.id == lti.ltiTool.id ? 'selected' : '' }}>{{ tool.toolsName }}</option>
									{% endfor %}
								</select>
							{% else %}
								<select name="idTool" class="form-control">
									{% for tool in ltiTools %}
										<option value="{{ tool.id }}">{{ tool.toolsName }}</option>
									{% endfor %}
								</select>
							{% endif %}
						</div>
						<button type="button" @click="saveLtiChapter()" class="btn btn-primary">
							{{ 'common_areas.save'|trans({}, 'messages', app.user.locale) }}
						</button>
					</form>
				</div>
			</div>
		</div>
	{% endblock main %}

	{% block body_javascript %}
		{{ encore_entry_script_tags('saveLtiChapter') }}
	{% endblock %}

</div>
