{% block head_stylesheets %}
    {{ encore_entry_link_tags('uploadVideo') }}
    {{ encore_entry_link_tags('iframeVimeo') }}
{% endblock %}

{% block main %}
<div class="d-flex flex-row justify-content-between align-content-center w-100 pb-3">
    <div class="content-header-title">
<!--        <h1 class="title">
            {{ 'component_video.package_video'|trans({}, 'messages', app.user.locale) }}
        </h1>-->
    </div>
    {% if not video %}

    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modal-chapter-9">
        {{ 'component_video.add_package_video'|trans({}, 'messages', app.user.locale) }}
    </button>

  {%  else %}
    {% if video.origen == 'vimeo' %}
    <div class="containerVideo-vimeo">
        {{ include("admin/video/show_video_vimeo.html.twig", {type:  video.name })}}
    </div>
    {% else %}
    <div class="containerVideo-you">
        {{ include("admin/video/show_video_youtube.html.twig") }}
    </div>
    {% endif %}
      <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modal-chapter-{{ chapter.type.id }}" style="margin-left: 1rem">
         {{ 'component_video.edit_package_video'|trans({}, 'messages', app.user.locale) }}
    </button>
    {% endif %}

</div>
{# {% if video %}
{% if video.origen == 'vimeo' %}
 <div class="containerVideo-vimeo">
      {{ include("admin/video/show_video_vimeo.html.twig", {type:  video.name })}}
</div>
{% else %}
<div class="containerVideo-you">
    {{ include("admin/video/show_video_youtube.html.twig") }}
<div>
{% endif %}

{% else %}
    No
{% endif %} #}

<!-- Modal -->
<div class="modal fade" id="modal-chapter-{{ chapter.type.id }}" tabindex="-1" aria-labelledby="modal-chapter-9Label" aria-hidden="true" data-bs-backdrop="static">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header" >
      {% if video %}
        <h5 class="modal-title" id="modal-chapter-9Label">{{ 'component_video.edit_package_video'|trans({}, 'messages', app.user.locale) }}</h5>
      {% else %}
        <h5 class="modal-title" id="modal-chapter-9Label" >{{ 'component_video.add_package_video'|trans({}, 'messages', app.user.locale) }}</h5>
      {% endif %}
        <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close" id="close-modal-chapter-9">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
        <div class="modal-body">
            {% if video %}
                <div id="upload-video">
                    <upload-video :chapter="{{chapter.id}}" :action="'edit'" :idvideo="{{video.id}}"
                                  :vimeouploadsubdomain="'{{ upload_subdomain }}'"
                                  :locale="'{{ chapter.course.locale }}'"></upload-video>
                </div>
            {% else %}
                <div id="upload-video">
                    <upload-video :chapter="{{chapter.id}}" :action="'new'" :idvideo="1"
                                  :vimeouploadsubdomain="'{{ upload_subdomain }}'"
                                  :locale="'{{ chapter.course.locale }}'"></upload-video>
                </div>
            {% endif %}
        </div>
  </div>
</div>
{% endblock main %}

{% block body_javascript %}
    {{ encore_entry_script_tags('uploadVideo') }}
    {{ encore_entry_script_tags('iframeVimeo') }}
{% endblock %}

