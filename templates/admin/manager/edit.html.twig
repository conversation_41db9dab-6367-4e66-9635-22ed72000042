{% extends '@!EasyAdmin/crud/edit.html.twig' %}

{% block page_title %}
{{ 'user.manage.assign_data'|trans({}, 'messages',  app.user.locale) }}
{% endblock page_title %}

{% block head_stylesheets %}
	{{ parent() }}
	{{ encore_entry_link_tags('managerEdit') }}
{% endblock %}

{% block content_title %}
{{ 'user.manage.assign_data'|trans({}, 'messages',  app.user.locale) }}   ->  {{user.firstName}} {{user.lastName}}
{% endblock %}

{% block page_actions %}
	{% if user_use_filters %}
	  <a href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\ManagerCrudController').setAction('index') }}" class="btn btn-secondary">
  		 {{ 'common_areas.back_list'|trans({}, 'messages',  app.user.locale) }}
    </a>
	{% else %}
	{{ parent() }}
	{% endif %}
{% endblock page_actions %}

{% block content %}
	<div id="manager-edit">{{ parent() }} </div>
{% endblock content %}

{% block main %}


	{% if user_use_filters %}
		<div class="">
			<category-filter :save-in-realtime="true"
							 :use-rest-methods="true"
							 :allow-add-remove-all="true"
							 url-add-filter="/admin/manager/{{ app.request.get('entityId') }}/filter"
							 url-remove-filter="/admin/manager/{{ app.request.get('entityId') }}/filter"
							 url-selected-filters="/admin/manager/{{ app.request.get('entityId') }}/filters"
							 url-add-all="/admin/manager/{{ app.request.get('entityId') }}/filters"
							 url-remove-all="/admin/manager/{{ app.request.get('entityId') }}/filters/remove-all"
							filters-add-all="{{ 'filters.add_all' |trans({}, 'messages', app.user.locale)|raw }}"
                            filters-remove-all="{{ 'filters.remove_all' |trans({}, 'messages', app.user.locale)|raw }}"
                            filters-search="{{ 'filters.placeholder' |trans({}, 'messages', app.user.locale)|raw }}"

			></category-filter>
		</div>
	{% else %}
		{{ parent() }}
	{% endif %}

{% endblock main %}

{% block body_javascript %}
	{{ parent() }}
	{{ encore_entry_script_tags('user-impersonate') }} 
	{% if user_use_filters %}
		{{ encore_entry_script_tags('managerEdit') }}
	{% endif %}
{% endblock %}
