<div class="modal fade" id="editContent{{ contentId }}" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="editContent{{ contentId }}Label" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-xl">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="editContent{{ contentId }}Label">{{ 'common_areas.edit'|trans({}, 'messages', app.user.locale) }} {{ 'content.label_in_singular'|trans({}, 'messages', app.user.locale) }}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        {% set route = '/admin/edit-content/' ~ contentId ~ '/chapter/' ~ chapter.id %}
        {{ form_start(form, { action: route, method: 'POST' }) }}
        {{ form_widget(form) }}
        <div class="text-center mt-4">
          <button class="btn btn-primary">{{ button_label|default('component_video.button_save'|trans({}, 'messages', app.user.locale)) }}</button>
        </div>
        {{ form_end(form) }}
      </div>
    </div>
  </div>
</div>
