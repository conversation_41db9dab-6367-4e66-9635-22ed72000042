{% extends '@!EasyAdmin/crud/edit.html.twig' %}
{% set referer = app.request.query.get('referrer') %}
{% set default =  ea_url().unsetAll().setController('App\\Controller\\Admin\\CourseCrudController').setAction('detail').setEntityId(chapter.course.id) %}


{% block content_title %}
    {% if referer %}
        <a href="{{ referer }}" class="card-link">{{ chapter.course.name }}</a> > {{ chapter.title }}
    {% else %}
        <a href="{{ default }}" class="card-link">{{ chapter.course.name }}</a> > {{ chapter.title }}
    {% endif %}
{% endblock %}

{% block head_stylesheets %}
  {{ parent() }}
  {{ encore_entry_link_tags('chapterType') }}
  {{ encore_entry_link_tags('chapter_type_app') }}
{% endblock %}
{% block main %}
  {{ parent() }}

  {{ include('admin/chapter/alerts_chapter.html.twig') }}

  <div class="content_chapter_{{ chapter.type.id }}">
    {% if table_template %}
      {% include table_template %}
    {% endif %}
  </div>
  <div class="text-center" style="margin-top: 5rem">
    <button class="action-saveAndReturn btn btn-primary action-save btn-sm" type="submit" name="ea[newForm][btn]" value="saveAndReturn" data-action-name="saveAndReturn" form="edit-Chapter-form"><span class="btn-label">{{ 'common_areas.save'|trans({}, 'messages') }}</span></button>
  </div>
{% endblock %}

{% block body_javascript %}
  <script>
        let modalChapter =  {{ app.request.get('modal') | json_encode | raw }};
        let typeChapterId = {{ chapter.type.id | json_encode | raw }};
        console.log('modal-> ' + modalChapter);
        console.log('chpaterCode-> ' + typeChapterId);
    </script>

  {{ parent() }}

  {{ encore_entry_script_tags('chapterType') }}
  {{ encore_entry_script_tags('chapter_type_app') }}
{% endblock %}
