<div class="modal fade" id="createSeason" tabindex="-1" aria-labelledby="createSeasonLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title px-2" id="createSeasonLabel">
                    {{ 'course.configureFields.add_seasons'|trans({}, 'messages') }}</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="/admin/new-season/course/{{course.id}}" onsubmit="document.getElementById('createSeasonBtn').disabled=true;">
                    <div class="col-12">
                        <label for="name">
                            {{ 'course.configureFields.name'|trans({}, 'messages',  app.user.locale) }}
                        </label>
                        <input class="form-control" type="text" id="name" name="name" required><br>
                    </div>
                    <div class="col-12 mb-1">
                        <label for="type" class="form-label">
                            {{ 'chapter.configureFields.type'|trans({}, 'messages',  app.user.locale) }}
                        </label>
                        <select class="form-select" name="type" id="type">
                            {% for key, type in seasonTypes %}
                                <option value="{{ key }}">{{ type }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="my-3 pr-3 text-right">
                        <input type="submit" id="createSeasonBtn" class="btn btn-primary" value="{{ 'Save'|trans({}, 'messages') }}">
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
