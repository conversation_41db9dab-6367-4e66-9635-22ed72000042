<?php

declare(strict_types=1);

namespace App\Entity;

use App\Enum\Games;
use App\Repository\QuestionRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Validator\Constraints\Length;
use Vich\UploaderBundle\Mapping\Annotation as Vich;

/**
 * @ORM\HasLifecycleCallbacks()
 *
 * @Gedmo\SoftDeleteable(fieldName="deletedAt", timeAware=false, hardDelete=false)
 *
 * @ORM\Entity(repositoryClass=QuestionRepository::class)
 *
 * @Vich\Uploadable()
 */
class Question
{
    use AtAndBy;
    use Imageable;

    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     *
     */
    private $id;

    /**
     * @ORM\Column(type="text")
     *
     */
    private $question;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     *
     */
    private $image;

    /**
     * @Vich\UploadableField(mapping="question_image", fileNameProperty="image")
     */
    private $imageFile;

    /**
     * @var string
     *
     */
    private $imageUrl;

    /**
     * @ORM\ManyToOne(targetEntity=Chapter::class, inversedBy="questions")
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private $chapter;

    /**
     * @ORM\OneToMany(targetEntity=Answer::class, mappedBy="question", orphanRemoval=true, cascade={"persist"})
     *
     */
    private $answers;

    /**
     * @ORM\Column(type="integer", nullable=true)
     *
     */
    private $random;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     */
    private $isFeedback;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $feedbackPositive;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $feedbackNegative;

    /**
     * @ORM\Column(type="decimal", precision=10, scale=0, nullable=true)
     */
    private $time;

    public function __construct()
    {
        $this->answers = new ArrayCollection();
    }

    public function __toString()
    {
        return $this->question;
    }

    public function __clone()
    {
        $this->id = null;

        // clone image
        if ($this->getUploadsFolder()) {
            $this->cloneImage();
        }

        // clone answers
        $criteria = Criteria::create()->orderBy(['id' => Criteria::DESC]);
        $answers = $this->getAnswers()->matching($criteria);
        $this->answers = new ArrayCollection();
        if (\count($answers)) {
            foreach ($answers as $answer) {
                $clonedAnswer = clone $answer;
                $this->addAnswer($clonedAnswer);
            }
        }
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getQuestion(): ?string
    {
        return $this->question;
    }

    public function setQuestion(string $question): self
    {
        $this->question = $question;

        return $this;
    }

    public function getImageUrl(): ?string
    {
        return $this->imageUrl;
    }

    public function setImageUrl(?string $imageUrl): self
    {
        $this->imageUrl = $imageUrl;

        return $this;
    }

    public function getChapter(): ?Chapter
    {
        return $this->chapter;
    }

    public function setChapter(?Chapter $chapter): self
    {
        $this->chapter = $chapter;

        return $this;
    }

    /**
     * @return Collection|Answer[]
     */
    public function getAnswers(): Collection
    {
        return $this->answers;
    }

    public function addAnswer(Answer $answer): self
    {
        if (!$this->answers->contains($answer)) {
            $this->answers[] = $answer;
            $answer->setQuestion($this);
        }

        return $this;
    }

    public function removeAnswer(Answer $answer): self
    {
        if ($this->answers->contains($answer)) {
            $this->answers->removeElement($answer);
            // set the owning side to null (unless already changed)
            if ($answer->getQuestion() === $this) {
                $answer->setQuestion(null);
            }
        }

        return $this;
    }

    /**
     * @Groups({"questions"})
     */
    public function getTime(): ?int
    {
        return $this->getChapter()->getType()->getQuestionTime();
    }

    /**
     * @Groups({"questions"})
     */
    public function getQuestionTime(): ?int
    {
        return  $this->time ? (int) $this->time : (int) $this->getChapter()->getType()->getQuestionTime();
    }

    public function getValidateQuestion()
    {
        $state = false;
        if ($this->getId() && count($this->getAnswers()) >= 2 && Games::SECRET_WORD_TYPE != $this->getChapter()->getType()->getId()) {
            foreach ($this->getAnswers() as $answers) {
                if ($answers->getCorrect()) {
                    $state = true;
                    break;
                }
            }
        } elseif ($this->getId() && Games::SECRET_WORD_MINIMUM_QUESTIONS == count($this->getAnswers()) && Games::SECRET_WORD_TYPE == $this->getChapter()->getType()->getId()) {
            foreach ($this->getAnswers() as $clave => $answers) {
                if ($answers->getCorrect() && 0 == $clave) {
                    $state = true;
                    break;
                }
            }
        }

        return $state;
    }

    public function getRandom(): ?bool
    {
        return !\is_null($this->random) && $this->random > 0;
    }

    public function setRandom(?bool $random): self
    {
        $this->random = ($random) ? 1 : 0;

        return $this;
    }

    public function isIsFeedback(): ?bool
    {
        return $this->isFeedback;
    }

    public function setIsFeedback(?bool $isFeedback): self
    {
        $this->isFeedback = $isFeedback;

        return $this;
    }

    public function getFeedbackPositive(): ?string
    {
        return $this->feedbackPositive;
    }

    public function setFeedbackPositive(?string $feedbackPositive): self
    {
        $this->feedbackPositive = $feedbackPositive;

        return $this;
    }

    public function getFeedbackNegative(): ?string
    {
        return $this->feedbackNegative;
    }

    public function setFeedbackNegative(?string $feedbackNegative): self
    {
        $this->feedbackNegative = $feedbackNegative;

        return $this;
    }

    /**
     * @Groups({"questions"})
     *
     * @return array
     */
    public function getFeedback()
    {
        $feedback = [];
        $feedback['active'] = $this->isIsFeedback() ?? false;
        $feedback['correct'] = $this->getFeedbackPositive() ?? '';
        $feedback['incorrect'] = $this->getFeedbackNegative() ?? '';

        return $feedback;
    }

    public function setTime(?string $time): self
    {
        $this->time = $time;

        return $this;
    }
}
