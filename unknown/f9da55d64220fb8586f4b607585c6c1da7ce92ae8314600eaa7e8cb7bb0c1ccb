<?php

namespace App\Controller\Admin;

use App\Entity\QuestionsAnnouncement;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTManager;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class QuestionsAnnouncementCrudController extends AbstractCrudController
{
	private $settings;
	private $em;
	private $context;
	private $logger;
	private $jwt;
	protected $translator;
	private  $router;
	private $requestStack;


	public function __construct(SettingsService $settings, EntityManagerInterface $em, RouterInterface $router, RequestStack $requestStack, AdminContextProvider $context, LoggerInterface $logger, JWTManager $jwt, TranslatorInterface $translator)
	{
		$this->settings = $settings;
		$this->em = $em;
		$this->context = $context;
		$this->logger = $logger;
		$this->jwt = $jwt;
		$this->translator = $translator;
		$this->router = $router;
		$this->requestStack = $requestStack;
	}
    public static function getEntityFqcn(): string
    {
        return QuestionsAnnouncement::class;
    }
	public function configureCrud(Crud $crud): Crud
	{
		return $crud
			->setEntityLabelInSingular($this->translator->trans('questions_announcemnt.label_in_singular', [], 'messages', $this->getUser()->getLocale()))
			->setEntityLabelInPlural($this->translator->trans('questions_announcemnt.label_in_plural', [], 'messages', $this->getUser()->getLocale()))
			->overrideTemplate('crud/index', 'admin/questions-announcement/index.html.twig')
			;
	}


	/*
	public function configureFields(string $pageName): iterable
	{
		return [
			IdField::new('id'),
			TextField::new('title'),
			TextEditorField::new('description'),
		];
	}
	*/

	public function configureActions(Actions $actions): Actions {
		$actions->remove(Crud::PAGE_INDEX, Action::NEW);

		return  $actions;
	}
}
