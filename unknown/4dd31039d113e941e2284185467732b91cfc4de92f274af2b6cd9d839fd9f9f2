<?php

declare(strict_types=1);

namespace App\Controller\Apiv1\lti;

use App\Controller\Apiv1\ApiBaseController;
use App\Entity\User;
use OAT\Library\Lti1p3Core\Registration\RegistrationInterface;
use OAT\Library\Lti1p3Core\Security\User\Result\UserAuthenticationResult;
use OAT\Library\Lti1p3Core\Security\User\Result\UserAuthenticationResultInterface;
use OAT\Library\Lti1p3Core\User\UserIdentity;

class AuthenticateLtiController extends ApiBaseController implements UserAuthenticatorInterface
{
    // use to test LTI course from admin
    private $testName = 'Soporte Gestionet';
    private $testEmail = '<EMAIL>';
    private $testLocale = 'es';
    private $isTest = false;

    public function authenticateV2(RegistrationInterface $registration, string $loginHint, $userId): UserAuthenticationResultInterface
    {
        $user = $this->entityManager->getRepository(User::class)->find($userId);

        if (str_contains($_SERVER['REQUEST_URI'], 'admin/') || (int)$userId === 0) {
            $this->isTest = true;
        }

        if (null === $user && $this->isTest === false) {
            throw new \ErrorException('User not exists -> authenticateV2');
        }

        return new UserAuthenticationResult(
            true,
            new UserIdentity(
                \strval(($this->isTest) ? rand(5, 50000) : $user->getId()),
                ($this->isTest) ? $this->testName : $user->getFirstName() . ' ' . $user->getLastName(),
                ($this->isTest) ? $this->testEmail : $user->getEmail(),
                null,
                null,
                null,
                ($this->isTest) ? $this->testLocale : $user->getLocale()
            )
        );
    }
}
