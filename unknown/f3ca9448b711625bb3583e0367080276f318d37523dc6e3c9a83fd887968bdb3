<?php

namespace App\Entity;

use App\Repository\QuestionsAnnouncementRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=QuestionsAnnouncementRepository::class)
 */
class QuestionsAnnouncement
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $section;

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    private $section_json;

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    private $questions_json;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getSection(): ?string
    {
        return $this->section;
    }

    public function setSection(string $section): self
    {
        $this->section = $section;

        return $this;
    }

    public function getSectionJson(): ?string
    {
        return $this->section_json;
    }

    public function setSectionJson(?string $section_json): self
    {
        $this->section_json = $section_json;

        return $this;
    }

    public function getQuestionsJson(): ?string
    {
        return $this->questions_json;
    }

    public function setQuestionsJson(?string $questions_json): self
    {
        $this->questions_json = $questions_json;

        return $this;
    }
}
