<?php

namespace App\Service\Vimeo;

use App\Service\SettingsService;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Response;
use Vimeo\Vimeo;

class VimeoService
{
    private SettingsService $settings;
    private Vimeo $client;
    public function __construct(SettingsService $settings)
    {
        $this->settings = $settings;

        $this->client = new Vimeo(
            $settings->get('app.clientIdVimeo'),
            $settings->get('app.clientSecretVimeo'),
            $settings->get('app.accessTokenVimeo')
        );
    }

    public function upload(UploadedFile $uploadedFile, $idFolderVimeo = ''): array
    {
        try {
            if (strpos($uploadedFile->getMimeType(), 'video') === FALSE) {

                return [
                    'status' => Response::HTTP_BAD_REQUEST,
                    'message' => 'Wrong file mimetype'
                ];
            }

            $vimeoUrl = $this->client->upload($uploadedFile, [
                'name' => $uploadedFile->getClientOriginalName(),
                'privacy' => [
                    'embed' => 'whitelist'
                ]
            ]);

            $videoId = substr($vimeoUrl, 8); // Ge the videoID
            $link = $this->client->request($vimeoUrl . '?fields=link');

            $userId = $this->settings->get('app.userIdVimeo');
            $projectId = $idFolderVimeo != '' ? $idFolderVimeo : $this->settings->get('app.projectIdVimeo');

            $this->client->request("/users/$userId/projects/$projectId/videos/$videoId", [], 'PUT');

            return [
                'status' => Response::HTTP_OK,
                'videoId' => $videoId,
                'link' => $link['body']['link']
            ];
        } catch (\Exception $e) {
            return [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'message' => $e->getMessage()
            ];
        }
    }

    public function deleteFromVimeo($videoIdentifier, $idFolderVimeo = '')
    {
        try {
            $userId = $this->settings->get('app.userIdVimeo');
            $projectId = $idFolderVimeo != '' ? $idFolderVimeo : $this->settings->get('app.projectIdVimeo');
            $this->client->request("/users/$userId/projects/$projectId/videos/$videoIdentifier", [], 'DELETE');
            return true;
        } catch (\Exception $e) {
            return new Response(
                $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }
}
