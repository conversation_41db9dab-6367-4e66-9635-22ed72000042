<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\ChatChannel;
use App\Entity\ChatMessage;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ChatMessage>
 *
 * @method ChatMessage|null find($id, $lockMode = null, $lockVersion = null)
 * @method ChatMessage|null findOneBy(array $criteria, array $orderBy = null)
 * @method ChatMessage[]    findAll()
 * @method ChatMessage[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ChatMessageRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ChatMessage::class);
    }

    public function add(ChatMessage $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(ChatMessage $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function getMessages(
        ChatChannel $channel,
        ?User $user = null,
        ?int $newest = null,
        ?int $oldest = null,
        int $pageSize = 100,
        ?string $order = 'DESC'
    ): array {
        $query = $this->createQueryBuilder('cm')
            ->select('cm.id', 'cm.message', 'cm.seen', 'cm.createdAt')
            ->addSelect('u.id as userId', "concat(u.firstName, ' ', u.lastName) as name", 'u.avatar')
            ->addSelect('count(n_likes.id) as likes')
            ->join('cm.user', 'u')
            ->leftJoin('cm.chatMessageLikes', 'n_likes', Join::WITH, 'n_likes.active = 1')
            ->where('cm.channel =:channel')
            ->andWhere('cm.replyTo is null')
            ->setParameter('channel', $channel)
            ->orderBy('cm.createdAt', $order)
            ->addGroupBy('cm.id')
        ;

        if ($user) {
            $query->addSelect('COUNT(n_liked.id) as liked')
                ->addSelect('COUNT(n_reports.id) as reported')
                ->leftJoin('cm.chatMessageLikes', 'n_liked', Join::WITH, 'n_liked.user = :user AND n_liked.active = 1')
                ->leftJoin('cm.chatMessageReports', 'n_reports', Join::WITH, 'n_reports.user = :user AND n_reports.active = 1')
                ->setParameter('user', $user)
            ;
        } else {
            $query->addSelect('0 AS liked', '0 AS reported');
        }

        $replyQuery = $this->createQueryBuilder('cm')
            ->select('rt.id')
            ->join('cm.replyTo', 'rt')
            ->andWhere('cm.replyTo is not null')
            ->groupBy('rt.id')
        ;

        if (null === $newest && null === $oldest) {
            if ($pageSize > 0) {
                $query->setMaxResults($pageSize);
                $replyQuery->setMaxResults($pageSize);
            }
        } elseif (null !== $newest && $newest > 0) {
            $query->andWhere('cm.id > :newest')->setParameter('newest', $newest);
            $replyQuery->andWhere('cm.id > :newest')->setParameter('newest', $newest);
        } else {
            $query->andWhere('cm.id < :oldest')
                ->setParameter('oldest', $oldest);
            $replyQuery->andWhere('cm.id < :oldest')
                ->setParameter('oldest', $oldest);

            if ($pageSize > 0) {
                $query->setMaxResults($pageSize);
                $replyQuery->setMaxResults($pageSize);
            }
        }

        $messages = $query->getQuery()->getResult();
        $ids = $replyQuery->getQuery()->getResult();

        $idsValues = [];
        foreach ($ids as $i) {
            $idsValues[] = $i['id'];
        }

        $data = [];
        foreach ($messages as $m) {
            $message = $m;
            $message['liked'] = $message['liked'] > 0;
            $message['reported'] = $message['reported'] > 0;
            $message['replies'] = [];
            if (\in_array($m['id'], $idsValues)) {
                $messageRepliesQuery = $this->createQueryBuilder('cm')
                    ->select('cm.id', 'cm.message', 'cm.seen', 'cm.createdAt')
                    ->addSelect('u.id as userId', "concat(u.firstName, ' ', u.lastName) as name", 'u.avatar')
                    ->addSelect('count(n_likes.id) as likes')
                    ->leftJoin('cm.chatMessageLikes', 'n_likes', Join::WITH, 'n_likes.active = 1')
                    ->join('cm.replyTo', 'rt')
                    ->join('cm.user', 'u')
                    ->where('cm.replyTo is not null AND rt.id =:id')
                    ->setParameter('id', $m['id'])
                    ->addGroupBy('cm.id')
                    ->addOrderBy('cm.createdAt', 'ASC')
                ;

                if ($user) {
                    $messageRepliesQuery->addSelect('COUNT(n_liked.id) as liked')
                        ->addSelect('COUNT(n_reports.id) as reported')
                        ->leftJoin('cm.chatMessageLikes', 'n_liked', Join::WITH, 'n_liked.user = :user AND n_liked.active = 1')
                        ->leftJoin('cm.chatMessageReports', 'n_reports', Join::WITH, 'n_reports.user = :user AND n_reports.active = 1')
                        ->setParameter('user', $user)
                    ;
                }

                $message['replies'] = $messageRepliesQuery->getQuery()
                    ->getResult();
            }
            $data[] = $message;
        }

        return $data;
    }

    /**
     * @return array|false FALSE when user is not in the channel. Only with direct channels
     */
    public function sendMessage(User $user, ChatChannel $channel, string $message, ?int $replyToId = null)
    {
        if (ChatChannel::TYPE_DIRECT === $channel->getType() && !$this->_em->getRepository(ChatChannel::class)->isUserInChannel($channel, $user)) {
            // Check if the user is part of the channel
            return false;
        }

        if (!$channel->isAvailable()) {
            return false;
        }

        $chatMessage = new ChatMessage();
        $chatMessage->setUser($user)
            ->setChannel($channel)
            ->setMessage($message)
        ;
        $replyTo = null;
        if ($replyToId && ($replyTo = $this->find($replyToId))) {
            $chatMessage->setReplyTo($replyTo);
        }

        $this->_em->persist($chatMessage);
        $this->_em->flush();

        return [
            'id' => $chatMessage->getId(),
            'avatar' => $user->getAvatar(),
            'createdAt' => $chatMessage->getCreatedAt(),
            'likes' => 0,
            'liked' => false,
            'name' => trim("{$user->getFirstName()} {$user->getLastName()}"),
            'message' => $chatMessage->getMessage(),
            'replies' => [],
            'userId' => $user->getId(),
            'seen' => $chatMessage->isSeen(),
            'replyTo' => $replyTo ? $replyTo->getId() : null,
            'reported' => false
        ];
    }
}
