<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\Categorize;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Categorize>
 *
 * @method Categorize|null find($id, $lockMode = null, $lockVersion = null)
 * @method Categorize|null findOneBy(array $criteria, array $orderBy = null)
 * @method Categorize[]    findAll()
 * @method Categorize[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CategorizeRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Categorize::class);
    }

    public function add(Categorize $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Categorize $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
