<?php

declare(strict_types=1);

namespace App\Security;

use App\Admin\Traits\BrowserAndDeviceLoginTrait;
use App\Entity\User;
use App\Entity\UserLogin;
use App\Entity\UserToken;
use App\Security\Integrations\Clients\LdapClient;
use App\Security\Integrations\Exceptions\IntegrationLdapException;
use App\Service\SettingsService;
use App\Service\User\Token\RefreshTokenService;
use App\Service\User\Token\TokenLoginService;
use Doctrine\ORM\EntityManagerInterface;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTManager;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Core\Exception\UserNotFoundException;
use Symfony\Component\Security\Http\Authenticator\AbstractAuthenticator;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Passport;
use Symfony\Component\Security\Http\Authenticator\Passport\SelfValidatingPassport;
use Symfony\Contracts\Translation\TranslatorInterface;

class ApiLoginAuthenticator extends AbstractAuthenticator
{
    use BrowserAndDeviceLoginTrait;
    use AuthenticationLogsTrait;
    public const REF_LDAP = 'ldap';

    public const API_LOGIN_ROUTE_NAME = 'api_user_manual_login';

    private EntityManagerInterface $em;
    private SettingsService $settings;
    protected TranslatorInterface $translator;
    private TokenLoginService $tokenLoginService;
    private UserPasswordHasherInterface $userPasswordHasher;
    private JWTManager $JWTManager;
    private RefreshTokenService $refreshTokenService;
    private LoggerInterface $logger;
    private ParameterBagInterface $params;
    private LdapClient $ldapClient;

    public function __construct(
        EntityManagerInterface $em,
        SettingsService $settings,
        TranslatorInterface $translator,
        TokenLoginService $tokenLoginService,
        UserPasswordHasherInterface $userPasswordHasher,
        JWTManager $JWTManager,
        RefreshTokenService $refreshTokenService,
        LoggerInterface $logger,
        ParameterBagInterface $params,
        LdapClient $ldapClient
    ) {
        $this->em = $em;
        $this->settings = $settings;
        $this->translator = $translator;
        $this->tokenLoginService = $tokenLoginService;
        $this->userPasswordHasher = $userPasswordHasher;
        $this->JWTManager = $JWTManager;
        $this->refreshTokenService = $refreshTokenService;
        $this->logger = $logger;
        $this->params = $params;
        $this->ldapClient = $ldapClient;
    }

    public function supports(Request $request): ?bool
    {
        return $request->isMethod('POST') && self::API_LOGIN_ROUTE_NAME === $request->attributes->get('_route');
    }

    /**
     * @return User|array
     *
     * @throws AuthenticationException Authentication failed
     */
    private function handleToken(string $token)
    {
        /** @var UserToken|null $userToken */
        $userToken = $this->em->getRepository(UserToken::class)->findOneBy(['token' => $token]);
        if (!$userToken && $this->settings->get('sso.saml')) {
            // No token found, and sso is active, try to log in user
            try {
                return $this->handleSsoSamlLoginProcedure($token);
            } catch (UserNotFoundException $e) {
                // let the request continue to check token against other services
            }
        }

        if (($userToken && UserToken::TYPE_SAML2_AUTH === $userToken->getType()) && $this->settings->get('saml.enabled')) {
            // token found and is type SAML2, try SAML2 login
            return $this->handleSaml2LoginProcedure($token);
        }

        // Try other type of login procedure
        return $this->handleTokenLoginService($token);
    }

    public function authenticate(Request $request): Passport
    {
        $content = json_decode($request->getContent(), true);
        $token = $content['token'] ?? null;
        $email = $content['email'] ?? null;
        $ref = $content['ref'] ?? null;
        if (empty($token) && empty($email)) {
            throw new AuthenticationException('No email or token has been provided');
        }

        if (!empty($token)) {
            $user = $this->handleToken($token);
        } else {
            // By default, the credentials is email and password
            $password = $content['password'] ?? null;
            if (empty($password)) {
                throw new AuthenticationException('No password has been provided');
            }

            if (!empty($ref) && self::REF_LDAP === $ref) {
                $user = $this->handleLdapLogin($email, $password);
            } else {
                $user = $this->handleNormalLoginProcedure($email, $password);
            }
        }

        $payload = [];
        if (\is_array($user)) {
            $payload = $user['payload'];
            $user = $user['user'];
        }

        $identifier = $user->getEmail(); // Modify to allow user code

        $passport = new SelfValidatingPassport(
            new UserBadge($identifier, function ($userIdentifier) {
                // if is email find by email
                $u = $this->em->getRepository(User::class)->findOneBy(['email' => $userIdentifier]);
                if ($u) {
                    return $u;
                }

                // Supports user code
                $u = $this->em->getRepository(User::class)->findOneBy(['code' => $userIdentifier]);
                if (!$u) {
                    throw new UserNotFoundException();
                }

                return $u;
            })
        );

        if (!empty($payload)) {
            $jwtToken = $this->JWTManager->createFromPayload($user, $payload);
        } else {
            $jwtToken = $this->JWTManager->create($user);
        }
        $refreshToken = $this->refreshTokenService->createForUserWithTtl($user, 7200, $payload);
        $this->em->persist($refreshToken);
        $this->em->flush();

        // Save login
        try {
            $this->saveUserLogin($user, $content);
        } catch (\Exception $e) {
            $this->logger->error('Failed to save user login: ' . $e->getMessage(), ['file' => $e->getFile(), 'line' => $e->getLine()]);
        }

        $passport->setAttribute('token', $jwtToken);
        $passport->setAttribute('refresh_token', $refreshToken);

        return $passport;
    }

    public function createToken(Passport $passport, string $firewallName): TokenInterface
    {
        $postAuthenticationToken = parent::createToken($passport, $firewallName); // TODO: Change the autogenerated stub
        $postAuthenticationToken->setAttribute('token', $passport->getAttribute('token'));
        $postAuthenticationToken->setAttribute('refresh_token', $passport->getAttribute('refresh_token'));

        return $postAuthenticationToken;
    }

    public function onAuthenticationSuccess(Request $request, TokenInterface $token, string $firewallName): ?Response
    {
        $content = json_decode($request->getContent(), true);
        $email = $content['email'] ?? null;
        if (!empty($email)) {
            $this->generateLog($request, $email, 'SUCCESS', $this->params->get('app.authentication_logs'));
        }

        return null; // let the request continue
    }

    public function onAuthenticationFailure(Request $request, AuthenticationException $exception): ?Response
    {
        $content = json_decode($request->getContent(), true);
        $email = $content['email'] ?? null;
        if (!empty($email)) {
            $this->generateLog($request, $email, 'DENIED', $this->params->get('app.authentication_logs'));
        }

        return new JsonResponse(
            [
                'error' => true,
                'data' => $exception->getMessage(),
            ],
            Response::HTTP_UNAUTHORIZED
        );
    }

    /**
     * Authentication using saml.
     *
     * @throws AuthenticationException
     */
    private function handleSaml2LoginProcedure(string $token): User
    {
        $userToken = $this->em->getRepository(UserToken::class)->findOneBy([
            'token' => $token,
            'type' => UserToken::TYPE_SAML2_AUTH,
        ]);
        if (!$userToken || !$userToken->getIsValid()) {
            throw new AuthenticationException(
                $this->translator->trans('message_api.base.authorized_token_fail', [], 'message_api', $this->settings->get('app.defaultLanguage'))
            );
        }

        $user = $userToken->getUser();

        $userToken->setUsed(true)
            ->setUsedAt(new \DateTimeImmutable());
        $this->em->flush($userToken);

        return $user;
    }

    /**
     * Backward compatibility with previous implementation of Saml Login.
     *
     * @throws UserNotFoundException   When the token is not used in sso format
     * @throws AuthenticationException When the token is valid for an existing user but authentication fails
     *
     * @deprecated  Migrate to Saml2Service
     * @see Saml2Service
     */
    private function handleSsoSamlLoginProcedure(string $token): User
    {
        $token_data = explode('_', $token, 2);

        $user = $this->em->getRepository(User::ROLE_USER)->find($token_data[0]);
        if (!$user) {
            throw new UserNotFoundException();
        }

        if ($user->getAutologinHash() != $token_data[1]) {
            // Failed to Log In
            throw new AuthenticationException(
                $this->translator->trans(
                    'message_api.base.authorized_access',
                    [],
                    'message_api',
                    $user->getLocale() ?? $this->settings->get('app.defaultLanguage')
                )
            );
        }

        return $user;
    }

    /**
     * @return User|array
     *
     * @throws AuthenticationException
     */
    private function handleTokenLoginService(string $token)
    {
        $user = $this->tokenLoginService->checkToken($token);
        if (!$user) {
            throw new AuthenticationException(
                $this->translator->trans('message_api.base.authorized_token_fail', [], 'message_api', $this->settings->get('app.defaultLanguage'))
            );
        }

        return $user;
    }

    /**
     * Handle authentication related to ldap|ldaps.
     *
     * @throws AuthenticationException
     */
    private function handleLdapLogin($uid, $password): User
    {
        try {
            if (!$this->ldapClient->isEnabled()) {
                throw new AuthenticationException(
                    'LDAP services not enabled'
                );
            }
            $user = $this->ldapClient->login($uid, $password);
        } catch (IntegrationLdapException $e) {
            throw new AuthenticationException(
                $this->translator->trans(
                    'message_api.base.user_not_exists_in_ad',
                    [],
                    'message_api',
                    $this->settings->get('app.defaultLanguage')
                ) . ':' . $e->getMessage(),
                $e->getCode(),
                $e
            );
        }

        return $user;
    }

    /**
     * Authentication against user stored in database.
     *
     * @throws AuthenticationException
     */
    private function handleNormalLoginProcedure(string $email, string $password): User
    {
        $user = $this->em->getRepository(User::class)->findOneBy(['email' => $email]);
        if (!$user || !$user->getIsActive() || !$user->getValidated() || !$this->userPasswordHasher->isPasswordValid($user, $password)) {
            // If one of the conditions fail, not authorized

            throw new AuthenticationException(
                $this->translator->trans(
                    'message_api.base.credentials_no_correct',
                    [],
                    'message_api',
                    $user ? $user->getLocale() : $this->settings->get('app.defaultLanguage')
                )
            );
        }

        /*
         * Check if API Login validations are enabled
         */
        if (true === $this->settings->get('app.api_login.enabled')) {
            // Call extra steps after login to validate if user is still active
        }

        return $user;
    }

    /**
     * @throws \Exception
     */
    private function saveUserLogin(User $user, $data = null)
    {
        $userLoginRepository = $this->em->getRepository(UserLogin::class);
        $count = $userLoginRepository->countByUserFromDate($user, new \DateTime('-2 hour'));
        if (!$count) {
            $timezone = $data['timezone'] ?? '';
            if (empty($timezone)) {
                $timezone = $this->settings->get('app.default_timezone');
            }
            $tz = new \DateTimeZone($timezone);
            $country = $tz->getLocation()['country_code'];

            $now = new \DateTime('now', $tz);

            $userLogin = new UserLogin();
            $userLogin->setUser($user)
                ->setCreatedAt(new \DateTime())
                ->setDevice($this->detectDevice())
                ->setBrowser($this->detectBrowser())
                ->setPlatform($this->detectPlatform())
                ->setTimezone($timezone)
                ->setCountry($country)
                ->setTimezoneCreatedAt($now)
                ->setTimezoneUpdatedAt($now);

            $this->em->persist($userLogin);
            $this->em->flush();

            return $userLogin;
        }

        return false;
    }
}
